package com.subskribe.billy.subscription.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.notification.service.TestNotificationService;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.db.SubscriptionStatusChangeScheduleDAO;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionStatusChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionStatusChangeSchedule;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.TransactionalRunnable;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

@SuppressWarnings({ "unchecked" })
class SubscriptionStatusScheduleServiceTest {

    private static final String TENANT_ID = "test-tenant";
    private static final String ENTITY_ID = "test-entity";
    private static final String SUBSCRIPTION_ID = "test-subscription";
    private static final String ACCOUNT_ID = "test-account";
    private static final Duration FUTURE_EVENT_BEFORE_DURATION = Duration.ofHours(1);

    @Mock
    private SubscriptionGetService subscriptionGetService;

    @Mock
    private SubscriptionStatusChangeScheduleDAO subscriptionStatusChangeScheduleDAO;

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private EventPublishingService eventPublishingService;

    @Mock
    private DSLContext dslContext;

    @Mock
    private SubscriptionEventService subscriptionEventService;

    @Mock
    private TestNotificationService testNotificationService;

    @Mock
    private FeatureService featureService;

    @Mock
    private TaskDispatcher taskDispatcher;

    private Configuration configuration;
    private Clock fixedClock;
    private SubscriptionLifecycleScheduleService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        configuration = new DefaultConfiguration();
        fixedClock = Clock.fixed(Instant.parse("2023-01-01T10:00:00Z"), ZoneId.systemDefault());

        when(featureService.isEnabled(Feature.USE_TASKS_FOR_SUBSCRIPTION_CHANGES)).thenReturn(false);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        doAnswer(invocation -> {
            TransactionalRunnable runnable = invocation.getArgument(0);
            runnable.run(configuration);
            return null;
        })
            .when(dslContext)
            .transaction(any(TransactionalRunnable.class));

        service = new SubscriptionLifecycleScheduleService(
            subscriptionGetService,
            null,
            subscriptionStatusChangeScheduleDAO,
            dslContextProvider,
            tenantIdProvider,
            eventPublishingService,
            fixedClock,
            subscriptionEventService,
            testNotificationService,
            taskDispatcher,
            featureService
        );
    }

    @Test
    void scheduleStatusChangesWithFutureStartDateShouldCreateActivatingAndActivatedEvents() {
        Instant now = fixedClock.instant();
        Instant futureStart = now.plusSeconds(3600);
        Instant futureEnd = futureStart.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, futureStart, futureEnd, null);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        // 4 because it creates both activating and activated events, plus expiring and expired if applicable
        assertThat(capturedSchedules).hasSize(4);

        SubscriptionStatusChangeSchedule activatingSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_ACTIVATING);
        SubscriptionStatusChangeSchedule activatedSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_ACTIVATED);

        assertThat(activatingSchedule).isEqualTo(
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(futureStart.minus(FUTURE_EVENT_BEFORE_DURATION))
                .changeEventType(EventType.SUBSCRIPTION_ACTIVATING)
                .isProcessed(false)
                .isDeleted(false)
                .build()
        );

        assertThat(activatedSchedule).isEqualTo(
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(futureStart)
                .changeEventType(EventType.SUBSCRIPTION_ACTIVATED)
                .isProcessed(false)
                .isDeleted(false)
                .build()
        );

        verify(subscriptionStatusChangeScheduleDAO).deleteUnprocessedSchedulesForOtherVersions(eq(SUBSCRIPTION_ID), eq(1), any());
    }

    @Test
    void scheduleStatusChangesWithPastStartDateShouldCreateActivatedEvent() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(3600);
        Instant futureEnd = now.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, pastStart, futureEnd, null);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(3);

        SubscriptionStatusChangeSchedule activatedSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_ACTIVATED);
        assertThat(activatedSchedule).isEqualTo(
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(pastStart)
                .changeEventType(EventType.SUBSCRIPTION_ACTIVATED)
                .isProcessed(false)
                .isDeleted(false)
                .build()
        );
    }

    @Test
    void scheduleStatusChangesWithPastStartDateAndProcessedActivatedShouldNotCreateActivatedEvent() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(3600);
        Instant futureEnd = now.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, pastStart, futureEnd, null);

        List<SubscriptionStatusChangeSchedule> processedEvents = List.of(
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(pastStart)
                .changeEventType(EventType.SUBSCRIPTION_ACTIVATED)
                .isProcessed(true)
                .isDeleted(false)
                .build()
        );

        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(processedEvents);

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        // Should only create expiring and expired events since activated was already processed
        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(2);

        // Verify we don't have any ACTIVATED events in the list
        assertThat(capturedSchedules.stream().noneMatch(s -> s.getChangeEventType() == EventType.SUBSCRIPTION_ACTIVATED)).isTrue();
    }

    @Test
    void scheduleStatusChangesWithFutureCancelDateShouldCreateCancellingAndCancelledEvents() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(3600);
        Instant futureCancel = now.plusSeconds(7200);
        Instant futureEnd = now.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, pastStart, futureEnd, futureCancel);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(3); // ACTIVATED, CANCELLING, CANCELLED

        SubscriptionStatusChangeSchedule activatedSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_ACTIVATED);
        SubscriptionStatusChangeSchedule cancellingSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_CANCELLING);
        SubscriptionStatusChangeSchedule cancelledSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_CANCELLED);

        assertThat(activatedSchedule).isNotNull();
        assertThat(activatedSchedule.getChangesOn()).isEqualTo(pastStart);

        assertThat(cancellingSchedule).isNotNull();
        assertThat(cancellingSchedule.getChangesOn()).isEqualTo(futureCancel.minus(FUTURE_EVENT_BEFORE_DURATION));

        assertThat(cancelledSchedule).isNotNull();
        assertThat(cancelledSchedule.getChangesOn()).isEqualTo(futureCancel);
    }

    @Test
    void scheduleStatusChangesWithPastCancelDateShouldCreateCancelledEvent() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(7200);
        Instant pastCancel = now.minusSeconds(3600);
        Instant futureEnd = now.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, pastStart, futureEnd, pastCancel);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(2); // ACTIVATED, CANCELLED

        SubscriptionStatusChangeSchedule activatedSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_ACTIVATED);
        SubscriptionStatusChangeSchedule cancelledSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_CANCELLED);

        assertThat(activatedSchedule).isNotNull();
        assertThat(activatedSchedule.getChangesOn()).isEqualTo(pastStart);

        assertThat(cancelledSchedule).isNotNull();
        assertThat(cancelledSchedule.getChangesOn()).isEqualTo(pastCancel);
    }

    @Test
    void scheduleStatusChangesWithPastCancelDateAndProcessedCancelledShouldNotCreateCancelledEvent() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(7200);
        Instant pastCancel = now.minusSeconds(3600);
        Instant futureEnd = now.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, pastStart, futureEnd, pastCancel);

        // SUBSCRIPTION_CANCELLED was already processed
        List<SubscriptionStatusChangeSchedule> processedEvents = List.of(
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(pastCancel)
                .changeEventType(EventType.SUBSCRIPTION_CANCELLED)
                .isProcessed(true)
                .isDeleted(false)
                .build()
        );

        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(processedEvents);

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(1); // Only ACTIVATED

        // Verify we don't have any CANCELLED events in the list
        assertThat(capturedSchedules.stream().noneMatch(s -> s.getChangeEventType() == EventType.SUBSCRIPTION_CANCELLED)).isTrue();
    }

    @Test
    void scheduleStatusChangesWithFutureEndDateWithoutCancelShouldCreateExpiringAndExpiredEvents() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(3600);
        Instant futureEnd = now.plusSeconds(7200);

        Subscription subscription = createSubscription(1, pastStart, futureEnd, null);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(3); // ACTIVATED, EXPIRING, EXPIRED

        SubscriptionStatusChangeSchedule expiringSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_EXPIRING);
        SubscriptionStatusChangeSchedule expiredSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_EXPIRED);

        assertThat(expiringSchedule).isNotNull();
        assertThat(expiringSchedule.getChangesOn()).isEqualTo(futureEnd.minus(FUTURE_EVENT_BEFORE_DURATION));

        assertThat(expiredSchedule).isNotNull();
        assertThat(expiredSchedule.getChangesOn()).isEqualTo(futureEnd);
    }

    @Test
    void scheduleStatusChangesWithPastEndDateWithoutCancelShouldCreateExpiredEvent() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(7200);
        Instant pastEnd = now.minusSeconds(3600);

        Subscription subscription = createSubscription(1, pastStart, pastEnd, null);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(2); // ACTIVATED, EXPIRED

        SubscriptionStatusChangeSchedule activatedSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_ACTIVATED);
        SubscriptionStatusChangeSchedule expiredSchedule = findEventByType(capturedSchedules, EventType.SUBSCRIPTION_EXPIRED);

        // Verify ACTIVATED event
        assertThat(activatedSchedule).isNotNull();
        assertThat(activatedSchedule.getChangesOn()).isEqualTo(pastStart);

        // Verify EXPIRED event
        assertThat(expiredSchedule).isNotNull();
        assertThat(expiredSchedule.getChangesOn()).isEqualTo(pastEnd);
    }

    @Test
    void scheduleStatusChangesWithPastEndDateAndProcessedExpiredShouldNotCreateExpiredEvent() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(7200);
        Instant pastEnd = now.minusSeconds(3600);

        Subscription subscription = createSubscription(1, pastStart, pastEnd, null);

        // SUBSCRIPTION_EXPIRED was already processed
        List<SubscriptionStatusChangeSchedule> processedEvents = List.of(
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(pastEnd)
                .changeEventType(EventType.SUBSCRIPTION_EXPIRED)
                .isProcessed(true)
                .isDeleted(false)
                .build()
        );

        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(processedEvents);

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(1); // Only ACTIVATED

        // Verify we don't have any EXPIRED events in the list
        assertThat(capturedSchedules.stream().noneMatch(s -> s.getChangeEventType() == EventType.SUBSCRIPTION_EXPIRED)).isTrue();
    }

    @Test
    void scheduleStatusChangesWithCompletelyProcessedEventsShouldCreateNoNewEvents() {
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(7200);
        Instant pastEnd = now.minusSeconds(3600);

        Subscription subscription = createSubscription(1, pastStart, pastEnd, null);

        // Mock that both SUBSCRIPTION_ACTIVATED and SUBSCRIPTION_EXPIRED were already processed
        List<SubscriptionStatusChangeSchedule> processedEvents = List.of(
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(pastStart)
                .changeEventType(EventType.SUBSCRIPTION_ACTIVATED)
                .isProcessed(true)
                .isDeleted(false)
                .build(),
            ImmutableSubscriptionStatusChangeSchedule.builder()
                .tenantId(TENANT_ID)
                .subscriptionId(SUBSCRIPTION_ID)
                .subscriptionVersion(1)
                .changesOn(pastEnd)
                .changeEventType(EventType.SUBSCRIPTION_EXPIRED)
                .isProcessed(true)
                .isDeleted(false)
                .build()
        );

        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(processedEvents);

        service.scheduleStatusChanges(subscription);

        ArgumentCaptor<List<SubscriptionStatusChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionStatusChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).isEmpty(); // No new events
    }

    @Test
    void scheduleStatusChangesDeletesUnprocessedOlderSchedulesAndCreatesNewSchedules() {
        Instant now = fixedClock.instant();
        Instant futureStart = now.plusSeconds(3600);
        Instant futureEnd = futureStart.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(2, futureStart, futureEnd, null);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        verify(subscriptionStatusChangeScheduleDAO).deleteUnprocessedSchedulesForOtherVersions(eq(SUBSCRIPTION_ID), eq(2), any());
        verify(subscriptionStatusChangeScheduleDAO).createSchedules(anyList(), any());
    }

    private Subscription createSubscription(int version, Instant startDate, Instant endDate, Instant canceledDate) {
        Subscription subscription = mock(Subscription.class);
        when(subscription.getSubscriptionId()).thenReturn(SUBSCRIPTION_ID);
        when(subscription.getVersion()).thenReturn(version);
        when(subscription.getTenantId()).thenReturn(TENANT_ID);
        when(subscription.getStartDate()).thenReturn(startDate);
        when(subscription.getEndDate()).thenReturn(endDate);
        when(subscription.getCanceledDate()).thenReturn(canceledDate);
        when(subscription.getCreationTime()).thenReturn(startDate);
        when(subscription.getAccountId()).thenReturn(ACCOUNT_ID);
        when(subscription.getEntityId()).thenReturn(ENTITY_ID);
        when(subscription.getCharges()).thenReturn(new ArrayList<>());
        return subscription;
    }

    private SubscriptionStatusChangeSchedule findEventByType(List<SubscriptionStatusChangeSchedule> schedules, EventType eventType) {
        return schedules.stream().filter(s -> s.getChangeEventType() == eventType).findFirst().orElse(null);
    }

    @Test
    void scheduleStatusChangesWhenFeatureFlagEnabledShouldScheduleTasks() {
        when(featureService.isEnabled(Feature.USE_TASKS_FOR_SUBSCRIPTION_CHANGES)).thenReturn(true);

        Instant now = fixedClock.instant();
        Instant futureStart = now.plusSeconds(3600);
        Instant futureEnd = futureStart.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, futureStart, futureEnd, null);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        List<SubscriptionStatusChangeSchedule> returnedSchedules = new ArrayList<>();
        when(subscriptionStatusChangeScheduleDAO.createSchedules(anyList(), any())).thenAnswer(invocation -> {
            List<SubscriptionStatusChangeSchedule> schedules = invocation.getArgument(0);
            return schedules
                .stream()
                .map(s -> ImmutableSubscriptionStatusChangeSchedule.builder().from(s).id(UUID.randomUUID()).build())
                .peek(returnedSchedules::add)
                .toList();
        });

        service.scheduleStatusChanges(subscription);

        // Verify task scheduling
        ArgumentCaptor<List<QueuedTaskRequest>> tasksCaptor = ArgumentCaptor.forClass(List.class);
        verify(taskDispatcher).scheduleTasks(tasksCaptor.capture(), any());

        List<QueuedTaskRequest> scheduledTasks = tasksCaptor.getValue();
        assertThat(scheduledTasks).hasSize(4);
        returnedSchedules.forEach(s ->
            Assertions.assertTrue(scheduledTasks.stream().anyMatch(task -> task.getTaskData().equals(Objects.requireNonNull(s.getId()).toString())))
        );
        assertThat(scheduledTasks.stream().map(t -> t.getDelayedUntil().get())).containsExactlyInAnyOrder(
            subscription.getStartDate(),
            subscription.getEndDate(),
            subscription.getStartDate().minus(FUTURE_EVENT_BEFORE_DURATION),
            subscription.getEndDate().minus(FUTURE_EVENT_BEFORE_DURATION)
        );
    }

    @Test
    void scheduleStatusChangesWhenFeatureFlagDisabledShouldNotScheduleTasks() {
        Instant now = fixedClock.instant();
        Instant futureStart = now.plusSeconds(3600);
        Instant futureEnd = futureStart.plus(Duration.ofDays(365));

        Subscription subscription = createSubscription(1, futureStart, futureEnd, null);
        when(subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(SUBSCRIPTION_ID)).thenReturn(Collections.emptyList());

        service.scheduleStatusChanges(subscription);

        verify(taskDispatcher, never()).scheduleTasks(anyList(), any());
    }
}
