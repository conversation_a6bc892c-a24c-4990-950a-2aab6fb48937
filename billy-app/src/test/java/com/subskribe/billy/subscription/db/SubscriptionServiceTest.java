package com.subskribe.billy.subscription.db;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.type.TypeReference;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.fixtures.AccountData;
import com.subskribe.billy.fixtures.ProductCatalogData;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.services.SubscriptionEventService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionIdGenerator;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import com.subskribe.billy.validation.helpers.SubscriptionValidation;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SubscriptionServiceTest extends WithDb {

    private static final String TENANT_ID = "test tenant 1 id";
    private static final UUID PLAN_ID = UUID.fromString("363db8f3-0c2d-46bd-a52a-0a204291d5bc");
    private static final String EXTERNAL_PLAN_ID = "Tenant 1 Plan 1";
    private static final String TEST_PRODUCT_ID = "test_product_id";
    private static final UUID CHARGE_ID = UUID.fromString("c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a");
    private static final String ACCOUNT_ID = "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68";
    private static final String USER_ID = "8625d494-**************-fe7487aae7da";
    private static final String ADDRESS_ID = "ffb3c876-4f4f-419d-a99a-d0881acbeea8";

    private static final TenantIdProvider MOCK_TENANT_ID_PROVIDER = mock(TenantIdProvider.class);
    private static final ProductCatalogGetService MOCK_PRODUCT_CATALOG_GET_SVC = mock(ProductCatalogGetService.class);
    private static final AccountGetService MOCK_ACCOUNT_GET_SERVICE = mock(AccountGetService.class);
    private static final EmailContactListService MOCK_EMAIL_CONTACT_LIST_SERVICE = mock(EmailContactListService.class);
    private static final CustomFieldService MOCK_CUSTOM_FIELDS_SERVICE = mock(CustomFieldService.class);
    private static final HubSpotJobQueueService MOCK_HUBSPOT_JOB_QUEUE_SERVICE = mock(HubSpotJobQueueService.class);
    private static final CrmService MOCK_CRM_SERVICE = mock(CrmService.class);
    private SubscriptionService subscriptionService;
    private TestData testData;
    private String generatedExternalSubscriptionId;

    private SubscriptionGetService subscriptionGetService;

    @BeforeAll
    public void init() throws IOException, URISyntaxException {
        EntityFixture.initDb(dslContextProvider);
        var subscriptionDao = new SubscriptionDAO(MOCK_TENANT_ID_PROVIDER, dslContextProvider);
        var subscriptionIdGenerator = new SubscriptionIdGenerator(subscriptionDao, dslContextProvider, MOCK_TENANT_ID_PROVIDER);

        subscriptionGetService = new SubscriptionGetService(subscriptionDao, MOCK_TENANT_ID_PROVIDER, dslContextProvider, MOCK_CUSTOM_FIELDS_SERVICE);
        subscriptionService = new SubscriptionService(
            subscriptionDao,
            dslContextProvider,
            MOCK_TENANT_ID_PROVIDER,
            MOCK_ACCOUNT_GET_SERVICE,
            subscriptionIdGenerator,
            subscriptionGetService,
            mock(SalesforceJobQueueService.class),
            MOCK_HUBSPOT_JOB_QUEUE_SERVICE,
            MOCK_EMAIL_CONTACT_LIST_SERVICE,
            MOCK_CUSTOM_FIELDS_SERVICE,
            MOCK_CRM_SERVICE,
            mock(SubscriptionEventService.class)
        );

        testData = asType(getClass(), "createUpdateSubTestData.json", new TypeReference<>() {});
        var charge = ProductCatalogData.createCharge(CHARGE_ID, PLAN_ID);
        Plan plan = ProductCatalogData.createPlan(PLAN_ID, EXTERNAL_PLAN_ID, TEST_PRODUCT_ID, List.of(charge));
        when(MOCK_PRODUCT_CATALOG_GET_SVC.getPlansByIds(List.of(PLAN_ID))).thenReturn(List.of(plan));
        when(MOCK_ACCOUNT_GET_SERVICE.getAccount(ACCOUNT_ID)).thenReturn(AccountData.createAccount(ACCOUNT_ID, USER_ID, ADDRESS_ID));
        when(MOCK_ACCOUNT_GET_SERVICE.getContact(USER_ID)).thenReturn(AccountData.createAccountUser(USER_ID, ACCOUNT_ID, ADDRESS_ID));
        when(MOCK_TENANT_ID_PROVIDER.provideTenantIdString()).thenReturn(TENANT_ID);
        when(MOCK_CUSTOM_FIELDS_SERVICE.getCustomFields(any(), anyString())).thenReturn(new CustomField(new HashMap<>()));
    }

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    private static class TestData {

        // create a new subscription
        Order orderV1;
        SubscriptionTestData expectedSubscriptionV1;

        // update subscription
        Order orderV2;
        SubscriptionTestData expectedSubscriptionV2;

        // cancel a subscription charge to a future date
        Order orderV3;
        SubscriptionTestData expectedSubscriptionV3;
    }

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    private static class SubscriptionTestData {

        SubscriptionEntity subscriptionEntity;
        List<SubscriptionCharge> charges;
    }

    @Test
    @org.junit.jupiter.api.Order(1)
    public void testCreateSubscription() {
        var dslContext = dslContextProvider.get();
        var subscription = subscriptionService.createSubscription(dslContext, testData.orderV1);
        generatedExternalSubscriptionId = subscription.getSubscriptionId();
        SubscriptionValidation.validateEntity(testData.expectedSubscriptionV1.subscriptionEntity, subscription);
        SubscriptionValidation.validateCharges(testData.expectedSubscriptionV1.charges, subscription.getCharges());
    }

    @Test
    @org.junit.jupiter.api.Order(2)
    public void testUpdateSubscription() {
        var dslContext = dslContextProvider.get();

        // set the proper SubscriptionChargeId on charges in the OrderV2 object
        var latestSubscription = subscriptionGetService.getSubscription(generatedExternalSubscriptionId);
        testData.orderV2
            .getLineItems()
            .forEach(li -> li.setBaseExternalSubscriptionChargeId(latestSubscription.getCharges().get(0).getSubscriptionChargeId()));

        testData.orderV2.setExternalSubscriptionId(generatedExternalSubscriptionId);
        var subscription = subscriptionService.updateSubscription(dslContext, testData.orderV2);
        SubscriptionValidation.validateEntity(testData.expectedSubscriptionV2.subscriptionEntity, subscription);
        SubscriptionValidation.validateCharges(testData.expectedSubscriptionV2.charges, subscription.getCharges());
    }

    @Test
    @org.junit.jupiter.api.Order(3)
    public void testCancelSubscriptionLineItem() {
        var dslContext = dslContextProvider.get();

        // set the proper SubscriptionChargeId on charges in the OrderV2 object
        var latestSubscription = subscriptionGetService.getSubscription(generatedExternalSubscriptionId);
        testData.orderV3
            .getLineItems()
            .forEach(li -> li.setBaseExternalSubscriptionChargeId(latestSubscription.getCharges().get(1).getSubscriptionChargeId()));

        testData.orderV3.setExternalSubscriptionId(generatedExternalSubscriptionId);
        var subscription = subscriptionService.updateSubscription(dslContext, testData.orderV3);
        SubscriptionValidation.validateEntity(testData.expectedSubscriptionV3.subscriptionEntity, subscription);
        SubscriptionValidation.validateCharges(testData.expectedSubscriptionV3.charges, subscription.getCharges());
    }
}
