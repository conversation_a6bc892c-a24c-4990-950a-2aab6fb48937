package com.subskribe.ai.functions;

import com.subskribe.ai.service.functions.FunctionCallDefinitions;
import com.subskribe.ai.service.functions.FunctionCallName;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.util.Optional;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class FunctionCallDefinitionsTest {

    @Test
    public void testFunctionsLoadAsExpected() throws Exception {
        // all functions should be present in the definition
        // all of them should have description and params schema
        for (FunctionCallName name : FunctionCallName.values()) {
            Optional<String> description = FunctionCallDefinitions.getFunctionDescription(name.getFunctionName());
            Optional<String> jsonSchema = FunctionCallDefinitions.getFunctionParamJsonSchema(name.getFunctionName());

            Assertions.assertThat(description).isPresent();
            Assertions.assertThat(jsonSchema).isPresent();

            // json schema should be parseable
            JacksonProvider.defaultMapper().readTree(jsonSchema.get());
        }

        // functions that do not exist should not manifest
        Optional<String> fooDesc = FunctionCallDefinitions.getFunctionDescription("foo");
        Optional<String> fooSchema = FunctionCallDefinitions.getFunctionParamJsonSchema("foo");
        Assertions.assertThat(fooDesc).isEmpty();
        Assertions.assertThat(fooSchema).isEmpty();
    }
}
