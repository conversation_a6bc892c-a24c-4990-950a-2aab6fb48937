package com.subskribe.ai.service;

import static org.mockito.Mockito.when;

import com.subskribe.ai.service.db.PromptDAO;
import com.subskribe.ai.service.model.PromptType;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.util.Optional;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PromptDbTest extends WithDb {

    private static final String TEST_PROMPT =
        """
        Test Guided Selling Prompt, and this prompt is needed for the system to work as expected,
        this makes sure things work as expected
        """;

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";

    @Mock
    private TenantIdProvider tenantIdProvider;

    private PromptService promptService;

    @BeforeAll
    public void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        PromptDAO promptDAO = new PromptDAO(tenantIdProvider, dslContextProvider);
        promptService = new PromptService(promptDAO);
    }

    @Test
    public void promptAdditionWorksAsExpected() {
        Optional<String> stored = promptService.getPrompt(PromptType.GUIDED_SELLING);
        Assertions.assertThat(stored).isEmpty();

        promptService.upsertPrompt(PromptType.GUIDED_SELLING, TEST_PROMPT);
        stored = promptService.getPrompt(PromptType.GUIDED_SELLING);
        Assertions.assertThat(stored).isNotEmpty();
        Assertions.assertThat(stored.get()).isEqualTo(TEST_PROMPT);

        promptService.purgePrompt(PromptType.GUIDED_SELLING);
        stored = promptService.getPrompt(PromptType.GUIDED_SELLING);
        Assertions.assertThat(stored).isEmpty();
    }
}
