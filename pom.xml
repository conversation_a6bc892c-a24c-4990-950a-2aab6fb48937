<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.subskribe</groupId>
    <artifactId>billy-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>billy-app</module>
        <module>subskribe-api</module>
        <module>infra/tenant-lambda-executor</module>
    </modules>
    <distributionManagement>
        <repository>
            <id>codeartifact</id>
            <name>codeartifact</name>
            <url>https://subskribe-domain-054887729006.d.codeartifact.us-east-2.amazonaws.com/maven/subskribe-maven-repo/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </distributionManagement>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.release>17</maven.compiler.release>
        <java.version>17</java.version>
        <javadoc.plugin.version>3.11.2</javadoc.plugin.version>
        <pmd.version>3.27.0</pmd.version>
        <jacoco.version>0.8.13</jacoco.version>
        <dropwizard.version>2.1.12</dropwizard.version>
        <!-- DW and jersey version are tied currently DW brings jersey 2.41
             when we update DW we need to also keep jersey version in sync-->
        <jersey.version>2.41</jersey.version>
        <jackson.version>2.19.1</jackson.version>
        <mainClass>com.subskribe.billy.BillyApplication</mainClass>
        <jooq.version>3.19.18</jooq.version>
        <junit.version>5.11.4</junit.version>
        <awsjavasdk.version>2.31.78</awsjavasdk.version>
        <stripe.version>22.31.0</stripe.version>
        <javadoc.exclude.packages>com.subskribe.billy.jooq.*:com.subskribe.billy.jooq</javadoc.exclude.packages>
        <archunit.version>1.4.1</archunit.version>
        <datadog.trace.api.version>1.51.1</datadog.trace.api.version>
        <opentracing.version>0.33.0</opentracing.version>
        <testGroups>!integration</testGroups>
        <license.skipDownloadLicenses>true</license.skipDownloadLicenses>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <maven.source.skip>true</maven.source.skip>
        <quickbooks.version>6.5.0</quickbooks.version>
        <immutables.version>2.11.0</immutables.version>
        <testcontainers.version>1.19.1</testcontainers.version>
        <equalsverifier.version>4.0.4</equalsverifier.version>
        <kinesis.producer>0.15.8</kinesis.producer>
        <kinesis.consumer>2.5.4</kinesis.consumer>
        <dynamodb.lock.client.version>1.3.0</dynamodb.lock.client.version>
        <bucket4j.version>8.10.1</bucket4j.version>
        <avro.version>1.12.0</avro.version>
        <jnr-ffi.version>2.2.17</jnr-ffi.version>
        <jakarta.validation-api.version>2.0.2</jakarta.validation-api.version>
        <hibernate-validator.version>6.2.0.Final</hibernate-validator.version>
        <assertj-core.version>3.27.3</assertj-core.version>
        <commons-lang3.version>3.18.0</commons-lang3.version>
        <commons-collections4.version>4.5.0</commons-collections4.version>
        <commons-math3.version>3.6.1</commons-math3.version>
        <commons-io.version>2.19.0</commons-io.version>
        <dropwizard-swagger.version>2.0.12-1</dropwizard-swagger.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <mapstruct-processor.version>1.6.3</mapstruct-processor.version>
        <postgresql.version>42.7.7</postgresql.version>
        <javax.persistence-api.version>2.2</javax.persistence-api.version>
        <dropwizard-flyway.version>2.1.0-1</dropwizard-flyway.version>
        <java-jwt.version>4.5.0</java-jwt.version>
        <jwks-rsa.version>0.22.1</jwks-rsa.version>
        <mockito-core.version>5.18.0</mockito-core.version>
        <groovy-all.version>4.0.27</groovy-all.version>
        <failsafe.version>3.3.2</failsafe.version>
        <quartz.version>2.5.0</quartz.version>
        <gson.version>2.13.1</gson.version>
        <graphql-java-annotations.version>9.1</graphql-java-annotations.version>
        <httpclient5.version>5.5</httpclient5.version>
        <compiler.version>0.9.14</compiler.version>
        <avatax-rest-v2-api.version>25.6.2</avatax-rest-v2-api.version>
        <elasticsearch-rest-high-level-client.version>7.13.0</elasticsearch-rest-high-level-client.version>
        <log4j.version>2.25.0</log4j.version>
        <dropwizard-metrics-datadog.version>1.1.14</dropwizard-metrics-datadog.version>
        <commons-csv.version>1.14.0</commons-csv.version>
        <okhttp.version>4.12.0</okhttp.version>
        <libphonenumber.version>9.0.9</libphonenumber.version>
        <mockwebserver.version>2.7.5</mockwebserver.version>
        <xmemcached.version>2.4.8</xmemcached.version>
        <mail.version>1.4.7</mail.version>
        <owasp-java-html-sanitizer.version>20240325.1</owasp-java-html-sanitizer.version>
        <typesafe-config.version>1.4.3</typesafe-config.version>
        <docusign-esign-java.version>3.18.0</docusign-esign-java.version>
        <spotbugs-annotations.version>4.9.3</spotbugs-annotations.version>
        <json-logic-java.version>1.1.0</json-logic-java.version>
        <bcpkix-jdk15on.version>1.70</bcpkix-jdk15on.version>
        <json.version>20250517</json.version>
        <opencsv.version>5.11.2</opencsv.version>
        <slack-api-client.version>1.45.3</slack-api-client.version>
        <pdfbox.version>2.0.34</pdfbox.version>
        <pandadoc-java-client.version>6.2.0</pandadoc-java-client.version>
        <cron-expression-descriptor.version>1.2.10</cron-expression-descriptor.version>
        <google-api-client.version>2.8.0</google-api-client.version>
        <taxjar-java.version>5.0.3</taxjar-java.version>
        <merge-java-client.version>1.0.17</merge-java-client.version>
        <commons-dbcp2.version>2.13.0</commons-dbcp2.version>
        <exp4j.version>0.4.8</exp4j.version>
        <azure-ai-openai-assistants.version>1.0.0-beta.5</azure-ai-openai-assistants.version>
        <maven-shade-plugin.version>3.6.0</maven-shade-plugin.version>
        <maven-jar-plugin.version>3.4.2</maven-jar-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <junit-vintage-engine.version>5.11.4</junit-vintage-engine.version>
        <spotless-maven-plugin.version>2.44.2</spotless-maven-plugin.version>
        <maven-site-plugin.version>3.21.0</maven-site-plugin.version>
        <maven-enforcer-plugin.version>3.6.0</maven-enforcer-plugin.version>
        <license-maven-plugin.version>2.6.0</license-maven-plugin.version>
        <spotbugs-maven-plugin.version>4.8.6.6</spotbugs-maven-plugin.version>
        <versions-maven-plugin.version>2.18.0</versions-maven-plugin.version>
        <aws-lambda-java-log4j2.version>1.6.0</aws-lambda-java-log4j2.version>
        <swagger-annotations.version>1.6.15</swagger-annotations.version>
        <crac.version>1.5.0</crac.version>
        <jsoup.version>1.21.1</jsoup.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-dependencies</artifactId>
                <version>${dropwizard.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${awsjavasdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.github.jnr</groupId>
                <artifactId>jnr-ffi</artifactId>
                <version>${jnr-ffi.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-core</artifactId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-sse</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj-core.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.tngtech.archunit</groupId>
                <artifactId>archunit-junit5</artifactId>
                <version>${archunit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>${commons-math3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.smoketurner</groupId>
                <artifactId>dropwizard-swagger</artifactId>
                <version>${dropwizard-swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct-processor.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>secretsmanager</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cognitoidentity</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cognitoidentityprovider</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sqs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sns</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ses</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dynamodb</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dynamodb-enhanced</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesis</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>amazon-kinesis-producer</artifactId>
                <version>${kinesis.producer}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.kinesis</groupId>
                <artifactId>amazon-kinesis-client</artifactId>
                <version>${kinesis.consumer}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apache-client</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudwatch</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appconfig</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appconfigdata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sts</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jooq.pro</groupId>
                <artifactId>jooq</artifactId>
                <version>${jooq.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>${javax.persistence-api.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.modules</groupId>
                <artifactId>dropwizard-flyway</artifactId>
                <version>${dropwizard-flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-auth</artifactId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-testing</artifactId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>jwks-rsa</artifactId>
                <version>${jwks-rsa.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-core</artifactId>
                <version>2.3-groovy-4.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>${groovy-all.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.stripe</groupId>
                <artifactId>stripe-java</artifactId>
                <version>${stripe.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.failsafe</groupId>
                <artifactId>failsafe</artifactId>
                <version>${failsafe.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.graphql-java</groupId>
                <artifactId>graphql-java-annotations</artifactId>
                <version>${graphql-java-annotations.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.spullara.mustache.java</groupId>
                <artifactId>compiler</artifactId>
                <version>${compiler.version}</version>
            </dependency>
            <dependency>
                <groupId>net.avalara.avatax</groupId>
                <artifactId>avatax-rest-v2-api-java_2.11</artifactId>
                <version>${avatax-rest-v2-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch-rest-high-level-client.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-json-logging</artifactId>
                <version>${dropwizard.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-forms</artifactId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <groupId>org.coursera</groupId>
                <artifactId>dropwizard-metrics-datadog</artifactId>
                <version>${dropwizard-metrics-datadog.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${commons-csv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.libphonenumber</groupId>
                <artifactId>libphonenumber</artifactId>
                <version>${libphonenumber.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp</groupId>
                <artifactId>mockwebserver</artifactId>
                <version>${mockwebserver.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.googlecode.xmemcached</groupId>
                <artifactId>xmemcached</artifactId>
                <version>${xmemcached.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${mail.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
                <artifactId>owasp-java-html-sanitizer</artifactId>
                <version>${owasp-java-html-sanitizer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe</groupId>
                <artifactId>config</artifactId>
                <version>${typesafe-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.subskribe</groupId>
                <artifactId>docusign-esign-java</artifactId>
                <version>${docusign-esign-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-annotations</artifactId>
                <version>${spotbugs-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.jamsesso</groupId>
                <artifactId>json-logic-java</artifactId>
                <version>${json-logic-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bcpkix-jdk15on.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${json.version}</version>
            </dependency>
            <dependency>
                <groupId>com.datadoghq</groupId>
                <artifactId>dd-trace-api</artifactId>
                <version>${datadog.trace.api.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.slack.api</groupId>
                <artifactId>slack-api-client</artifactId>
                <version>${slack-api-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.intuit.quickbooks-online</groupId>
                <artifactId>oauth2-platform-api</artifactId>
                <version>${quickbooks.version}</version>
                <classifier>jar-with-dependencies</classifier>
            </dependency>
            <dependency>
                <groupId>com.intuit.quickbooks-online</groupId>
                <artifactId>ipp-v3-java-devkit</artifactId>
                <version>${quickbooks.version}</version>
                <classifier>jar-with-dependencies</classifier>
            </dependency>
            <dependency>
                <groupId>com.intuit.quickbooks-online</groupId>
                <artifactId>ipp-v3-java-data</artifactId>
                <version>${quickbooks.version}</version>
            </dependency>
            <dependency>
                <groupId>org.immutables</groupId>
                <artifactId>value</artifactId>
                <version>${immutables.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>postgresql</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>localstack</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.pandadoc</groupId>
                <artifactId>pandadoc-java-client</artifactId>
                <version>${pandadoc-java-client.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>it.burning</groupId>
                <artifactId>cron-expression-descriptor</artifactId>
                <version>${cron-expression-descriptor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.api-client</groupId>
                <artifactId>google-api-client</artifactId>
                <version>${google-api-client.version}</version>
            </dependency>
            <dependency>
                <groupId>nl.jqno.equalsverifier</groupId>
                <artifactId>equalsverifier</artifactId>
                <version>${equalsverifier.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.taxjar</groupId>
                <artifactId>taxjar-java</artifactId>
                <version>${taxjar-java.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.merge</groupId>
                <artifactId>merge-java-client</artifactId>
                <version>${merge-java-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>dynamodb-lock-client</artifactId>
                <version>${dynamodb.lock.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>${commons-dbcp2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bucket4j</groupId>
                <artifactId>bucket4j-core</artifactId>
                <version>${bucket4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bucket4j</groupId>
                <artifactId>bucket4j-postgresql</artifactId>
                <version>${bucket4j.version}</version>
            </dependency>
            <dependency>
                <groupId>net.objecthunter</groupId>
                <artifactId>exp4j</artifactId>
                <version>${exp4j.version}</version>
            </dependency>
            <!-- AI dependencies -->
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-ai-openai-assistants</artifactId>
                <version>${azure-ai-openai-assistants.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bedrockruntime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>${avro.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lambda</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-log4j2</artifactId>
                <version>${aws-lambda-java-log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-guava</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.crac</groupId>
                <artifactId>crac</artifactId>
                <version>${crac.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${javadoc.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.2</version>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.vintage</groupId>
                        <artifactId>junit-vintage-engine</artifactId>
                        <version>${junit-vintage-engine.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless-maven-plugin.version}</version>
                <configuration>
                    <formats>
                        <format>
                            <includes>
                                <include>.gitignore</include>
                            </includes>
                            <excludes>
                                <exclude>*.md</exclude>
                            </excludes>
                            <!-- define the steps to apply to those files -->
                            <trimTrailingWhitespace></trimTrailingWhitespace>
                            <endWithNewline></endWithNewline>
                            <indent>
                                <tabs>true</tabs>
                                <spacesPerTab>4</spacesPerTab>
                            </indent>
                        </format>
                    </formats>
                    <java>
                        <excludes>
                            <exclude>**/billy/jooq/**/*.*</exclude>
                        </excludes>
                        <prettier>
                            <devDependencies>
                                <prettier>3.4.2</prettier>
                                <prettier-plugin-java>2.6.7</prettier-plugin-java>
                            </devDependencies>
                            <config>
                                <tabWidth>4</tabWidth>
                                <useTabs>false</useTabs>
                                <printWidth>150</printWidth>
                                <parser>java</parser>
                                <plugins>prettier-plugin-java</plugins>
                            </config>
                        </prettier>
                        <removeUnusedImports></removeUnusedImports>
                    </java>
                    <pom>
                        <includes>
                            <include>pom.xml</include>
                            <include>**/pom.xml</include>
                        </includes>
                        <sortPom>
                            <nrOfIndentSpace>4</nrOfIndentSpace>
                            <sortExecutions>false</sortExecutions>
                            <!-- Sort plugin executions -->
                        </sortPom>
                    </pom>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>${maven-site-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.8.7</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>[17,18)</version>
                                </requireJavaVersion>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <phase>prepare-package</phase>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <phase>test</phase>
                        <configuration>
                            <outputDirectory>target/coverage-reports</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>${license-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>download-licenses</id>
                        <goals>
                            <goal>download-licenses</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>${pmd.version}</version>
                <configuration>
                    <linkXRef>true</linkXRef>
                    <inputEncoding>utf-8</inputEncoding>
                    <minimumTokens>100</minimumTokens>
                    <targetJdk>17</targetJdk>
                    <rulesets>
                        <ruleset>${maven.multiModuleProjectDirectory}/pmd-rules.xml</ruleset>
                        <ruleset>${maven.multiModuleProjectDirectory}/pmd-custom-rules.xml</ruleset>
                    </rulesets>
                    <printFailingErrors>true</printFailingErrors>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                            <goal>cpd</goal>
                        </goals>
                        <phase>verify</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>${spotbugs-maven-plugin.version}</version>
                <configuration>
                    <xmlOutput>true</xmlOutput>
                    <!-- Optional directory to put spotbugs xdoc xml report -->
                    <xmlOutputDirectory>target/site</xmlOutputDirectory>
                    <excludeFilterFile>${maven.multiModuleProjectDirectory}/spotbugs-exclude.xml</excludeFilterFile>
                    <maxHeap>2048</maxHeap>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>verify</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${versions-maven-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>noverify</id>
            <properties>
                <spotbugs.skip>true</spotbugs.skip>
                <maven.test.skip>true</maven.test.skip>
                <pmd.skip>true</pmd.skip>
                <spotless.check.skip>true</spotless.check.skip>
            </properties>
        </profile>
    </profiles>
</project>
