package com.subskribe.billy.resources.json.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.customization.model.OrderCreationCustomizationOutputJson;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.CreditableAmount;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.resources.json.opportunity.OpportunityJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.template.model.OrderTerms;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.BooleanUtils;

@GraphQLName("Order")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderJson implements NotifyingEvent {

    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    @ApiModelProperty(value = "Uniquely identifies the Order.")
    private String id;

    @JsonProperty
    @GraphQLField
    @GraphQLName("entityId")
    @ApiModelProperty(value = "Entity ID associated with the order.")
    private String entityId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    @ApiModelProperty(value = "External identifier for the order.")
    private String externalId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("name")
    @ApiModelProperty(value = "Name of the Order.")
    private String name;

    @JsonProperty
    @GraphQLField
    @GraphQLName("accountId")
    @ApiModelProperty(value = "ID of the account associated with the order.")
    private String accountId;

    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("orderType")
    @ApiModelProperty(value = "Type of the order. Possible values: NEW, AMENDMENT, RENEWAL, CHANGE, CANCEL.")
    @GraphQLNonNull
    private OrderType orderType;

    @GraphQLField
    @GraphQLName("currency")
    @ApiModelProperty(value = "Currency in which the order is billed. Default is USD.")
    private String currency;

    @JsonProperty
    @GraphQLField
    @GraphQLName("paymentTerm")
    @ApiModelProperty(value = "Payment terms for the order.")
    private String paymentTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("subscriptionId")
    @ApiModelProperty(value = "The associated subscription ID.")
    private String subscriptionId;

    @GraphQLField
    @GraphQLName("subscriptionTargetVersion")
    @ApiModelProperty(value = "Target version of the subscription.")
    private int subscriptionTargetVersion;

    @JsonProperty
    @GraphQLField
    @GraphQLName("shippingContactId")
    @ApiModelProperty(value = "Shipping contact ID for the order.")
    private String shippingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingContactId")
    @ApiModelProperty(value = "Billing contact ID for the order.")
    private String billingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    @ApiModelProperty(value = "List of predefined discounts to be applied.")
    private List<TenantDiscountJson> predefinedDiscounts;

    @JsonProperty
    @GraphQLField
    @GraphQLName("creditableAmounts")
    @ApiModelProperty(value = "List of credit amount to be applied during amendment and cancellation.")
    private List<CreditableAmount> creditableAmounts;

    @JsonProperty
    @GraphQLField
    @GraphQLName("lineItems")
    @ApiModelProperty(value = "Line items included in the order.")
    private List<OrderLineItemJson> lineItems;

    @GraphQLField
    @GraphQLName("lineItemsNetEffect")
    @ApiModelProperty(value = "Net effect of line items.")
    private List<OrderLineItemJson> lineItemsNetEffect;

    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("startDate")
    @ApiModelProperty(value = "The start date of the order.")
    @GraphQLNonNull
    private Long startDate;

    @GraphQLField
    @GraphQLName("endDate")
    @ApiModelProperty(value = "The end date of the order.")
    private Long endDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("termLength")
    @ApiModelProperty(value = "The term length for the order.")
    private RecurrenceJson termLength;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingCycle")
    @ApiModelProperty(value = "	The billing cycle. Possible values: MONTH, YEAR, etc.")
    private RecurrenceJson billingCycle;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingTerm")
    @ApiModelProperty(value = "Billing term for the order. Possible values: UP_FRONT, RECURRING.")
    private BillingTerm billingTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingAnchorDate")
    @ApiModelProperty(value = "Anchor date for billing in Unix timestamp.")
    private Long billingAnchorDate;

    @GraphQLField
    @GraphQLName("totalAmount")
    @ApiModelProperty(value = "Total Amount of the order.")
    private BigDecimal totalAmount;

    private BigDecimal totalListAmount;

    private BigDecimal totalListAmountBeforeOverride;

    @GraphQLField
    @GraphQLName("taxEstimate")
    @ApiModelProperty(value = "Estimated taxes for the order.")
    private BigDecimal taxEstimate;

    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("status")
    @ApiModelProperty(value = "Current status of the order.")
    @GraphQLNonNull
    private OrderStatus status;

    @JsonProperty
    @GraphQLField
    @GraphQLName("executedOn")
    @ApiModelProperty(value = "The date the order was executed.")
    private Long executedOn;

    @JsonProperty
    @GraphQLField
    @GraphQLName("updatedOn")
    @ApiModelProperty(value = "Timestamp of the last update to the order.")
    @GraphQLNonNull
    private Long updatedOn;

    private String executedOnFormatted;

    @JsonProperty
    @GraphQLField
    @GraphQLName("rampInterval")
    @ApiModelProperty(value = "Ramp interval.")
    private List<Long> rampInterval;

    @JsonProperty
    @GraphQLField
    @GraphQLName("orderFormTemplateIds")
    @ApiModelProperty(value = "Order form template IDs associated with the order.")
    private List<String> orderFormTemplateIds;

    @JsonProperty
    @GraphQLField
    @GraphQLName("orderTerms")
    @ApiModelProperty(value = "Terms and conditions of the order.")
    private List<OrderTerms> orderTerms;

    // TODO: Rename and remove _sfdc_ from all following fields.

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityId")
    @ApiModelProperty(value = "Salesforce opportunity ID associated with the order.")
    private String sfdcOpportunityId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("isPrimaryOrderForSfdcOpportunity")
    @ApiModelProperty(value = "Indicates if the order is the primary order for the Salesforce opportunity.")
    private Boolean isPrimaryOrderForSfdcOpportunity;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityName")
    @ApiModelProperty(value = "Salesforce opportunity name.")
    private String sfdcOpportunityName;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityType")
    @ApiModelProperty(value = "Type of Salesforce opportunity.")
    private String sfdcOpportunityType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityStage")
    @ApiModelProperty(value = "Stage of the Salesforce opportunity.")
    private String sfdcOpportunityStage;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOrderCanBeExecuted")
    @ApiModelProperty(value = "Indicates whether the order can be executed in Salesforce.")
    private Boolean sfdcOrderCanBeExecuted;

    @JsonProperty
    @GraphQLField
    @GraphQLName("opportunityCrmType")
    @ApiModelProperty(value = "CRM type of the opportunity (e.g., SALESFORCE).")
    private OpportunityCrmType opportunityCrmType;

    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = "ID of the subscription being renewed.")
    private String renewalForSubscriptionId;

    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = "The version of the subscription being renewed.")
    private int renewalForSubscriptionVersion;

    @JsonProperty
    @GraphQLField
    @GraphQLName("ownerId")
    @ApiModelProperty(value = "Owner ID of the order.")
    private String ownerId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("documentMasterTemplateId")
    @ApiModelProperty(value = "ID of the master document template.")
    private String documentMasterTemplateId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderNumber")
    @ApiModelProperty(value = "Purchase order number.")
    private String purchaseOrderNumber;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderRequiredForInvoicing")
    @ApiModelProperty(value = "Indicates if a purchase order is required for invoicing.")
    private Boolean purchaseOrderRequiredForInvoicing;

    @JsonProperty
    @GraphQLField
    @GraphQLName("autoRenew")
    @ApiModelProperty(value = " Indicates if the order is set to auto-renew.")
    private boolean autoRenew;

    @JsonProperty
    @GraphQLField
    @GraphQLName("approvalSegmentId")
    @ApiModelProperty(value = "Approval segment ID.")
    private String approvalSegmentId;

    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = "Attachment ID for any associated files.")
    private String attachmentId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("compositeOrderId")
    @ApiModelProperty(value = "ID of the Composite order.")
    private String compositeOrderId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("restructureForSubscriptionId")
    @ApiModelProperty(value = "ID of the subscription being restructured.")
    private String restructureForSubscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("expiresOn")
    @ApiModelProperty(value = "Expiration date of the order.")
    private Long expiresOn;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customFields")
    @ApiModelProperty(value = "Custom fields for the order.")
    private List<CustomFieldEntry> customFields;

    @JsonProperty
    @GraphQLField
    @GraphQLName("startDateType")
    @ApiModelProperty(value = "Start date type (e.g., FIXED).")
    private OrderStartDateType startDateType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customBillingEligibleOrderLineIds")
    @ApiModelProperty(value = "List of order line IDs eligible for custom billing.")
    private List<String> customBillingEligibleOrderLineIds;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customPredefinedTemplatesOnOrder")
    @ApiModelProperty(value = "List of custom predefined templates on the order.")
    private List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder;

    @JsonProperty
    @GraphQLField
    @GraphQLName("subscriptionDurationModel")
    @ApiModelProperty(value = "Duration model of the order (i.e TERMED, EVERGREEN).")
    private SubscriptionDurationModel subscriptionDurationModel;

    @JsonProperty
    @GraphQLField
    @GraphQLName("opportunity")
    @ApiModelProperty(value = "Opportunity associated with the order.")
    private OpportunityJson opportunity;

    @JsonProperty
    private OrderCreationCustomizationOutputJson zeppaOutput;

    public OrderJson() {
        customFields = new ArrayList<>();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    @JsonProperty
    public int getSubscriptionTargetVersion() {
        return subscriptionTargetVersion;
    }

    @JsonIgnore
    public void setSubscriptionTargetVersion(int subscriptionTargetVersion) {
        this.subscriptionTargetVersion = subscriptionTargetVersion;
    }

    public String getShippingContactId() {
        return shippingContactId;
    }

    public void setShippingContactId(String shippingContactId) {
        this.shippingContactId = shippingContactId;
    }

    public String getBillingContactId() {
        return billingContactId;
    }

    public void setBillingContactId(String billingContactId) {
        this.billingContactId = billingContactId;
    }

    public List<OrderLineItemJson> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<OrderLineItemJson> lineItems) {
        this.lineItems = lineItems;
    }

    @JsonProperty
    public List<OrderLineItemJson> getLineItemsNetEffect() {
        return lineItemsNetEffect;
    }

    @JsonIgnore
    public void setLineItemsNetEffect(List<OrderLineItemJson> lineItemsNetEffect) {
        this.lineItemsNetEffect = lineItemsNetEffect;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public RecurrenceJson getTermLength() {
        return termLength;
    }

    public void setTermLength(RecurrenceJson termLength) {
        this.termLength = termLength;
    }

    public RecurrenceJson getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(RecurrenceJson billingCycle) {
        this.billingCycle = billingCycle;
    }

    @JsonProperty
    public BigDecimal getTotalListAmount() {
        return totalListAmount;
    }

    public void setTotalListAmount(BigDecimal totalListAmount) {
        this.totalListAmount = totalListAmount;
    }

    @JsonProperty
    public BigDecimal getTotalListAmountBeforeOverride() {
        return totalListAmountBeforeOverride;
    }

    public void setTotalListAmountBeforeOverride(BigDecimal totalListAmountBeforeOverride) {
        this.totalListAmountBeforeOverride = totalListAmountBeforeOverride;
    }

    @JsonProperty
    public BigDecimal getTaxEstimate() {
        return taxEstimate;
    }

    public void setTaxEstimate(BigDecimal taxEstimate) {
        this.taxEstimate = taxEstimate;
    }

    @JsonProperty
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public Long getBillingAnchorDate() {
        return billingAnchorDate;
    }

    public void setBillingAnchorDate(Long billingAnchorDate) {
        this.billingAnchorDate = billingAnchorDate;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public Long getExecutedOn() {
        return executedOn;
    }

    public void setExecutedOn(Long executedOn) {
        this.executedOn = executedOn;
    }

    public String getExecutedOnFormatted() {
        return executedOnFormatted;
    }

    public void setExecutedOnFormatted(String executedOnFormatted) {
        this.executedOnFormatted = executedOnFormatted;
    }

    public Long getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Long updatedOn) {
        this.updatedOn = updatedOn;
    }

    public List<Long> getRampInterval() {
        return rampInterval;
    }

    public void setRampInterval(List<Long> rampInterval) {
        this.rampInterval = rampInterval;
    }

    public List<String> getOrderFormTemplateIds() {
        return Objects.requireNonNullElseGet(orderFormTemplateIds, List::of);
    }

    public void setOrderFormTemplateIds(List<String> orderFormTemplateIds) {
        this.orderFormTemplateIds = orderFormTemplateIds;
    }

    public List<OrderTerms> getOrderTerms() {
        return Objects.requireNonNullElseGet(orderTerms, List::of);
    }

    public void setOrderTerms(List<OrderTerms> orderTerms) {
        this.orderTerms = orderTerms;
    }

    public String getSfdcOpportunityId() {
        return sfdcOpportunityId;
    }

    public void setSfdcOpportunityId(String sfdcOpportunityId) {
        this.sfdcOpportunityId = sfdcOpportunityId;
    }

    public String getSfdcOpportunityName() {
        return sfdcOpportunityName;
    }

    public void setSfdcOpportunityName(String sfdcOpportunityName) {
        this.sfdcOpportunityName = sfdcOpportunityName;
    }

    public String getSfdcOpportunityType() {
        return sfdcOpportunityType;
    }

    public void setSfdcOpportunityType(String sfdcOpportunityType) {
        this.sfdcOpportunityType = sfdcOpportunityType;
    }

    public String getSfdcOpportunityStage() {
        return sfdcOpportunityStage;
    }

    public void setSfdcOpportunityStage(String sfdcOpportunityStage) {
        this.sfdcOpportunityStage = sfdcOpportunityStage;
    }

    public boolean getIsPrimaryOrderForSfdcOpportunity() {
        return BooleanUtils.isTrue(isPrimaryOrderForSfdcOpportunity);
    }

    public void setIsPrimaryOrderForSfdcOpportunity(Boolean primaryOrderForSfdcOpportunity) {
        isPrimaryOrderForSfdcOpportunity = primaryOrderForSfdcOpportunity;
    }

    public boolean getSfdcOrderCanBeExecuted() {
        return BooleanUtils.isNotFalse(sfdcOrderCanBeExecuted);
    }

    public void setSfdcOrderCanBeExecuted(Boolean sfdcOrderCanBeExecuted) {
        this.sfdcOrderCanBeExecuted = sfdcOrderCanBeExecuted;
    }

    public List<TenantDiscountJson> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<TenantDiscountJson> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public List<CreditableAmount> getCreditableAmounts() {
        return creditableAmounts;
    }

    public void setCreditableAmounts(List<CreditableAmount> creditableAmounts) {
        this.creditableAmounts = creditableAmounts;
    }

    public String getRenewalForSubscriptionId() {
        return renewalForSubscriptionId;
    }

    public void setRenewalForSubscriptionId(String renewalForSubscriptionId) {
        this.renewalForSubscriptionId = renewalForSubscriptionId;
    }

    public int getRenewalForSubscriptionVersion() {
        return renewalForSubscriptionVersion;
    }

    public void setRenewalForSubscriptionVersion(int renewalForSubscriptionVersion) {
        this.renewalForSubscriptionVersion = renewalForSubscriptionVersion;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getDocumentMasterTemplateId() {
        return documentMasterTemplateId;
    }

    public void setDocumentMasterTemplateId(String documentMasterTemplateId) {
        this.documentMasterTemplateId = documentMasterTemplateId;
    }

    public String getPurchaseOrderNumber() {
        return purchaseOrderNumber;
    }

    public void setPurchaseOrderNumber(String purchaseOrderNumber) {
        this.purchaseOrderNumber = purchaseOrderNumber;
    }

    public Boolean getPurchaseOrderRequiredForInvoicing() {
        return purchaseOrderRequiredForInvoicing;
    }

    public void setPurchaseOrderRequiredForInvoicing(Boolean purchaseOrderRequiredForInvoicing) {
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public OpportunityCrmType getOpportunityCrmType() {
        return opportunityCrmType;
    }

    public void setOpportunityCrmType(OpportunityCrmType opportunityCrmType) {
        this.opportunityCrmType = opportunityCrmType;
    }

    public String getApprovalSegmentId() {
        return approvalSegmentId;
    }

    public void setApprovalSegmentId(String approvalSegmentId) {
        this.approvalSegmentId = approvalSegmentId;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getCompositeOrderId() {
        return compositeOrderId;
    }

    public void setCompositeOrderId(String compositeOrderId) {
        this.compositeOrderId = compositeOrderId;
    }

    public String getRestructureForSubscriptionId() {
        return restructureForSubscriptionId;
    }

    public void setRestructureForSubscriptionId(String restructureForSubscriptionId) {
        this.restructureForSubscriptionId = restructureForSubscriptionId;
    }

    public Long getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Long expiresOn) {
        this.expiresOn = expiresOn;
    }

    public List<CustomFieldEntry> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<CustomFieldEntry> customFields) {
        this.customFields = customFields;
    }

    public OrderStartDateType getStartDateType() {
        return startDateType;
    }

    public void setStartDateType(OrderStartDateType startDateType) {
        this.startDateType = startDateType;
    }

    public List<String> getCustomBillingEligibleOrderLineIds() {
        return customBillingEligibleOrderLineIds;
    }

    public void setCustomBillingEligibleOrderLineIds(List<String> customBillingEligibleOrderLineIds) {
        this.customBillingEligibleOrderLineIds = customBillingEligibleOrderLineIds;
    }

    public List<CustomPredefinedTemplateOnOrder> getCustomPredefinedTemplatesOnOrder() {
        return customPredefinedTemplatesOnOrder;
    }

    public void setCustomPredefinedTemplatesOnOrder(List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder) {
        this.customPredefinedTemplatesOnOrder = customPredefinedTemplatesOnOrder;
    }

    public SubscriptionDurationModel getSubscriptionDurationModel() {
        return subscriptionDurationModel;
    }

    public void setSubscriptionDurationModel(SubscriptionDurationModel subscriptionDurationModel) {
        this.subscriptionDurationModel = subscriptionDurationModel;
    }

    public OpportunityJson getOpportunity() {
        return opportunity;
    }

    public void setOpportunity(OpportunityJson opportunity) {
        this.opportunity = opportunity;
    }

    public OrderCreationCustomizationOutputJson getZeppaOutput() {
        return zeppaOutput;
    }

    public void setZeppaOutput(OrderCreationCustomizationOutputJson zeppaOutput) {
        this.zeppaOutput = zeppaOutput;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderJson orderJson = (OrderJson) o;
        return (
            subscriptionTargetVersion == orderJson.subscriptionTargetVersion &&
            renewalForSubscriptionVersion == orderJson.renewalForSubscriptionVersion &&
            autoRenew == orderJson.autoRenew &&
            Objects.equals(id, orderJson.id) &&
            Objects.equals(entityId, orderJson.entityId) &&
            Objects.equals(externalId, orderJson.externalId) &&
            Objects.equals(name, orderJson.name) &&
            Objects.equals(accountId, orderJson.accountId) &&
            orderType == orderJson.orderType &&
            Objects.equals(currency, orderJson.currency) &&
            Objects.equals(paymentTerm, orderJson.paymentTerm) &&
            Objects.equals(subscriptionId, orderJson.subscriptionId) &&
            Objects.equals(shippingContactId, orderJson.shippingContactId) &&
            Objects.equals(billingContactId, orderJson.billingContactId) &&
            Objects.equals(predefinedDiscounts, orderJson.predefinedDiscounts) &&
            Objects.equals(creditableAmounts, orderJson.creditableAmounts) &&
            Objects.equals(lineItems, orderJson.lineItems) &&
            Objects.equals(lineItemsNetEffect, orderJson.lineItemsNetEffect) &&
            Objects.equals(startDate, orderJson.startDate) &&
            Objects.equals(endDate, orderJson.endDate) &&
            Objects.equals(termLength, orderJson.termLength) &&
            Objects.equals(billingCycle, orderJson.billingCycle) &&
            billingTerm == orderJson.billingTerm &&
            Objects.equals(billingAnchorDate, orderJson.billingAnchorDate) &&
            Numbers.equals(totalAmount, orderJson.totalAmount) &&
            Numbers.equals(totalListAmount, orderJson.totalListAmount) &&
            Numbers.equals(totalListAmountBeforeOverride, orderJson.totalListAmountBeforeOverride) &&
            Numbers.equals(taxEstimate, orderJson.taxEstimate) &&
            status == orderJson.status &&
            Objects.equals(executedOn, orderJson.executedOn) &&
            Objects.equals(updatedOn, orderJson.updatedOn) &&
            Objects.equals(executedOnFormatted, orderJson.executedOnFormatted) &&
            Objects.equals(rampInterval, orderJson.rampInterval) &&
            Objects.equals(orderFormTemplateIds, orderJson.orderFormTemplateIds) &&
            Objects.equals(orderTerms, orderJson.orderTerms) &&
            Objects.equals(sfdcOpportunityId, orderJson.sfdcOpportunityId) &&
            Objects.equals(isPrimaryOrderForSfdcOpportunity, orderJson.isPrimaryOrderForSfdcOpportunity) &&
            Objects.equals(sfdcOpportunityName, orderJson.sfdcOpportunityName) &&
            Objects.equals(sfdcOpportunityType, orderJson.sfdcOpportunityType) &&
            Objects.equals(sfdcOpportunityStage, orderJson.sfdcOpportunityStage) &&
            Objects.equals(sfdcOrderCanBeExecuted, orderJson.sfdcOrderCanBeExecuted) &&
            opportunityCrmType == orderJson.opportunityCrmType &&
            Objects.equals(renewalForSubscriptionId, orderJson.renewalForSubscriptionId) &&
            Objects.equals(ownerId, orderJson.ownerId) &&
            Objects.equals(documentMasterTemplateId, orderJson.documentMasterTemplateId) &&
            Objects.equals(purchaseOrderNumber, orderJson.purchaseOrderNumber) &&
            Objects.equals(purchaseOrderRequiredForInvoicing, orderJson.purchaseOrderRequiredForInvoicing) &&
            Objects.equals(approvalSegmentId, orderJson.approvalSegmentId) &&
            Objects.equals(attachmentId, orderJson.attachmentId) &&
            Objects.equals(compositeOrderId, orderJson.compositeOrderId) &&
            Objects.equals(restructureForSubscriptionId, orderJson.restructureForSubscriptionId) &&
            Objects.equals(expiresOn, orderJson.expiresOn) &&
            Objects.equals(customFields, orderJson.customFields) &&
            startDateType == orderJson.startDateType &&
            Objects.equals(customPredefinedTemplatesOnOrder, orderJson.customPredefinedTemplatesOnOrder) &&
            subscriptionDurationModel == orderJson.subscriptionDurationModel &&
            Objects.equals(opportunity, orderJson.opportunity) &&
            Objects.equals(zeppaOutput, orderJson.zeppaOutput)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            id,
            entityId,
            externalId,
            name,
            accountId,
            orderType,
            currency,
            paymentTerm,
            subscriptionId,
            subscriptionTargetVersion,
            shippingContactId,
            billingContactId,
            predefinedDiscounts,
            creditableAmounts,
            lineItems,
            lineItemsNetEffect,
            startDate,
            endDate,
            termLength,
            billingCycle,
            billingTerm,
            billingAnchorDate,
            Numbers.makePriceDisplayScale(totalAmount),
            Numbers.makePriceDisplayScale(totalListAmount),
            Numbers.makePriceDisplayScale(totalListAmountBeforeOverride),
            Numbers.makePriceDisplayScale(taxEstimate),
            status,
            executedOn,
            updatedOn,
            executedOnFormatted,
            rampInterval,
            orderFormTemplateIds,
            orderTerms,
            sfdcOpportunityId,
            isPrimaryOrderForSfdcOpportunity,
            sfdcOpportunityName,
            sfdcOpportunityType,
            sfdcOpportunityStage,
            sfdcOrderCanBeExecuted,
            opportunityCrmType,
            renewalForSubscriptionId,
            renewalForSubscriptionVersion,
            ownerId,
            documentMasterTemplateId,
            purchaseOrderNumber,
            purchaseOrderRequiredForInvoicing,
            autoRenew,
            approvalSegmentId,
            attachmentId,
            compositeOrderId,
            restructureForSubscriptionId,
            expiresOn,
            customFields,
            startDateType,
            customPredefinedTemplatesOnOrder,
            subscriptionDurationModel,
            opportunity,
            zeppaOutput
        );
    }

    @Override
    @JsonIgnore
    public String getEventObjectId() {
        return getId();
    }
}
