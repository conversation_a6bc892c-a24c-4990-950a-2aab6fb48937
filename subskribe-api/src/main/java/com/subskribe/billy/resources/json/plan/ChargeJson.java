package com.subskribe.billy.resources.json.plan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.productcatalog.model.LedgerAccountMapping;
import com.subskribe.billy.shared.annotations.ImportExportClass;
import com.subskribe.billy.shared.annotations.ImportExportField;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@ImportExportClass
@GraphQLName("Charge")
@ApiModel(description = "JSON object representing the charge details.")
public class ChargeJson extends ChargePartialJson implements NotifyingEvent {

    @ImportExportField(header = CatalogDomainColumns.PLAN_EXTERNAL_ID_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("planId")
    @ApiModelProperty(value = ChargeDocumentation.PLAN_ID)
    private String planId;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_AMOUNT_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("amount")
    @ApiModelProperty(value = ChargeDocumentation.AMOUNT)
    private BigDecimal amount;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_TYPE_HEADER)
    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("type")
    @GraphQLNonNull
    @ApiModelProperty(required = true, value = ChargeDocumentation.CHARGE_TYPE)
    private ChargeType type;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_MODEL_HEADER)
    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("chargeModel")
    @GraphQLNonNull
    @ApiModelProperty(required = true, value = ChargeDocumentation.CHARGE_MODEL)
    private ChargeModel chargeModel;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_MODEL_HEADER)
    @Valid
    @JsonProperty
    @GraphQLField
    @GraphQLName("recurrence")
    @ApiModelProperty(value = ChargeDocumentation.RECURRENCE)
    private RecurrenceJson recurrence;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_PRICE_TIER_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("priceTiers")
    @ApiModelProperty(value = ChargeDocumentation.PRICE_TIERS)
    private List<@GraphQLNonNull PriceTierJson> priceTiers;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_IS_DRAWDOWN_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("isDrawdown")
    @ApiModelProperty(value = ChargeDocumentation.IS_DRAWDOWN)
    private boolean isDrawdown;

    // todo: add to import/export
    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("minimumCommitBaseChargeId")
    @ApiModelProperty(value = ChargeDocumentation.MINIMUM_COMMIT_BASE_CHARGE_ID)
    private String minimumCommitBaseChargeId;

    // todo: add to import/export
    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("overageBaseChargeId")
    @ApiModelProperty(value = ChargeDocumentation.OVERAGE_BASE_CHARGE_ID)
    private String overageBaseChargeId;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_IS_CUSTOM_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("isCustom")
    @ApiModelProperty(value = ChargeDocumentation.IS_CUSTOM)
    private boolean isCustom;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_PERCENT_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("percent")
    @ApiModelProperty(value = ChargeDocumentation.PERCENT)
    private BigDecimal percent;

    // todo: add to import/export
    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("percentDerivedFrom")
    @ApiModelProperty(value = ChargeDocumentation.PERCENT_DERIVED_FROM)
    private PercentDerivedFrom percentDerivedFrom;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_PERCENT_TARGET_PLANS_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("targetPlanIds")
    @ApiModelProperty(value = ChargeDocumentation.TARGET_PLAN_IDS)
    private List<@GraphQLNonNull String> targetPlanIds;

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("ledgerAccountMapping")
    @ApiModelProperty(value = ChargeDocumentation.LEDGER_ACCOUNT_MAPPING)
    private LedgerAccountMapping ledgerAccountMapping;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_DURATION_IN_MONTHS_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("durationInMonths")
    @ApiModelProperty(value = ChargeDocumentation.DURATION_IN_MONTHS)
    private Long durationInMonths;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_IS_EVENT_BASED)
    @JsonProperty
    @GraphQLField
    @GraphQLName("isEventBased")
    @ApiModelProperty(value = ChargeDocumentation.IS_EVENT_BASED)
    private boolean isEventBased;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_IS_DISCOUNT)
    @JsonProperty
    @GraphQLField
    @GraphQLName("isDiscount")
    @ApiModelProperty(value = ChargeDocumentation.IS_DISCOUNT)
    private boolean isDiscount;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_RATE_CARD_NAME)
    @JsonProperty
    @GraphQLField
    @GraphQLName("rateCardId")
    @ApiModelProperty(value = ChargeDocumentation.RATE_CARD_ID)
    private String rateCardId;

    // todo: add to import/export
    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("billingTerm")
    @ApiModelProperty(value = ChargeDocumentation.BILLING_TERM)
    private BillingTerm billingTerm;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_BILLING_CYCLE_HEADER)
    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = ChargeDocumentation.BILLING_CYCLE)
    private BillingCycle billingCycle;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_SHOULD_TRACK_ARR)
    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = ChargeDocumentation.SHOULD_TRACK_ARR)
    private Boolean shouldTrackArr;

    @ImportExportField(ignore = true)
    @JsonProperty
    @ApiModelProperty(value = ChargeDocumentation.CUSTOM_FIELDS)
    private Map<String, CustomFieldValue> customFields;

    public ChargeJson() {
        customFields = new HashMap<>();
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public ChargeType getType() {
        return type;
    }

    public void setType(ChargeType type) {
        this.type = type;
    }

    public ChargeModel getChargeModel() {
        return chargeModel;
    }

    public void setChargeModel(ChargeModel chargeModel) {
        this.chargeModel = chargeModel;
    }

    public RecurrenceJson getRecurrence() {
        return recurrence;
    }

    public void setRecurrence(RecurrenceJson recurrenceJson) {
        recurrence = recurrenceJson;
    }

    public List<PriceTierJson> getPriceTiers() {
        return priceTiers;
    }

    public void setPriceTiers(List<PriceTierJson> priceTierJsons) {
        priceTiers = priceTierJsons;
    }

    public boolean isDrawdown() {
        return isDrawdown;
    }

    public void setDrawdown(boolean drawdown) {
        isDrawdown = drawdown;
    }

    public String getMinimumCommitBaseChargeId() {
        return minimumCommitBaseChargeId;
    }

    public void setMinimumCommitBaseChargeId(String minimumCommitBaseChargeId) {
        this.minimumCommitBaseChargeId = minimumCommitBaseChargeId;
    }

    public String getOverageBaseChargeId() {
        return overageBaseChargeId;
    }

    public void setOverageBaseChargeId(String overageBaseChargeId) {
        this.overageBaseChargeId = overageBaseChargeId;
    }

    public boolean isCustom() {
        return isCustom;
    }

    public void setCustom(boolean custom) {
        isCustom = custom;
    }

    public BigDecimal getPercent() {
        return percent;
    }

    public void setPercent(BigDecimal percent) {
        this.percent = percent;
    }

    public PercentDerivedFrom getPercentDerivedFrom() {
        return percentDerivedFrom;
    }

    public void setPercentDerivedFrom(PercentDerivedFrom percentDerivedFrom) {
        this.percentDerivedFrom = percentDerivedFrom;
    }

    public List<String> getTargetPlanIds() {
        return targetPlanIds;
    }

    public void setTargetPlanIds(List<String> targetPlanIds) {
        this.targetPlanIds = targetPlanIds;
    }

    public LedgerAccountMapping getLedgerAccountMapping() {
        return ledgerAccountMapping;
    }

    public void setLedgerAccountMapping(LedgerAccountMapping ledgerAccountMapping) {
        this.ledgerAccountMapping = ledgerAccountMapping;
    }

    public Long getDurationInMonths() {
        return durationInMonths;
    }

    public void setDurationInMonths(Long durationInMonths) {
        this.durationInMonths = durationInMonths;
    }

    public boolean isEventBased() {
        return isEventBased;
    }

    public void setEventBased(boolean eventBased) {
        isEventBased = eventBased;
    }

    public boolean getIsDiscount() {
        return isDiscount;
    }

    public void setIsDiscount(boolean isDiscount) {
        this.isDiscount = isDiscount;
    }

    public String getRateCardId() {
        return rateCardId;
    }

    public void setRateCardId(String rateCardId) {
        this.rateCardId = rateCardId;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public BillingCycle getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(BillingCycle billingCycle) {
        this.billingCycle = billingCycle;
    }

    public Boolean getShouldTrackArr() {
        return shouldTrackArr;
    }

    public void setShouldTrackArr(Boolean shouldTrackArr) {
        this.shouldTrackArr = shouldTrackArr;
    }

    public Map<String, CustomFieldValue> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(Map<String, CustomFieldValue> customFields) {
        this.customFields = customFields == null ? new HashMap<>() : customFields;
    }

    @Override
    public String getEventObjectId() {
        return getId();
    }

    @JsonIgnore
    public boolean isRecurring() {
        return type.isRecurring() || (type == ChargeType.PREPAID && getRecurrence() != null);
    }
}
