package com.subskribe.billy.resources.json.account;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Locale;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

@GraphQLName("AccountAddress")
public class AccountAddressJson {

    @JsonProperty
    @GraphQLField
    @GraphQLName("streetAddressLine1")
    @ApiModelProperty(value = "Address Line 1 of the Contact")
    private String streetAddressLine1;

    @JsonProperty
    @GraphQLField
    @GraphQLName("streetAddressLine2")
    @ApiModelProperty(value = "Address Line 2 of the Contact")
    private String streetAddressLine2;

    @JsonProperty
    @GraphQLField
    @GraphQLName("city")
    @ApiModelProperty(value = "City of the Contact")
    private String city;

    @JsonProperty
    @GraphQLField
    @GraphQLName("state")
    @ApiModelProperty(
        value = "State Code of the Contact (ISO 3166-2 state/province code). Currently supported for USA, Canada. For instance, for Arizona (USA), set state as AZ (not US-AZ).For British Columbia (Canada), set as BC (not CA-BC)"
    )
    private String state;

    @JsonProperty
    @GraphQLField
    @GraphQLName("country")
    @ApiModelProperty(value = "Country Code of the Contact ( ISO 3166 alpha-2 country code).")
    private String country;

    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = "Zip or Postal Code of the Contact")
    @GraphQLName("zipcode")
    private String zipcode;

    public AccountAddressJson() {}

    @GraphQLConstructor
    public AccountAddressJson(
        @GraphQLName("streetAddressLine1") @GraphQLNonNull String streetAddressLine1,
        @GraphQLName("streetAddressLine2") String streetAddressLine2,
        @GraphQLName("city") @GraphQLNonNull String city,
        @GraphQLName("state") String state,
        @GraphQLName("country") @GraphQLNonNull String country,
        @GraphQLName("zipcode") @GraphQLNonNull String zipcode
    ) {
        this.streetAddressLine1 = streetAddressLine1;
        this.streetAddressLine2 = streetAddressLine2;
        this.city = city;
        this.state = state;
        this.country = country;
        this.zipcode = zipcode;
    }

    public String getStreetAddressLine1() {
        return streetAddressLine1;
    }

    public void setStreetAddressLine1(String streetAddressLine1) {
        this.streetAddressLine1 = streetAddressLine1;
    }

    public String getStreetAddressLine2() {
        return streetAddressLine2;
    }

    public void setStreetAddressLine2(String streetAddressLine2) {
        this.streetAddressLine2 = streetAddressLine2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    @JsonIgnore
    public String getCountryName() {
        String countryCode = getCountry();
        if (StringUtils.isBlank(countryCode)) {
            return countryCode;
        }
        Locale country = new Locale(StringUtils.EMPTY, countryCode);
        return country.getDisplayName();
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountAddressJson that = (AccountAddressJson) o;
        return (
            Objects.equals(streetAddressLine1, that.streetAddressLine1) &&
            Objects.equals(streetAddressLine2, that.streetAddressLine2) &&
            Objects.equals(city, that.city) &&
            Objects.equals(state, that.state) &&
            Objects.equals(country, that.country) &&
            Objects.equals(zipcode, that.zipcode)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(streetAddressLine1, streetAddressLine2, city, state, country, zipcode);
    }
}
