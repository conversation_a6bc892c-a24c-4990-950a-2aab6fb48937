package com.subskribe.billy.resources.json.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns;
import com.subskribe.billy.shared.annotations.ImportExportClass;
import com.subskribe.billy.shared.annotations.ImportExportField;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.UUID;
import javax.validation.constraints.NotEmpty;

@ImportExportClass
@GraphQLName("ChargePartial")
public class ChargePartialJson {

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    private String id;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_NAME_HEADER)
    @NotEmpty
    @JsonProperty
    @GraphQLField
    @GraphQLName("name")
    @GraphQLNonNull
    private String name;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_DISPLAY_NAME_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("displayName")
    private String displayName;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_DESCRIPTION_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("description")
    private String description;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_TAX_RATE_CODE_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("taxRateId")
    private UUID taxRateId;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_UNIT_OF_MEASURE_NAME_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("unitOfMeasureId")
    private UUID unitOfMeasureId;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_IS_RENEWABLE_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("isRenewable")
    @GraphQLNonNull
    private boolean isRenewable;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_IS_CREDITABLE_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("isCreditable")
    private boolean isCreditable;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_IS_LIST_PRICE_EDITABLE)
    @JsonProperty
    @GraphQLField
    @GraphQLName("isListPriceEditable")
    private boolean isListPriceEditable;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_MIN_QUANTITY_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("minQuantity")
    private Long minQuantity;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_DEFAULT_QUANTITY_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("defaultQuantity")
    private Long defaultQuantity;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_MAX_QUANTITY_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("maxQuantity")
    private Long maxQuantity;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_EXTERNAL_ID_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    private String externalId;

    // todo: add to import/export when feature is enabled
    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("minAmount")
    private BigDecimal minAmount;

    // todo: add to import/export when feature is enabled
    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("maxAmount")
    private BigDecimal maxAmount;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_RECOGNITION_RULE_NAME_HEADER)
    @JsonProperty
    @GraphQLField
    private String recognitionRuleId;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_ERP_ID_HEADER)
    @JsonProperty
    @GraphQLField
    private String erpId;

    @ImportExportField(header = CatalogDomainColumns.CHARGE_ITEM_CODE_HEADER)
    @JsonProperty
    @GraphQLField
    private String itemCode;

    public ChargePartialJson() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UUID getTaxRateId() {
        return taxRateId;
    }

    public void setTaxRateId(UUID taxRateId) {
        this.taxRateId = taxRateId;
    }

    public UUID getUnitOfMeasureId() {
        return unitOfMeasureId;
    }

    public void setUnitOfMeasureId(UUID unitOfMeasureId) {
        this.unitOfMeasureId = unitOfMeasureId;
    }

    public boolean getIsRenewable() {
        return isRenewable;
    }

    public void setIsRenewable(boolean renewable) {
        isRenewable = renewable;
    }

    public boolean isCreditable() {
        return isCreditable;
    }

    public void setCreditable(boolean creditable) {
        isCreditable = creditable;
    }

    public Long getMinQuantity() {
        return minQuantity;
    }

    public void setMinQuantity(Long minQuantity) {
        this.minQuantity = minQuantity;
    }

    public Long getDefaultQuantity() {
        return defaultQuantity;
    }

    public void setDefaultQuantity(Long defaultQuantity) {
        this.defaultQuantity = defaultQuantity;
    }

    public Long getMaxQuantity() {
        return maxQuantity;
    }

    public void setMaxQuantity(Long maxQuantity) {
        this.maxQuantity = maxQuantity;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getRecognitionRuleId() {
        return recognitionRuleId;
    }

    public void setRecognitionRuleId(String recognitionRuleId) {
        this.recognitionRuleId = recognitionRuleId;
    }

    public String getErpId() {
        return erpId;
    }

    public void setErpId(String erpId) {
        this.erpId = erpId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public boolean getIsListPriceEditable() {
        return isListPriceEditable;
    }

    public void setIsListPriceEditable(boolean isListPriceEditable) {
        this.isListPriceEditable = isListPriceEditable;
    }
}
