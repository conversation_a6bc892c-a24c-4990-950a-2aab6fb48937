package com.subskribe.billy.resources.json.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.shared.annotations.ImportExportClass;
import com.subskribe.billy.shared.annotations.ImportExportField;
import graphql.annotations.annotationTypes.GraphQLDeprecate;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Set;
import javax.validation.constraints.NotEmpty;

/** A product or service. Decoupled from how the product is sold. */
@ImportExportClass
@GraphQLName("Product")
public class ProductJson implements NotifyingEvent {

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    @GraphQLNonNull
    @ApiModelProperty(value = "System-generated unique identifier for the product")
    private String id;

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("entityIds")
    @ApiModelProperty(value = "Set of entity IDs associated with this product")
    private Set<@GraphQLNonNull String> entityIds;

    @ImportExportField(header = CatalogDomainColumns.PRODUCT_NAME_HEADER)
    @NotEmpty
    @JsonProperty
    @GraphQLField
    @GraphQLName("name")
    @GraphQLNonNull
    @ApiModelProperty(required = true, value = "Unique name of the product")
    private String name;

    @ImportExportField(header = CatalogDomainColumns.PRODUCT_DISPLAY_NAME_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("displayName")
    @ApiModelProperty(value = "Display name of the product shown to customers")
    private String displayName;

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("inUse")
    @GraphQLNonNull
    @ApiModelProperty(value = "Indicates if the product is currently in use by any plans")
    private boolean inUse;

    @ImportExportField(header = CatalogDomainColumns.PRODUCT_DESCRIPTION_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("description")
    @ApiModelProperty(value = "Detailed description of the product")
    private String description;

    @ImportExportField(header = CatalogDomainColumns.PRODUCT_SKU_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("sku")
    @ApiModelProperty(required = true, value = "Stock Keeping Unit (SKU) for the product")
    private String sku;

    @ImportExportField(header = CatalogDomainColumns.PRODUCT_SKU_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("productCategoryId")
    @ApiModelProperty(value = "ID of the category this product belongs to")
    private String productCategoryId;

    @Deprecated
    @GraphQLDeprecate
    @ImportExportField(header = CatalogDomainColumns.PRODUCT_CATEGORY_NAME_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("productCategory")
    @ApiModelProperty(value = "Deprecated. Category information for this product", accessMode = ApiModelProperty.AccessMode.READ_ONLY)
    private ProductCategory productCategory;

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("updatedOn")
    @ApiModelProperty(value = "Timestamp of when the product was last updated (in seconds since epoch)")
    private Long updatedOn;

    @ImportExportField(header = CatalogDomainColumns.PRODUCT_EXTERNAL_ID_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    @ApiModelProperty(value = "External identifier for the product, used for integration with other systems")
    private String externalId;

    public ProductJson() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Set<String> getEntityIds() {
        return entityIds;
    }

    public void setEntityIds(Set<String> entityIds) {
        this.entityIds = entityIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isInUse() {
        return inUse;
    }

    public void setInUse(boolean inUse) {
        this.inUse = inUse;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getProductCategoryId() {
        return productCategoryId;
    }

    public void setProductCategoryId(String productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public ProductCategory getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(ProductCategory productCategory) {
        this.productCategory = productCategory;
    }

    public Long getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Long updatedOn) {
        this.updatedOn = updatedOn;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public String getEventObjectId() {
        return getId();
    }
}
