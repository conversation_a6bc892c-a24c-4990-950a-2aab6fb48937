package com.subskribe.billy.resources.json.order;

import com.subskribe.billy.shared.enums.CompositeOrderType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
public interface CompositeOrderJson {
    String getId();

    String getEntityId();

    CompositeOrderType getType();

    List<OrderJson> getOrders();

    OrderStatus getStatus();

    Optional<String> getCrmOpportunityId();

    Optional<String> getCrmOpportunityName();

    Optional<String> getCrmOpportunityStage();

    Optional<String> getCrmOpportunityType();

    Optional<Boolean> getIsPrimaryCompositeOrderForCrmOpportunity();

    Optional<UUID> getDocumentMasterTemplateId();

    Instant getCreatedOn();

    Instant getUpdatedOn();
}
