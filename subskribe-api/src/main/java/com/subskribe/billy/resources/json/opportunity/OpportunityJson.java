package com.subskribe.billy.resources.json.opportunity;

import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@GraphQLName("Opportunity")
public class OpportunityJson {

    @GraphQLField
    @GraphQLName("name")
    private String name;

    @GraphQLField
    @GraphQLName("id")
    private String id;

    @GraphQLField
    private String entityId;

    @GraphQLField
    @GraphQLName("type")
    private String type;

    @GraphQLField
    @GraphQLName("stage")
    private String stage;

    @GraphQLField
    @GraphQLName("crmId")
    private String crmId;

    @GraphQLField
    @GraphQLName("accountId")
    private String accountId;

    @GraphQLField
    @GraphQLName("createdOn")
    private Instant createdOn;

    @GraphQLField
    @GraphQLName("updatedOn")
    private Instant updatedOn;

    @GraphQLField
    @GraphQLName("opportunityCrmType")
    private OpportunityCrmType opportunityCrmType;

    @GraphQLField
    @GraphQLName("isClosed")
    private Boolean isClosed;

    @GraphQLField
    @GraphQLName("primaryOrderId")
    private String primaryOrderId;

    @GraphQLField
    @GraphQLName("opportunityId")
    private String opportunityId;

    @GraphQLField
    @GraphQLName("currency")
    private String currency;

    @GraphQLField
    @GraphQLName("customFields")
    private List<@GraphQLNonNull CustomFieldEntry> customFields;

    public OpportunityJson() {
        customFields = new ArrayList<>();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getCrmId() {
        return crmId;
    }

    public void setCrmId(String crmId) {
        this.crmId = crmId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Instant getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Instant createdOn) {
        this.createdOn = createdOn;
    }

    public Instant getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Instant updatedOn) {
        this.updatedOn = updatedOn;
    }

    public OpportunityCrmType getOpportunityCrmType() {
        return opportunityCrmType;
    }

    public void setOpportunityCrmType(OpportunityCrmType opportunityCrmType) {
        this.opportunityCrmType = opportunityCrmType;
    }

    public Boolean getIsClosed() {
        return isClosed;
    }

    public void setIsClosed(Boolean isClosed) {
        this.isClosed = isClosed;
    }

    public String getPrimaryOrderId() {
        return primaryOrderId;
    }

    public void setPrimaryOrderId(String primaryOrderId) {
        this.primaryOrderId = primaryOrderId;
    }

    public String getOpportunityId() {
        return opportunityId;
    }

    public void setOpportunityId(String opportunityId) {
        this.opportunityId = opportunityId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public List<CustomFieldEntry> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<CustomFieldEntry> customFields) {
        this.customFields = customFields == null ? List.of() : customFields;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OpportunityJson that = (OpportunityJson) o;
        return (
            Objects.equals(name, that.name) &&
            Objects.equals(id, that.id) &&
            Objects.equals(entityId, that.entityId) &&
            Objects.equals(type, that.type) &&
            Objects.equals(stage, that.stage) &&
            Objects.equals(crmId, that.crmId) &&
            Objects.equals(accountId, that.accountId) &&
            Objects.equals(createdOn, that.createdOn) &&
            Objects.equals(updatedOn, that.updatedOn) &&
            opportunityCrmType == that.opportunityCrmType &&
            Objects.equals(isClosed, that.isClosed) &&
            Objects.equals(primaryOrderId, that.primaryOrderId) &&
            Objects.equals(opportunityId, that.opportunityId) &&
            Objects.equals(currency, that.currency) &&
            Objects.equals(customFields, that.customFields)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            name,
            id,
            entityId,
            type,
            stage,
            crmId,
            accountId,
            createdOn,
            updatedOn,
            opportunityCrmType,
            isClosed,
            primaryOrderId,
            opportunityId,
            currency,
            customFields
        );
    }
}
