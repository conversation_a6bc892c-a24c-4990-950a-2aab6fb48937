package com.subskribe.billy.resources.json.taxrate;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.shared.pecuniary.Numbers;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@GraphQLName("TaxRate")
public class TaxRateJson {

    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    private UUID id;

    @JsonProperty
    @NotEmpty
    @GraphQLField
    @GraphQLName("name")
    @GraphQLNonNull
    private String name;

    @JsonProperty
    @GraphQLField
    @GraphQLName("description")
    private String description;

    @JsonProperty
    @GraphQLField
    @GraphQLName("taxPercentage")
    private BigDecimal taxPercentage;

    @JsonProperty
    @GraphQLField
    @GraphQLName("taxCode")
    private String taxCode;

    @JsonProperty
    @GraphQLField
    @GraphQLName("taxInclusive")
    @GraphQLNonNull
    private boolean taxInclusive;

    @JsonProperty
    @NotNull
    @GraphQLField
    @GraphQLName("status")
    @GraphQLNonNull
    private Status status;

    @JsonProperty
    @GraphQLField
    @GraphQLName("inUse")
    private Boolean inUse;

    public TaxRateJson() {}

    public TaxRateJson(
        UUID id,
        String name,
        String description,
        BigDecimal taxPercentage,
        String taxCode,
        boolean taxInclusive,
        Status status,
        Boolean inUse
    ) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.taxPercentage = taxPercentage;
        this.taxCode = taxCode;
        this.taxInclusive = taxInclusive;
        this.status = status;
        this.inUse = inUse;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(BigDecimal taxPercentage) {
        this.taxPercentage = taxPercentage;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public boolean isTaxInclusive() {
        return taxInclusive;
    }

    public void setTaxInclusive(boolean taxInclusive) {
        this.taxInclusive = taxInclusive;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Boolean isInUse() {
        return inUse;
    }

    public void setInUse(Boolean inUse) {
        this.inUse = inUse;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TaxRateJson that = (TaxRateJson) o;
        return (
            taxInclusive == that.taxInclusive &&
            Objects.equals(id, that.id) &&
            Objects.equals(name, that.name) &&
            Objects.equals(description, that.description) &&
            Numbers.equals(taxPercentage, that.taxPercentage) &&
            Objects.equals(taxCode, that.taxCode) &&
            status == that.status &&
            Objects.equals(inUse, that.inUse)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, description, Numbers.makeCalculationScale(taxPercentage), taxCode, taxInclusive, status, inUse);
    }
}
