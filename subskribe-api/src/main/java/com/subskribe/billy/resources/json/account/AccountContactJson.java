package com.subskribe.billy.resources.json.account;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.event.model.NotifyingEvent;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

@GraphQLName("AccountContact")
public class AccountContactJson implements NotifyingEvent {

    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    @ApiModelProperty(value = "This is a system-generated Account Contact ID")
    private String id;

    @JsonProperty
    @GraphQLField
    @GraphQLName("accountId")
    @ApiModelProperty(required = true, value = "Uniquely identifies the Account")
    private String accountId;

    @NotEmpty
    @JsonProperty
    @GraphQLField
    @GraphQLName("firstName")
    @GraphQLNonNull
    @ApiModelProperty(value = "First Name of the Contact")
    private String firstName;

    @JsonProperty
    @GraphQLField
    @GraphQLName("lastName")
    @ApiModelProperty(value = "(optional) Last Name of the Contact")
    private String lastName;

    @NotEmpty
    @JsonProperty
    @GraphQLField
    @GraphQLName("email")
    @GraphQLNonNull
    @ApiModelProperty(value = "Email of the Contact")
    private String email;

    @JsonProperty
    @GraphQLField
    @GraphQLName("phoneNumber")
    @ApiModelProperty(value = "(optional) Phone Number of the Contact")
    private String phoneNumber;

    @JsonProperty
    @GraphQLField
    @GraphQLName("title")
    @ApiModelProperty(value = "(optional) Title of the Contact")
    private String title;

    @Valid
    @JsonProperty
    @GraphQLField
    @GraphQLName("address")
    private AccountAddressJson address;

    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    private String externalId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("erpId")
    private String erpId;

    public AccountContactJson() {}

    @GraphQLConstructor
    public AccountContactJson(
        @GraphQLName("id") String id,
        @GraphQLName("accountId") String accountId,
        @GraphQLName("firstName") @GraphQLNonNull String firstName,
        @GraphQLName("lastName") String lastName,
        @GraphQLName("email") @GraphQLNonNull String email,
        @GraphQLName("phoneNumber") String phoneNumber,
        @GraphQLName("title") String title,
        @GraphQLName("address") AccountAddressJson address,
        @GraphQLName("externalId") String externalId,
        @GraphQLName("erpId") String erpId
    ) {
        this.id = id;
        this.accountId = accountId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.title = title;
        this.address = address;
        this.externalId = externalId;
        this.erpId = erpId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public AccountAddressJson getAddress() {
        return address;
    }

    public void setAddress(AccountAddressJson address) {
        this.address = address;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getErpId() {
        return erpId;
    }

    public void setErpId(String erpId) {
        this.erpId = erpId;
    }

    public String getFullName() {
        return String.format("%s %s", firstName, lastName).trim();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountContactJson that = (AccountContactJson) o;
        return (
            Objects.equals(id, that.id) &&
            Objects.equals(accountId, that.accountId) &&
            Objects.equals(firstName, that.firstName) &&
            Objects.equals(lastName, that.lastName) &&
            Objects.equals(email, that.email) &&
            Objects.equals(phoneNumber, that.phoneNumber) &&
            Objects.equals(title, that.title) &&
            Objects.equals(address, that.address) &&
            Objects.equals(externalId, that.externalId) &&
            Objects.equals(erpId, that.erpId)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, accountId, firstName, lastName, email, phoneNumber, title, address, externalId, erpId);
    }

    @Override
    public String getEventObjectId() {
        return id;
    }
}
