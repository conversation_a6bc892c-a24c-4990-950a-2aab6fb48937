package com.subskribe.billy.resources.json.taxrate;

import com.fasterxml.jackson.annotation.JsonProperty;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.Objects;
import java.util.UUID;
import javax.validation.constraints.NotEmpty;

@GraphQLName("TaxRateStrategy")
public class TaxRateStrategyJson {

    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    private String id;

    @JsonProperty
    @NotEmpty
    @GraphQLField
    @GraphQLName("name")
    @GraphQLNonNull
    private String name;

    @JsonProperty
    @GraphQLField
    @GraphQLName("countryCode")
    @GraphQLNonNull
    private String countryCode;

    @JsonProperty
    @GraphQLField
    @GraphQLName("taxRateId")
    @GraphQLNonNull
    private UUID taxRateId;

    public TaxRateStrategyJson() {}

    public TaxRateStrategyJson(String id, String name, String countryCode, UUID taxRateId) {
        this.id = id;
        this.name = name;
        this.countryCode = countryCode;
        this.taxRateId = taxRateId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public UUID getTaxRateId() {
        return taxRateId;
    }

    public void setTaxRateId(UUID taxRateId) {
        this.taxRateId = taxRateId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TaxRateStrategyJson that = (TaxRateStrategyJson) o;
        return (
            Objects.equals(id, that.id) &&
            Objects.equals(name, that.name) &&
            Objects.equals(countryCode, that.countryCode) &&
            Objects.equals(taxRateId, that.taxRateId)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, countryCode, taxRateId);
    }
}
