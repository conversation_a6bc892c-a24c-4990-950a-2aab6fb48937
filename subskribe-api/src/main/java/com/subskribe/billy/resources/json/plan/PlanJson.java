package com.subskribe.billy.resources.json.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.shared.annotations.ImportExportClass;
import com.subskribe.billy.shared.annotations.ImportExportField;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

@ImportExportClass
@GraphQLName("Plan")
@ApiModel(description = "A plan is a collection of charges that dictates how a product is priced.")
public class PlanJson implements NotifyingEvent {

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    @ApiModelProperty(value = "System-generated unique identifier for the plan", readOnly = true)
    private String id;

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("entityIds")
    @ApiModelProperty(value = "Array of entity IDs associated with this plan")
    private Set<@GraphQLNonNull String> entityIds;

    @ImportExportField(header = CatalogDomainColumns.PLAN_NAME_HEADER)
    @NotEmpty
    @JsonProperty
    @GraphQLField
    @GraphQLName("name")
    @GraphQLNonNull
    @ApiModelProperty(required = true, value = "Unique name of the plan")
    private String name;

    @ImportExportField(header = CatalogDomainColumns.PLAN_DISPLAY_NAME_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("displayName")
    @ApiModelProperty(value = "Display name of the plan shown to customers")
    private String displayName;

    @ImportExportField(header = CatalogDomainColumns.PLAN_DESCRIPTION_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("description")
    @ApiModelProperty(value = "Detailed description of the plan")
    private String description;

    @ImportExportField(ignore = true)
    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("status")
    @GraphQLNonNull
    @ApiModelProperty(
        required = true,
        value = "Status of the plan: `DRAFT`, `ACTIVE`, `GRANDFATHERED`, `ARCHIVED`, or `DEPRECATED`. Set the status to `DRAFT` when creating a new plan."
    )
    private PlanStatus status;

    @ImportExportField(ignore = true)
    @NotEmpty
    @JsonProperty
    @GraphQLField
    @GraphQLName("productId")
    @GraphQLNonNull
    @ApiModelProperty(required = true, value = "ID of the product this plan is associated with")
    private String productId;

    @ImportExportField(ignore = true)
    @Valid
    @JsonProperty
    @GraphQLField
    @GraphQLName("charges")
    @GraphQLNonNull
    @ApiModelProperty(required = true, value = "Array of charges associated with this plan")
    private List<@GraphQLNonNull ChargeJson> charges;

    @ImportExportField(header = CatalogDomainColumns.PLAN_CURRENCY_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("currency")
    @ApiModelProperty(value = "Currency code for this plan (defaults to system default if not specified)")
    private String currency;

    @ImportExportField(header = CatalogDomainColumns.PLAN_EXTERNAL_ID_HEADER)
    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    @ApiModelProperty(value = "External identifier for the plan, used for integration with other systems")
    private String externalId;

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("templateIds")
    @ApiModelProperty(value = "List of template IDs associated with this plan")
    private List<String> templateIds;

    // todo: add to import/export
    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = "IDs of plans that can replace this plan during upgrades/downgrades")
    private List<String> replacementPlanIds;

    // todo: add to import/export
    @ImportExportField(ignore = true)
    @JsonProperty
    @ApiModelProperty(value = "Map of custom fields associated with this plan")
    private Map<String, CustomFieldValue> customFields;

    @ImportExportField(ignore = true)
    @JsonProperty
    @GraphQLField
    @GraphQLName("updatedOn")
    @ApiModelProperty(value = "Timestamp of when the plan was last updated (in seconds since epoch)")
    private Long updatedOn;

    public PlanJson() {
        entityIds = new HashSet<>();
        charges = new ArrayList<>();
        customFields = new HashMap<>();
    }

    public void addCharge(ChargeJson chargeJson) {
        charges.add(chargeJson);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Set<String> getEntityIds() {
        return entityIds;
    }

    public void setEntityIds(Set<String> entityIds) {
        this.entityIds = entityIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PlanStatus getStatus() {
        return status;
    }

    public void setStatus(PlanStatus status) {
        this.status = status;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public List<ChargeJson> getCharges() {
        return charges;
    }

    public void setCharges(List<ChargeJson> chargeJsons) {
        charges = chargeJsons;
    }

    public String getCurrency() {
        if (StringUtils.isBlank(currency)) {
            return SupportedCurrency.getDefaultCurrency().getCurrencyCode();
        }
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public List<String> getTemplateIds() {
        return templateIds;
    }

    public void setTemplateIds(List<String> templateIds) {
        this.templateIds = templateIds;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Map<String, CustomFieldValue> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(Map<String, CustomFieldValue> customFields) {
        this.customFields = customFields == null ? new HashMap<>() : customFields;
    }

    public Long getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Long updatedOn) {
        this.updatedOn = updatedOn;
    }

    public List<String> getReplacementPlanIds() {
        return replacementPlanIds;
    }

    public void setReplacementPlanIds(List<String> replacementPlanIds) {
        this.replacementPlanIds = replacementPlanIds;
    }

    @Override
    public String getEventObjectId() {
        return getId();
    }
}
