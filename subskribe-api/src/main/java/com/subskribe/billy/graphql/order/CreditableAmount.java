package com.subskribe.billy.graphql.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.shared.pecuniary.Numbers;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.Objects;

@GraphQLName("CreditableAmount")
public class CreditableAmount {

    @JsonProperty
    @GraphQLField
    @GraphQLNonNull
    @GraphQLName("subscriptionChargeId")
    private String subscriptionChargeId;

    @JsonProperty
    @GraphQLField
    @GraphQLNonNull
    @GraphQLName("creditableAmount")
    private BigDecimal creditableAmount;

    @JsonProperty
    @GraphQLField
    @GraphQLName("maxCreditableAmount")
    private BigDecimal maxCreditableAmount;

    public String getSubscriptionChargeId() {
        return subscriptionChargeId;
    }

    public void setSubscriptionChargeId(String subscriptionChargeId) {
        this.subscriptionChargeId = subscriptionChargeId;
    }

    public BigDecimal getCreditableAmount() {
        return creditableAmount;
    }

    public void setCreditableAmount(BigDecimal creditableAmount) {
        this.creditableAmount = creditableAmount;
    }

    public BigDecimal getMaxCreditableAmount() {
        return maxCreditableAmount;
    }

    public void setMaxCreditableAmount(BigDecimal maxCreditableAmount) {
        this.maxCreditableAmount = maxCreditableAmount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreditableAmount that = (CreditableAmount) o;
        return (
            Objects.equals(subscriptionChargeId, that.subscriptionChargeId) &&
            Numbers.equals(creditableAmount, that.creditableAmount) &&
            Numbers.equals(maxCreditableAmount, that.maxCreditableAmount)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            subscriptionChargeId,
            creditableAmount == null ? null : creditableAmount.stripTrailingZeros(),
            maxCreditableAmount == null ? null : maxCreditableAmount.stripTrailingZeros()
        );
    }
}
