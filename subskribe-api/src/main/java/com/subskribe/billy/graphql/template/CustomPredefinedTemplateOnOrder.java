package com.subskribe.billy.graphql.template;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.Objects;

@GraphQLName("CustomPredefinedTemplateOnOrder")
public class CustomPredefinedTemplateOnOrder {

    @JsonProperty("id")
    @GraphQLField
    private String id;

    @JsonProperty("orderId")
    @GraphQLField
    private String orderId;

    @JsonProperty("name")
    @GraphQLField
    @GraphQLNonNull
    private String name;

    @JsonProperty("description")
    @GraphQLField
    private String description;

    @JsonProperty("content")
    @GraphQLField
    @GraphQLNonNull
    private String content;

    @JsonCreator
    @GraphQLConstructor
    @GraphQLNonNull
    public CustomPredefinedTemplateOnOrder(
        @JsonProperty("id") @GraphQLName("id") String id,
        @JsonProperty("orderId") @GraphQLName("orderId") String orderId,
        @JsonProperty("name") @GraphQLName("name") String name,
        @JsonProperty("description") @GraphQLName("description") String description,
        @JsonProperty("content") @GraphQLName("content") String content
    ) {
        this.id = id;
        this.orderId = orderId;
        this.name = name;
        this.description = description;
        this.content = content;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomPredefinedTemplateOnOrder that = (CustomPredefinedTemplateOnOrder) o;
        return (
            Objects.equals(id, that.id) &&
            Objects.equals(orderId, that.orderId) &&
            Objects.equals(name, that.name) &&
            Objects.equals(description, that.description) &&
            Objects.equals(content, that.content)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, orderId, name, description, content);
    }
}
