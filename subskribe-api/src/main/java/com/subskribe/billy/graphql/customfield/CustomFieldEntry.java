package com.subskribe.billy.graphql.customfield;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.customfield.model.CustomFieldDefault;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.List;
import javax.validation.constraints.NotBlank;

@GraphQLName("CustomFieldEntry")
public class CustomFieldEntry extends CustomFieldValue {

    @JsonProperty
    @NotBlank
    @GraphQLField
    @GraphQLNonNull
    private final String id;

    @JsonCreator
    @GraphQLConstructor
    public CustomFieldEntry(
        @GraphQLName("id") @JsonProperty("id") String id,
        @GraphQLName("type") @JsonProperty("type") CustomFieldType type,
        @GraphQLName("name") @JsonProperty("name") String name,
        @GraphQLName("label") @JsonProperty("label") String label,
        @GraphQLName("value") @JsonProperty("value") String value,
        @GraphQLName("selections") @JsonProperty("selections") List<String> selections,
        @GraphQLName("options") @JsonProperty("options") List<String> options,
        @GraphQLName("required") @JsonProperty("required") Boolean required,
        @GraphQLName("source") @JsonProperty("source") CustomFieldSource source,
        @GraphQLName("defaultValue") @JsonProperty("defaultValue") CustomFieldDefault defaultValue
    ) {
        super(type, name, label, value, selections, options, required, source, defaultValue);
        this.id = id;
    }

    public static CustomFieldValue toValue(CustomFieldEntry customFieldEntry) {
        return new CustomFieldValue(
            customFieldEntry.getType(),
            customFieldEntry.getName(),
            customFieldEntry.getLabel(),
            customFieldEntry.getValue(),
            customFieldEntry.getSelections(),
            customFieldEntry.getOptions(),
            customFieldEntry.isRequired(),
            customFieldEntry.getSource(),
            customFieldEntry.getDefaultValue()
        );
    }

    public String getId() {
        return id;
    }

    public static CustomFieldEntry fromValue(String id, CustomFieldValue customFieldValue) {
        return new CustomFieldEntry(
            id,
            customFieldValue.getType(),
            customFieldValue.getName(),
            customFieldValue.getLabel(),
            customFieldValue.getValue(),
            customFieldValue.getSelections(),
            customFieldValue.getOptions(),
            customFieldValue.isRequired(),
            customFieldValue.getSource(),
            customFieldValue.getDefaultValue()
        );
    }

    public static CustomFieldEntry fromDefaultValue(String id, CustomFieldValue customFieldValue) {
        if (customFieldValue.getDefaultValue() == null) {
            return fromValue(id, customFieldValue);
        }

        return new CustomFieldEntry(
            id,
            customFieldValue.getType(),
            customFieldValue.getName(),
            customFieldValue.getLabel(),
            customFieldValue.getDefaultValue().value(),
            customFieldValue.getDefaultValue().selections(),
            customFieldValue.getOptions(),
            customFieldValue.isRequired(),
            customFieldValue.getSource(),
            customFieldValue.getDefaultValue()
        );
    }
}
