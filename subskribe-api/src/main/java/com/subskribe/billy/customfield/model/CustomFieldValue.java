package com.subskribe.billy.customfield.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.validation.Validator;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.List;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;

@GraphQLName("CustomFieldValue")
public class CustomFieldValue {

    @JsonProperty
    @GraphQLField
    @GraphQLNonNull
    private final String name;

    @JsonProperty
    @GraphQLField
    private final String label;

    @JsonProperty
    @GraphQLField
    private final String value;

    @JsonProperty
    @GraphQLField
    private final List<String> selections;

    @JsonProperty
    @GraphQLField
    private final List<String> options;

    @JsonProperty
    @GraphQLField
    private final CustomFieldType type;

    @JsonProperty
    @GraphQLField
    private final Boolean required;

    @JsonProperty
    @GraphQLField
    private final CustomFieldSource source;

    @JsonProperty
    @GraphQLField
    private final CustomFieldDefault defaultValue;

    @JsonCreator
    @GraphQLConstructor
    public CustomFieldValue(
        @GraphQLName("type") @JsonProperty("type") CustomFieldType type,
        @GraphQLName("name") @JsonProperty("name") String name,
        @GraphQLName("label") @JsonProperty("label") String label,
        @GraphQLName("value") @JsonProperty("value") String value,
        @GraphQLName("selections") @JsonProperty("selections") List<String> selections,
        @GraphQLName("options") @JsonProperty("options") List<String> options,
        @GraphQLName("required") @JsonProperty("required") Boolean required,
        @GraphQLName("source") @JsonProperty("source") CustomFieldSource source,
        @GraphQLName("defaultValue") @JsonProperty("defaultValue") CustomFieldDefault defaultValue
    ) {
        this.type = type;
        this.name = name;
        this.label = label;
        this.value = value;
        this.selections = selections == null ? List.of() : selections;
        this.options = options;
        this.required = required;
        this.source = source;
        this.defaultValue = defaultValue;
    }

    public CustomFieldType getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        if (type == null) {
            return value;
        }

        return switch (type) {
            case STRING -> value;
            case PICKLIST -> {
                // in the case of picklist type, populate value from the selected value in selections list if value is null.
                if (CollectionUtils.isEmpty(selections)) {
                    yield null;
                } else {
                    yield selections.get(0);
                }
            }
            case MULTISELECT_PICKLIST -> {
                if (CollectionUtils.isEmpty(selections)) {
                    yield null;
                } else {
                    yield String.join("; ", selections);
                }
            }
        };
    }

    public List<String> getSelections() {
        return selections;
    }

    public List<String> getOptions() {
        return options;
    }

    public Boolean isRequired() {
        return required;
    }

    public CustomFieldSource getSource() {
        return source;
    }

    public CustomFieldDefault getDefaultValue() {
        return defaultValue;
    }

    public static CustomFieldValue fromDefinitionWithValueAndSelections(CustomFieldDefinition definition, String value, List<String> selections) {
        Validator.checkNonNullInternal(definition, "Custom field definition cannot be null when converting to value");

        return new CustomFieldValue(
            definition.getFieldType(),
            definition.getFieldName(),
            definition.getFieldLabel(),
            value,
            selections,
            definition.getOptions(),
            definition.isRequired(),
            definition.getSource(),
            definition.getDefaultValue()
        );
    }

    /**
     * <p>Method that creates a new {@link CustomFieldValue} based on definition and a list of canonically serialized values</p>
     * <ul>
     *     <li>The {@link CustomFieldDefinition#getFieldType()} is taken into account while making the value</li>
     *     <li>the list of values will be converted to the canonical type and suitable field will be set in the value</li>
     *     <li>if this conversion is not possible then a empty optional is returned</li>
     *     <li>TODO: in the future this method will accommodate negotiating more types</li>
     * </ul>
     * @param definition the definition of the custom field this <b>CANNOT BE NULL</b>
     * @param values the list of canonically serialized values for the custom field
     * @return optional of {@link CustomFieldValue}, optional will be present if the value can be constructed empty otherwise
     */
    public static Optional<CustomFieldValue> fromDefinitionWithValueAndSelections(CustomFieldDefinition definition, List<String> values) {
        if (CollectionUtils.isEmpty(values)) {
            return Optional.empty();
        }
        List<String> allowedValues = CollectionUtils.isEmpty(definition.getOptions()) ? List.of() : definition.getOptions();
        switch (definition.getFieldType()) {
            case STRING -> {
                if (values.size() == 1) {
                    return Optional.of(fromDefinitionWithValueAndSelections(definition, values.get(0), List.of()));
                }
            }
            case PICKLIST -> {
                if (values.size() == 1 && allowedValues.contains(values.get(0))) {
                    return Optional.of(fromDefinitionWithValueAndSelections(definition, values.get(0), values));
                }
            }
            case MULTISELECT_PICKLIST -> {
                List<String> setValues = values.stream().filter(allowedValues::contains).toList();
                return Optional.of(fromDefinitionWithValueAndSelections(definition, null, setValues));
            }
        }
        return Optional.empty();
    }
}
