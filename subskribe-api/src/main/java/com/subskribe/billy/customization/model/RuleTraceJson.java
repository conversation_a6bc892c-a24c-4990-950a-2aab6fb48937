package com.subskribe.billy.customization.model;

import graphql.annotations.annotationTypes.GraphQLField;
import java.util.List;

public record RuleTraceJson(
    @GraphQLField String ruleName,
    @GraphQL<PERSON><PERSON> <PERSON> fired,
    @GraphQLField List<RuleActionPerformedJson> orderActionsPerformed,
    @GraphQLField List<OrderLineActionsPerformedJson> orderLineActionsPerformed,
    @GraphQLField List<String> orderRuleWarnings,
    @GraphQLField List<OrderLineRuleWarningsJson> orderLineRuleWarnings
) {}
