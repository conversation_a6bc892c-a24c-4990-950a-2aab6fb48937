# billy

billy powers your bills  
bills power green backs  
green backs power everything man!!!

## Table of Contents 
1. [Java Setup](#java_setup)
2. [IntelliJ Setup](#intellij_setup)
3. [Running Billy Backend](#running_billy_backend)
4. [Running e2e tests](#e2e_tests)
5. [Add Localstack Secrets](#add_local_stack_secrets)
6. [Add new Localstack AWS resources](#new_local_stack_resources)
7. [Errors and Resolutions](#errors_and_resolutions)
8. [Feature Flag Development ](#feature_flag)
9. [<PERSON> Engineer access](#billy_engineer_access)
10. [Further Reading and Resources](#additional_resources)

## Java Setup <a id="java_setup"></a>
The following instructions are to get up and running with the billy application on your personal machine.
The instructions are currently for macOS but many of the steps are likely applicable for Windows as well.
1. Download and install [brew](https://brew.sh/), <mark>There might be an existing homebrew installation but not added to your PATH in mac. Try running `/opt/homebrew/bin/brew doctor` for an output. Followed by, `echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc` to add it to your PATH</mark>
2. Install Java JDK (currently version 17)
   - `brew install openjdk@17`
   - `sudo ln -sfn /usr/local/opt/openjdk@17/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-17.jdk`
   - Verify successful install with: `java -version`
   - Check java install location: `which java`
3. Install maven (currently version 3.8.7 or greater)
   - `brew install maven`
   - Find java home `/usr/libexec/java_home -V`
   - In your `.bashrc` or `.zshrc` set: `export JAVA_HOME=<path>`(If the file is not present, please create .zshrc)
   - Check `mvn --version` uses the correct jdk
4. Install node `brew install node` for making the code [spotless](https://github.com/diffplug/spotless)
5. Install python `brew install python@3.8`
6. Download [Docker](https://docs.docker.com/docker-for-mac/install/)
7. Before setting AWS credentials please ask in the slack channel `#engineering` about the env variable `LOCAL_STACK_ENCRYPTION_KEY` this key can be provided to you by any one of the devs, this env variable needs to be set in your `.bashrc` or `.zshrc`, having this variable is needed for billy development

**_Step 8 and 9 may be required only for UI development and access may be needed only for Cognito_**
8. Setup aws cli 
   - `brew install awscli`
   - Run `aws configure` to set default profile. You will need key ID and secret from [AWS console credentials page](https://console.aws.amazon.com/iam/home?#/security_credentials)
   - The above should generate `~/.aws` folder with config and credentials files. In your credentials file the profile generated would be `[default]`, replace it with `[dmz]`
9. You will also need to setup AWS permissions with one of our AWS administrators. Ask in `#engineering`
10.  Set maven credentials, especially for first build, <mark>Remember to reload your .zshrc or .bashrc files by using `source .<file>` to apply the updates!</mark>:
     ```
     export CODEARTIFACT_AUTH_TOKEN=`AWS_PROFILE=dmz aws codeartifact get-authorization-token --domain subskribe-domain --domain-owner 054887729006 --region us-east-2 --query authorizationToken --output text`
     export JOOQ_AUTH_TOKEN=`AWS_PROFILE=dev-admin aws secretsmanager get-secret-value --secret-id LOCAL_JOOQ_AUTH_TOKEN --query SecretString --output text`
     ```
> You can also use jEnv to setup java environment, follow instructions found [here](https://docs.google.com/document/d/16v2jpl8BeTE9gbktMqQsGcZagHo53iau8itzIlKaAvg/edit?usp=sharing)

## IntelliJ Setup <a id="intellij_setup"></a>
1. Clone [billy repo](https://github.com/Subskribe/billy) 
2. Download [IntelliJ IDE](https://www.jetbrains.com/idea/)
3. Set style: google-java-format plugin
   - IntelliJ IDEA -> preferences -> plugins -> marketplace
   - Enable the plugin
   - Restart IntelliJ
   - IntelliJ IDEA -> preferences -> other settings -> google-java-format settings -> code style = Android Open Source Project (AOSP) style
4. Import application root folder (open `billy/`)
5. In maven tab (right hand side), click on reload all maven projects icon. <mark>Note : IntelliJ won't be able to build the project on first try. Please build the project using `make` command for the first time.</mark> <b>Also, make sure to use the auto-detected maven settings file under /billy/.mvn/maven.config. This is important as the config file points to infra/maven_settings.xml which contains the necessary setup to resolve dependencies for jooq-pro and subskribe-docusign-esign, without which you will see the below error</b> ![image](https://github.com/Subskribe/billy/assets/175025609/83e05bfc-8bbd-4d28-9987-80b62cb7f85b)
 <img width="1512" alt="image" src="https://github.com/Subskribe/billy/assets/175025609/1b4ed67d-a17d-4228-9b91-3b06ab852e22">
 
6. Configure build + run + debug
   - Run -> edit configurations… 
   - Click on + -> application 
   - Make sure the project module is selected 
   - Main class should be the BillyApplication class 
   - Program arguments: server config-local.conf
   - please set the following env variables for the run configuration `AWS_ACCESS_KEY_ID=fake;AWS_SECRET_ACCESS_KEY=fake` so that you never use any AWS secrets to run billy 
   - click on modify options, then add vm arguments
   - add the following vm argument: `--add-opens java.base/java.time=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/sun.util.calendar=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED  --add-exports java.base/sun.util.calendar=ALL-UNNAMED`
   - For additional info see [how to run and debug in IntelliJ](https://www.jetbrains.com/help/idea/debug-a-java-application-using-a-dockerfile.html#create-dockerfile-run-config)
7. Add source files for jooq
   - run `make build` - This will generate the libraries necessary
   - Select `File -> Project Structure`
   - Go to `Modules` and click `Add Content Root`
   - Navigate to `billy/target/generated-sources/jooq` and click `open`
8. For local DB access
   - Click Database on right side of IntelliJ
   - Add Postgres DB with username `billy` and password `password`
   - Use database name as `billy-db`
   - Open a query console to run queries on the DB.

## Running billy backend <a id="running_billy_backend"></a>
<mark>Run commands from `billy/` and the full list of commands can be found in the `Makefile`</mark>

<b>Prerequisite</b>

External dependencies set up

1. `make run_docker_compose_local_up` - Runs the local docker containers for external services (e.g. postgres, s3, etc.)
2. `make run_docker_compose_local_down` - Brings down docker services when you are done with running the application
***


1. Execute `make clean_start_local_stack` to start billy with all features enabled from a clean build. This will:
   1. clean and build billy
   2. update SQL schema
   3. create ES indices
   4. run billy with most features enabled
  
<b>OR</b>

2. Alternatively, steps can be executed individually:
   1. `make build` - Builds billy
   2. `make db_migrate` - Creates and updates the tables in the db
   3. `make add_index_template` - Creates Elasticsearch indices
   4. `make run` OR `make run_local_ui_dev` - Run billy backend with basic features or with features needed for local UI development enabled
   5. `make spotless` - Cleanup the code to conform to style guidelines
  
## Testing application startup <a id="test_application"></a>
Once the application has started you can try hitting the below endpoints to verify

1. GET http://localhost:8080/health
2. GET http://localhost:8080/health/startup

## e2e Tests <a id="e2e_tests"></a>
End-to-end tests (e2e_tests) are how we test billy backend code. The billy service must be running for the tests to work.
1. `cd billy/e2e_tests`
2. Run `make` to clean, build, and run e2e_tests
3. To set up in IntelliJ
  - Open e2e_tests in IntelliJ workspace
  - Configure with JUnit
  - Run tests

## Running billy UI <a id="running_billy_ui"></a>
1. run `make add_index_template`
2. When running the backend, you can set `LOCAL_DOCUMENT_GEN_ENABLED=false` as an environment variable if you do not want to generate documents, however this is no longer necessary since most documents are generated on request now, so leaving the default value as `true` should not impact performance. 
3. Note: for other configs, you can either define your own environment variables or change `config-local.conf`
4. run `pnpm local` on billy ui
```
username: <EMAIL>
password: Subskribe123!
```

## Add secrets to local stack <a id="add_local_stack_secrets"></a>
1. Decrypt local secrets using `make local_secrets_decrypt`, when this command runs successfully the file in `localstack/local_secrets.json` will be decrypted. You will be able to see all the local secrets tin the file which will be of the form shown below, it is just a JSON map of secret name to value
```json
{
   "LOCAL_CI_DATABASE_SECRET": "<secret_value>",
   "LAUNCH_DARKLY_SDK_KEY": "<secret_value>",
   "SUBSKRIBE_STRIPE_API_KEY_SECRET": "<secret_value>",
   ...
}
```
2. Once you have added or removed your secret all you have to do is run `make local_secrets_encrypt`, which will encrypt the secret file, the file now will look like this
```json
{"localstack_secrets": "<base 64 encoded encrypted bytes of the file above"}
```

3. after adding and encrypting the secret file you will have to run `make local_resources` which will add the secret to the instance of localstack running

🛑 for all of these operations you need `LOCAL_STACK_ENCRYPTION_KEY` in your environment 🛑

## Add new AWS resources to local stack <a id="new_local_stack_resources"></a>
interaction with the local stack is achieved by implementing the `command` pattern provided by dropwizard
* please look at the class `LocalStackCommand` which supports basically all the localstack command operations including `encrypt`, `decrypt` and `resources`
* if you want to add a new AWS resources that you want to create locally please take a look at `ResourceCreator` in the `localstack` package in billy (same package as `LocalStackCommand`)
* the entire `BillyConfiguration` is available in this class as well, just fire up the correct AWS client and create resources in local stack, look at S3 and secrets service examples
* 🛑 NOTE: please create the resources in an idempotent way (i.e. create if not exists) this will make sure that we can run the command `make local_resources` several times without worry 🛑

### Errors and Resolutions <a id="errors_and_resolutions"></a>
1. If getting repeated errors after downloading a new patch, try running make `db_migrate` to make sure DB is up to date
2. Postgres still running
   - If you see something like `Error response from daemon: Ports are not available: listen tcp 0.0.0.0:5432: bind: address already in use
     ` when trying to bring up docker, it is because a postgres instance is already running
   - Run `sudo lsof -i -n -P | grep 5432` to get postgres pid
   - Run `sudo kill -i <pid>` to kill postgres instance
3. If you get an error that looks like `cannot delete network` when docker composing up or down, then please run `docker container prune` this will prune all resources and let you start containers with a clean slate

## Feature Flag Development <a id="feature_flag"></a>

While using feature flags, please make sure to develop with both states of feature flag.
On local environment, feature flags are easy to add and manage:
1. make sure `AWS_APP_CONFIG_ENABLED` environment variable is NOT set
2. edit `common-local.conf` and look for `dynamicFeatures` map
3. add the key of your feature flag in this map
4. before build / run, just set it to true or false to turn on or off the feature flag
5. add feature flag to [Feature](src/main/java/com/subskribe/billy/configuration/dynamic/Feature.java) enum
6. test if the feature is enabled by calling [FeatureService](src/main/java/com/subskribe/billy/configuration/dynamic/FeatureService.java)::isEnabled method.

## Billy Engineer access in production <a id="billy_engineer_access"></a>

Please reach out to #engineering channel in slack to get set up with a user that has `BILLY_ENGINEER`
access in production.

### Additional Resources <a id="additional_resources"></a>

- [Onboarding walkthrough](https://docs.google.com/presentation/d/1qcuwxpDZqwikjWpb8rJXbn8EY9ouzlpbJIv1GjPoD8w/edit?usp=sharing)
- [Google Doc setup guide](https://drive.google.com/a/subskribe.com/open?id=16v2jpl8BeTE9gbktMqQsGcZagHo53iau8itzIlKaAvg)
- [Notion setup guide](https://www.notion.so/subskribe/Billy-Setup-3d3fcb031efe4ceb91ad52b534aafaf0)
- [Billy UI](https://github.com/Subskribe/billy-ui-ng)
- [swagger](http://localhost:8080/swagger)
- [Subskribe Quick-Start Guide](https://docs.google.com/document/d/1yMRV37Cy1UUBgIEr2Gdo7FRfMEMpylPaCRRwZJPjtMM/)
- [Revenue Recognition User Guide](https://docs.google.com/document/d/1RfH_thqfkp2E5NxmSVkFuKzTAdPZtxxvj_C6DuE2uN8/)
