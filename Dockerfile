# ------ JDK BASE ------
ARG JDK_VERSION="17.0.14_7-jdk"
FROM eclipse-temurin:${JDK_VERSION} as jdk-base

# ------ COMMON BASE ------
FROM debian:bookworm-20250317-slim as common-base

RUN mkdir /app
WORKDIR /app
COPY infra/docker/*.sh /usr/local/bin

ENV JAVA_HOME=/opt/java/openjdk
COPY --from=jdk-base $JAVA_HOME $JAVA_HOME
ENV PATH="${JAVA_HOME}/bin:${PATH}"

# ------ BUILD BASE ------
FROM common-base as build-base
RUN capture-verbose.sh build-base install-buildtime-packages.sh

# ------ BUILD DEPS ------
FROM build-base as build-deps
RUN capture-verbose.sh download-dd-agent download-dd-agent.sh


# ------ PROD ------
FROM common-base as prod

EXPOSE 8080/tcp
EXPOSE 8081/tcp

COPY --from=build-deps /app/dd-java-agent.jar ./

RUN mkdir ./localstack
COPY localstack/* ./localstack

COPY billy-app/target/billy-app-1.0-SNAPSHOT.jar /app/billy-1.0-SNAPSHOT.jar
COPY infra/billy/* billy-app/src/main/resources/dd-java-agent.properties  ./

ARG git_sha_short
LABEL git_sha_short=${git_sha_short}
RUN echo ${git_sha_short} > version.txt

# Design this container image to be run with user=1000, group=2000.
RUN groupadd -g 2000 app && \
    useradd -u 1000 -g app app && \
    chown app:app /app

USER app

CMD ./run_billy.sh
