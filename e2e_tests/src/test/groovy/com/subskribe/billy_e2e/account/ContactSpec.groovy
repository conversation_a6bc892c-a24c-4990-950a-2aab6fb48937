package com.subskribe.billy_e2e.account

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.QueryParamBuilder
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.subscription.SubscriptionSpec
import com.subskribe.billy_e2e.utils.Authenticated
import spock.lang.Stepwise

@Stepwise
class ContactSpec extends Authenticated {

    static def getAccountContactJson(String firstName = null, String lastName = null, Map address = getAddressJson()) {
        String email = "email-" + UUID.randomUUID().toString() + "@subskribe.com"
        return [
            firstName  : firstName ? firstName : "Billie",
            lastName   : lastName ? lastName : "<PERSON>",
            email      : email,
            phoneNumber: "**********",
            title      : "<PERSON><PERSON>",
            address    : address
        ]
    }

    static def getContactJson(Map contactOverrides = [:]) {
        return [
            firstName  : "<PERSON>",
            lastName   : "<PERSON>",
            email      : "email-" + UUID.randomUUID().toString() + "@subskribe.com",
            phoneNumber: "**********",
            title      : "CFO",
            address    : getAddressJson(),
        ] + contactOverrides
    }

    static def getAddressJson() {
        return [
            streetAddressLine1: "118 N Delaware St",
            city              : "san mateo",
            state             : "CA",
            country           : "US",
            zipcode           : "94401"
        ]
    }

    def "add contact with invalid email"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        addContactJson.email = "invalid_email"
        def addContactResponse = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", addContactJson)

        then:
        addContactResponse.status == 400
    }

    def "add new contact to account"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        HttpResponse getContactResponse = createAndGetContact(client, accountId, addContactJson)

        then:
        getContactResponse.status == 200
        getContactResponse.body.firstName == addContactJson.firstName
        getContactResponse.body.address.city == addContactJson.address.city
    }

    def "add new contact with white spaces in name to account"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        String firstName = "  first name   "
        String lastName = "   last name   "
        String trimmedFirstName = firstName.trim()
        String trimmedLastName = lastName.trim()
        Map addContactJson = getAccountContactJson(firstName, lastName)
        HttpResponse getContactResponse = createAndGetContact(client, accountId, addContactJson)

        then:
        getContactResponse.status == 200
        getContactResponse.body.firstName == trimmedFirstName
        getContactResponse.body.lastName == trimmedLastName
    }

    def "add new contact to account without last name"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        addContactJson.lastName = null
        HttpResponse getContactResponse = createAndGetContact(client, accountId, addContactJson)

        then:
        getContactResponse.status == 200
        getContactResponse.body.firstName == addContactJson.firstName
        getContactResponse.body.lastName == ""
        getContactResponse.body.address.city == addContactJson.address.city
    }

    def "add new contact to account without title"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        addContactJson.title = null
        HttpResponse getContactResponse = createAndGetContact(client, accountId, addContactJson)

        then:
        getContactResponse.status == 200
        getContactResponse.body.firstName == addContactJson.firstName
        getContactResponse.body.title == null
        getContactResponse.body.address.city == addContactJson.address.city
    }

    def "add new contact to account without state should pass as partial address is allowed"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        addContactJson.address.state = null
        HttpResponse getContactResponse = createAndGetContact(client, accountId, addContactJson)

        then:
        getContactResponse.status == 200
    }

    def "add new contact to account without address and then add address"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContactWithoutAddress(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address == null
        contact.address = getAddressJson()

        String contactPath = getAccountContactPath(accountId, contact.id as String)
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        HttpResponse getUpdatedContactResponse = client.get(contactPath)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 200
        getUpdatedContactResponse.status == 200
        getUpdatedContactResponse.body.address != null
    }

    def "add new contact to account with partial address (no country)"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map partialAddress = [
            streetAddressLine1: "118 N Delaware St",
            city              : "san mateo",
            state             : "CA",
            zipcode           : "94401"
        ]
        Map accountContactJson = getAccountContactJson("Mickey", "Donald", partialAddress)
        HttpResponse getContactResponse = createAndGetContact(client, accountId, accountContactJson)

        Map contact = getContactResponse.body
        Map savedAddress = contact.address as Map
        assert savedAddress != null

        then:
        getContactResponse.status == 200
        savedAddress.streetAddressLine1 == partialAddress.streetAddressLine1
        savedAddress.city == partialAddress.city
        savedAddress.state == partialAddress.state
        savedAddress.zipcode == partialAddress.zipcode
        savedAddress.country == null
    }

    def "add new contact to account with invalid partial address (zipcode more 11 chars)"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map partialAddress = [
            streetAddressLine1: "118 N Delaware St",
            city              : "san mateo",
            state             : "CA",
            zipcode           : "***************"
        ]
        Map accountContactJson = getAccountContactJson("Mickey", "Donald", partialAddress)
        HttpResponse getContactResponse = createAndGetContact(client, accountId, accountContactJson)

        then:
        getContactResponse.status == 400
        getContactResponse.error.contains("zipcode cannot be more than 10 characters")
    }

    def "add new contact to account with invalid partial address (country code invalid)"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map partialAddress = [
            streetAddressLine1: "118 N Delaware St",
            city              : "san mateo",
            state             : "CA",
            country           : "UK",
            zipcode           : "94401"
        ]
        Map accountContactJson = getAccountContactJson("Mickey", "Donald", partialAddress)
        HttpResponse getContactResponse = createAndGetContact(client, accountId, accountContactJson)

        then:
        getContactResponse.status == 400
        getContactResponse.error.contains("ISO 3166 alpha-2 country code expected")
    }

    def "cannot update contact associated with executed order to have incomplete address(streetAddressLine1 null)"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContact(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address != null

        def order = OrderSpec.createAndGetOrder(client, accountId)
        String contactPath = getAccountContactPath(accountId, order.billingContactId as String)

        contact.address.streetAddressLine1 = null
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 400
        updateContactResponse.error.contains("a complete address (Street Address 1, City, Country, and Zipcode) is required")
    }

    def "cannot update contact associated with executed order to have incomplete address(city null)"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContact(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address != null

        def order = OrderSpec.createAndGetOrder(client, accountId)
        String contactPath = getAccountContactPath(accountId, order.billingContactId as String)

        contact.address.city = null
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 400
        updateContactResponse.error.contains("a complete address (Street Address 1, City, Country, and Zipcode) is required")
    }

    def "cannot update contact associated with executed order to have incomplete address(country null)"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContact(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address != null

        def order = OrderSpec.createAndGetOrder(client, accountId)
        String contactPath = getAccountContactPath(accountId, order.billingContactId as String)

        contact.address.country = null
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 400
        updateContactResponse.error.contains("a complete address (Street Address 1, City, Country, and Zipcode) is required")
    }

    def "cannot update contact associated with executed order to have incomplete address(zipcode null)"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContact(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address != null

        def order = OrderSpec.createAndGetOrder(client, accountId)
        String contactPath = getAccountContactPath(accountId, order.billingContactId as String)

        contact.address.zipcode = null
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 400
        updateContactResponse.error.contains("a complete address (Street Address 1, City, Country, and Zipcode) is required")
    }

    def "cannot update contact associated with executed order to have address with invalid zipcode length"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContact(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address != null

        def order = OrderSpec.createAndGetOrder(client, accountId)
        String contactPath = getAccountContactPath(accountId, order.billingContactId as String)

        contact.address.zipcode = "**************"
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 400
        updateContactResponse.error.contains("zipcode cannot be more than 10 characters")
    }

    def "add new contact to account with address and then delete address"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContact(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address != null

        String contactPath = getAccountContactPath(accountId, contact.id as String)
        contact.address = null
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        HttpResponse getUpdatedContactResponse = client.get(contactPath)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 200
        getUpdatedContactResponse.status == 200
        getUpdatedContactResponse.body.address == null
    }

    def "cannot delete address for in-use contact"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId

        def order = OrderSpec.createAndGetOrder(client, accountId)
        client.put("${OrderSpec.ORDER_PATH}/${order.id}/status/submitted", null)

        String contactPath = getAccountContactPath(accountId, order.billingContactId as String)
        Map contact = client.get(contactPath).body
        contact.address = null
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        updateContactResponse.status == 409
        updateContactResponse.error.contains("Cannot remove address")
    }

    def "can update address for in-use contact"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        HttpResponse getContactResponse = createAndGetContact(client, accountId)

        Map contact = getContactResponse.body
        assert contact.address != null

        def order = OrderSpec.createAndGetOrder(client, accountId)
        client.put("${OrderSpec.ORDER_PATH}/${order.id}/status/submitted", null)

        String contactPath = getAccountContactPath(accountId, contact.id as String)
        contact.address.city = "1"
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        HttpResponse getUpdatedContactResponse = client.get(contactPath)

        then:
        getContactResponse.status == 200
        updateContactResponse.status == 200
        getUpdatedContactResponse.status == 200
        getUpdatedContactResponse.body.address.city == "1"
    }


    def "add new contact to account with too large fields"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId

        Map firstNameContact = getAccountContactJson()
        firstNameContact.firstName = StringUtils.getRandomString(256)
        HttpResponse firstNameContactResponse = createAndGetContact(client, accountId, firstNameContact)

        Map lastNameContact = getAccountContactJson()
        lastNameContact.lastName = StringUtils.getRandomString(256)
        HttpResponse lastNameContactResponse = createAndGetContact(client, accountId, lastNameContact)

        Map titleContact = getAccountContactJson()
        titleContact.title = StringUtils.getRandomString(256)
        HttpResponse titleContactResponse = createAndGetContact(client, accountId, titleContact)

        then:
        firstNameContactResponse.status == 400
        firstNameContactResponse.error.contains("First name")

        lastNameContactResponse.status == 400
        lastNameContactResponse.error.contains("Last name")

        titleContactResponse.status == 400
        titleContactResponse.error.contains("Title")
    }

    def "when contact added with same external id we have conflict"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        String externalId = AccountSpec.externalId()
        addContactJson["externalId"] = externalId
        def addContactResponse1 = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", addContactJson)
        def getContactResponse = client.get(getAccountContactPath(accountId, addContactResponse1.locationId))

        addContactJson = getAccountContactJson()
        addContactJson["externalId"] = externalId
        def addContactResponse2 = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", addContactJson)

        then:
        getContactResponse.body["externalId"] == externalId
        addContactResponse1.status == 201
        addContactResponse2.status == 409
        addContactResponse2.error.contains("contact exists with external id")
    }

    def "update account contact"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map contact = createAndGetContact(client, accountId).body
        contact.firstName = "updated first name"
        contact.lastName = null
        def newExternalId = AccountSpec.externalId()
        contact.externalId = newExternalId
        String contactPath = getAccountContactPath(accountId, contact.id as String)
        HttpResponse updateContactResponse = client.put(contactPath, contact)
        HttpResponse getContactResponse = client.get(contactPath)

        // try creating a contact with same externalId and it should fail
        Map newContact = createAndGetContact(client, accountId).body
        newContact.externalId = newExternalId
        HttpResponse conflictResponse = client.put(getAccountContactPath(accountId, newContact.id as String), newContact)

        then:
        updateContactResponse.status == 200
        getContactResponse.status == 200
        getContactResponse.body.firstName == contact.firstName
        getContactResponse.body.lastName == ""
        getContactResponse.body.externalId == newExternalId
        conflictResponse.status == 409
        conflictResponse.error.contains("contact exists with external id")
    }

    def "update account contact address persists in database"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map contact = createAndGetContact(client, accountId).body
        contact.address.streetAddressLine1 = "updated street address line 1"
        contact.address.city = "updated city"
        contact.address.state = "updated state"
        contact.address.country = "NZ"
        contact.address.zipcode = "new_zip"
        String contactPath = getAccountContactPath(accountId, contact.id as String)
        def queryParams = QueryParamBuilder.buildQueryParam([
            skipAddressValidation: true
        ])
        HttpResponse updateContactResponse = client.put(contactPath + queryParams, contact)
        HttpResponse getContactResponse = client.get(contactPath)

        then:
        updateContactResponse.status == 200
        getContactResponse.status == 200
        getContactResponse.body.address.streetAddressLine1 == contact.address.streetAddressLine1
        getContactResponse.body.address.city == contact.address.city
        getContactResponse.body.address.state == contact.address.state
        getContactResponse.body.address.country == contact.address.country
        getContactResponse.body.address.zipcode == contact.address.zipcode
    }

    def "update account contact with non-existing accountId returns 404"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map contact = createAndGetContact(client, accountId).body
        accountId = accountId.substring(0, accountId.length()-1)
        String contactPath = getAccountContactPath(accountId, contact.id as String)
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        updateContactResponse.status == 404
    }

    def "move account contact across different accounts returns 400"() {
        when:
        String originalAccountId = AccountSpec.createCustomerAccount(client).locationId
        Map contact = createAndGetContact(client, originalAccountId).body
        String newAccountId = AccountSpec.createCustomerAccount(client).locationId
        String contactPath = getAccountContactPath(newAccountId, contact.id as String)
        HttpResponse updateContactResponse = client.put(contactPath, contact)

        then:
        updateContactResponse.status == 400
    }

    def "add new contact with existing email returns returns the same contact"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map contact = createAndGetContact(client, accountId).body
        String existingEmail = contact.email
        String existingId = contact.id
        contact.id = null
        def addContactResponse = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", contact)
        String contactPath = getAccountContactPath(accountId, addContactResponse.locationId)
        Map existingContact = client.get(contactPath).body

        then:
        addContactResponse.status == 201
        existingContact.id == existingId
        existingContact.email == existingEmail
    }

    def "update contact with non unique email across account returns 409"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map firstContact = createAndGetContact(client, accountId).body
        Map secondContact = createAndGetContact(client, accountId).body
        firstContact.email = secondContact.email
        String contactPath = getAccountContactPath(accountId, firstContact.id as String)
        HttpResponse updateContactResponse = client.put(contactPath, firstContact)

        then:
        updateContactResponse.status == 409
    }

    def "add contact with mixed case email creates contact with all lower case email returns 200"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        addContactJson.email = "<EMAIL>"
        def addContactResponse = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", addContactJson)
        HttpResponse contactResponse = client.get(getAccountContactPath(accountId, addContactResponse.locationId))
        addContactJson = contactResponse.body

        then:
        addContactResponse.status == 201
        contactResponse.status == 200
        addContactJson.email == "<EMAIL>"
    }

    def "add contact with mixed case non unique email returns the existing contact"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map addContactJson = getAccountContactJson()
        addContactJson.email = "<EMAIL>"
        def addFirstContactResponse = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", addContactJson)

        Map newContactJson = getAccountContactJson()
        newContactJson.email = "<EMAIL>"
        def addSecondContactResponse = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", newContactJson)

        then:
        addFirstContactResponse.status == 201
        addSecondContactResponse.status == 201
    }

    def "delete account contact"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map contact = createAndGetContact(client, accountId).body
        String contactPath = getAccountContactPath(accountId, contact.id as String)
        HttpResponse deleteContactResponse = client.delete(contactPath)
        HttpResponse getContactResponse = client.get(contactPath)

        then:
        deleteContactResponse.status == 200
        getContactResponse.status == 404
        deleteContactResponse.body.id == contact.id
    }

    def "add and delete account contact with same email multiple times"() {
        when:
        String accountId = AccountSpec.createCustomerAccount(client).locationId
        Map contact = createAndGetContact(client, accountId).body

        String contactPath = getAccountContactPath(accountId, contact.id as String)
        HttpResponse deleteContactResponse1 = client.delete(contactPath)
        HttpResponse getContactResponse1 = client.get(contactPath)

        contact.id = null
        HttpResponse addContactResponse2 = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", contact)
        contact = client.get(getAccountContactPath(accountId, addContactResponse2.locationId)).body
        contactPath = getAccountContactPath(accountId, contact.id as String)
        HttpResponse deleteContactResponse2 = client.delete(contactPath)
        HttpResponse getContactResponse2 = client.get(contactPath)

        contact.id = null
        HttpResponse addContactResponse3 = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", contact)
        contact = client.get(getAccountContactPath(accountId, addContactResponse3.locationId)).body
        contactPath = getAccountContactPath(accountId, contact.id as String)
        HttpResponse deleteContactResponse3 = client.delete(contactPath)
        HttpResponse getContactResponse3 = client.get(contactPath)

        then:
        deleteContactResponse1.status == 200
        getContactResponse1.status == 404

        addContactResponse2.status == 201
        deleteContactResponse2.status == 200
        getContactResponse2.status == 404

        addContactResponse3.status == 201
        deleteContactResponse3.status == 200
        getContactResponse3.status == 404
    }

    def "delete account contact when linked to order"() {
        when:
        Map order = OrderSpec.createAndGetOrder(client)
        String accountId = order.accountId
        String contactId = order.billingContactId
        HttpResponse deleteResponse = deleteContact(accountId, contactId)

        then:
        deleteResponse.status == 409
    }

    def "delete account used by subscription should fail"() {
        when:
        Map order = OrderSpec.createAndGetOrder(client)
        Map contact = createAndGetContact(client, order.accountId as String).body
        String accountId = order.accountId
        String contactId = contact.id
        HttpResponse updateSubContactResponse = SubscriptionSpec.updateSubscriptionAttributes(client, order.subscriptionId as String, contactId, contactId)
        HttpResponse deleteResponse = deleteContact(accountId, contactId)

        then:
        updateSubContactResponse.status == 200
        deleteResponse.status == 409
    }

    static HttpResponse updateContactName(HttpClient client, String accountId, String contactId, String firstName) {
        String contactPath = getAccountContactPath(accountId, contactId)
        Map contact = client.get(contactPath).body
        contact.firstName = firstName
        return client.put(contactPath, contact)
    }

    static HttpResponse createAndGetContact(HttpClient client, String accountId, Map contactJson = getAccountContactJson(), boolean performStrictValidation = false) {
        def createContactPath = "${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts"
        if (performStrictValidation) {
            createContactPath += "?strictValidation=true"
        }
        def addContactResponse = client.post(createContactPath, contactJson)
        if (addContactResponse.status != 201) {
            return addContactResponse
        }

        return client.get(getAccountContactPath(accountId, addContactResponse.locationId))
    }

    static HttpResponse createAndGetContactWithoutAddress(HttpClient client, String accountId, Map contactJson = getAccountContactJson()) {
        contactJson.address = null
        def addContactResponse = client.post("${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts", contactJson)
        if (addContactResponse.status != 201) {
            return addContactResponse
        }

        return client.get(getAccountContactPath(accountId, addContactResponse.locationId))
    }

    static String getAccountContactPath(String accountId, String contactId) {
        return "${AccountSpec.ACCOUNT_PATH}/${accountId}/contacts/${contactId}"
    }

    private HttpResponse deleteContact(String accountId, String contactId) {
        return client.delete(getAccountContactPath(accountId, contactId))
    }
}
