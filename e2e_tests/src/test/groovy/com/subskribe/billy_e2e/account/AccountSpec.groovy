package com.subskribe.billy_e2e.account

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.payment.PaymentConfigSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.EventControl

class AccountSpec extends Authenticated {
    public static final String ACCOUNT_PATH = "/accounts"

    def "update account with crm id succeeds"() {
        when:
        Map account = createAndGetCustomerAccount(client, getAccountJson() + [crmId: null]).body
        String newCrmId = getTestCrmId()
        account.crmId = newCrmId
        HttpResponse updateResponse = updateAccount(client, account)
        Map updatedAccount = getCustomerAccount(client, account.id).body
        def events = EventControl.getEvents(client, "ACCOUNT_UPDATED")

        then:
        updateResponse.status == 200
        updatedAccount.crmId == newCrmId
        updatedAccount.legalName.startsWith("account legal name")
        events.collect { it.payload }.find {it.id.equals(updatedAccount.id) }.crmId == newCrmId
    }

    def "add account with duplicate crm id fails"() {
        when:
        String crmId = getTestCrmId()
        Map accountJson = getAccountJson()
        accountJson.crmId = crmId
        createCustomerAccount(client, accountJson).body

        accountJson = getAccountJson()
        accountJson.crmId = crmId
        HttpResponse addAccountResponse = createCustomerAccount(client, accountJson)

        accountJson.crmId = null
        Map account2 = createCustomerAccount(client, accountJson).body
        account2.crmId = crmId
        HttpResponse updateResponse = updateAccount(client, account2)

        then:
        addAccountResponse.status == 409
        addAccountResponse.error.contains("${crmId} already exists")

        updateResponse.status == 409
        updateResponse.error.contains("${crmId} already exists")
    }

    def "update account ERP ID"() {
        when:
        HttpResponse response = createCustomerAccount(client)
        String accountId = response.locationId
        String erpId = "erp-id-" + StringUtils.getRandomString(8)
        HttpResponse updateResponse = updateAccountErpId(client, accountId, erpId)
        Map account = getCustomerAccount(client, accountId).body
        def events = EventControl.getEvents(client, "ACCOUNT_UPDATED")

        then:
        response.status == 201
        updateResponse.status == 200
        account.erpId == erpId
        events.collect { it.payload }.find {it.id.equals(account.id) }.erpId == erpId
    }

    def "update account ERP ID with long ID"() {
        when:
        HttpResponse response = createCustomerAccount(client)
        String accountId = response.locationId
        String erpId = "erp-id-" + StringUtils.getRandomString(100)
        HttpResponse updateResponse = updateAccountErpId(client, accountId, erpId)

        then:
        response.status == 201
        updateResponse.status == 422
        updateResponse.error.contains("more than 100 characters")
    }

    def "update account ERP ID with existing ID"() {
        when:
        HttpResponse response = createCustomerAccount(client)
        String accountId = response.locationId
        String erpId1 = "erp-id-" + StringUtils.getRandomString(8)
        String erpId2 = "erp-id-" + StringUtils.getRandomString(8)
        HttpResponse updateResponse1 = updateAccountErpId(client, accountId, erpId1)
        HttpResponse updateResponse2 = updateAccountErpId(client, accountId, erpId2)
        HttpResponse updateWithOverrideResponse = updateAccountErpId(client, accountId, erpId2, true)

        then:
        response.status == 201
        updateResponse1.status == 200
        updateResponse2.status == 409
        updateResponse2.error.contains("Account ${accountId} already has an ERP ID ${erpId1}")
        updateWithOverrideResponse.status == 200
    }

    def "update account's legal name"() {
        when:
        Map account = createAndGetCustomerAccount(client, getAccountJson() + [crmId: null]).body
        String newLegalName = "Some Legal Name"
        account.legalName = newLegalName
        HttpResponse updateResponse = updateAccount(client, account)
        Map updatedAccount = getCustomerAccount(client, account.id as String).body

        then:
        updateResponse.status == 200
        updatedAccount.legalName == newLegalName
    }

    def "update account's payment config"() {
        when:
        Map account = createAndGetCustomerAccount(client, getAccountJson() + [crmId: "dummy-account-payment-config", erpId: "dummy-account-payment-config"]).body
        String accountId = account.id as String
        Map paymentConfigJson = [
            accountId: accountId,
            excludeFromPaymentRetries: true,
        ]
        HttpResponse excludeTrueResponse = upsertAccountPaymentConfiguration(client, accountId, paymentConfigJson)
        assert excludeTrueResponse.status == 200
        HttpResponse getConfigResponseCreation = client.get(getAccountPaymentConfigurationPath(accountId))
        assert getConfigResponseCreation.status == 200
        assert getConfigResponseCreation.body.accountId == accountId
        assert getConfigResponseCreation.body.excludeFromPaymentRetries == true
        assert getConfigResponseCreation.body.id != null

        Map updatedConfig = excludeTrueResponse.body
        assert updatedConfig.id != null
        assert updatedConfig.accountId == accountId
        assert updatedConfig.excludeFromPaymentRetries == true

        paymentConfigJson["excludeFromPaymentRetries"] = false
        HttpResponse excludeFalseResponse = upsertAccountPaymentConfiguration(client, accountId, paymentConfigJson)
        HttpResponse configResponseUpdation = client.get(getAccountPaymentConfigurationPath(accountId))

        then:
        excludeFalseResponse.status == 200
        Map updatedConfigFalse = excludeFalseResponse.body
        assert updatedConfigFalse.id == updatedConfig.id
        assert updatedConfigFalse.accountId == accountId
        assert updatedConfigFalse.excludeFromPaymentRetries == false
        assert configResponseUpdation.status == 200
        Map updatedConfigFromGet = configResponseUpdation.body
        assert updatedConfigFromGet.id == updatedConfig.id
        assert updatedConfigFromGet.id == getConfigResponseCreation.body.id
        assert updatedConfigFromGet.accountId == accountId
        assert updatedConfigFromGet.excludeFromPaymentRetries == false
    }

    static HttpResponse createCustomerAccount(HttpClient client, def json = getAccountJson()) {
        return client.post(ACCOUNT_PATH, json)
    }

    static HttpResponse updateAccount(HttpClient client, Map accountJson) {
        return client.put("${ACCOUNT_PATH}/${accountJson.id}", accountJson)
    }

    static HttpResponse createAndGetCustomerAccount(HttpClient client, def json = getAccountJson()) {
        def response = createCustomerAccount(client, json)
        if (response.status != 201) {
            return response
        }

        return client.get("${ACCOUNT_PATH}/${response.locationId}")
    }

    static HttpResponse getCustomerAccount(HttpClient client, String accountId) {
        return client.get("${ACCOUNT_PATH}/${accountId}")
    }

    static HttpResponse addPaymentMethodToAccount(HttpClient client, def accountId, PaymentConfigSpec.PaymentType paymentType = PaymentConfigSpec.PaymentType.CHECK) {
        def json = getPaymentMethodJson(paymentType)
        json.accountId = accountId
        return client.post("${ACCOUNT_PATH}/${accountId}/paymentMethods", json)
    }

    static Map createAccountWithPaymentMethod(HttpClient client) {
        def createAccountResponse = createCustomerAccount(client)
        def accountId = createAccountResponse.locationId
        def addPaymentMethodJson = addPaymentMethodToAccount(client, accountId)
        return [
            "accountId"      : accountId,
            "paymentMethodId": addPaymentMethodJson.locationId
        ]
    }

    static HttpResponse upsertAccountPaymentConfiguration(HttpClient client, String accountId, Map paymentConfigJson) {
        return client.post("${ACCOUNT_PATH}/${accountId}/paymentConfig", paymentConfigJson)
    }

    static String getAccountPaymentConfigurationPath(String accountId) {
        return "${getAccountPath(accountId)}/paymentConfig"
    }

    static String getAccountPaymentMethodsPath(String accountId) {
        return "${getAccountPath(accountId)}/paymentMethods"
    }

    static String getAccountPaymentMethodPath(String accountId, String paymentMethodId) {
        return "${getAccountPaymentMethodsPath(accountId)}/internal/${paymentMethodId}"
    }

    static String getAccountPath(String accountId, type = null) {
        String basePath = "${ACCOUNT_PATH}/${accountId}"
        if (type != null) {
            return "${basePath}?idType=${type}"
        } else {
            return basePath
        }
    }

    static Map getAccountJson(Boolean isReseller = false, Boolean excludeFromBatchOperations = false, Boolean excludeFromDunning = false, Map accountOverrides = [:]) {
        return [
            name                        : "account name-" + StringUtils.getRandomString(8),
            legalName                   : "account legal name-" + StringUtils.getRandomString(8),
            description                 : "Zoom corporate",
            phoneNumber                 : "************",
            crmId                       : getTestCrmId(),
            erpId                       : null,
            isReseller                  : isReseller,
            excludeFromBatchOperations  : excludeFromBatchOperations,
            excludeFromDunning          : excludeFromDunning,
        ] + accountOverrides
    }

    static def getAddressJson() {
        return [
            streetAddressLine1: "1 Microsoft Way",
            city              : "Redmond",
            state             : "WA",
            country           : "US",
            zipcode           : "98052"
        ]
    }

    static String getTestCrmId() {
        return "Test_" + StringUtils.getRandomString(10)
    }

    static String externalId() {
        return "gangsta-rap" + StringUtils.getRandomString(8)
    }

    static Map getPaymentMethodJson(PaymentConfigSpec.PaymentType paymentType) {
        [
            name       : "pay by check",
            paymentType: paymentType,
        ]
    }

    static HttpResponse updateAccountErpId(HttpClient client, String accountId, String erpId, boolean override = false) {
        return client.put("${ACCOUNT_PATH}/${accountId}/erp?override=${override}", [erpId: erpId])
    }
}
