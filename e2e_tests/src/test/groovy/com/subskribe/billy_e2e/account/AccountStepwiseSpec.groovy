package com.subskribe.billy_e2e.account

import static com.subskribe.billy_e2e.account.AccountSpec.ACCOUNT_PATH
import static com.subskribe.billy_e2e.account.AccountSpec.accountJson
import static com.subskribe.billy_e2e.account.AccountSpec.addPaymentMethodToAccount
import static com.subskribe.billy_e2e.account.AccountSpec.addressJson
import static com.subskribe.billy_e2e.account.AccountSpec.createAndGetCustomerAccount
import static com.subskribe.billy_e2e.account.AccountSpec.createCustomerAccount
import static com.subskribe.billy_e2e.account.AccountSpec.externalId
import static com.subskribe.billy_e2e.account.AccountSpec.getAccountPath
import static com.subskribe.billy_e2e.account.AccountSpec.getAccountPaymentMethodsPath
import static com.subskribe.billy_e2e.account.AccountSpec.testCrmId

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.payment.PaymentConfigSpec
import com.subskribe.billy_e2e.payment.PaymentConfigSpec.PaymentType
import com.subskribe.billy_e2e.settings.TenantSettingRestAndGqlSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.EventControl
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class AccountStepwiseSpec extends Authenticated {

    static final String EXTERNAL_ID = "externalId"

    @Shared
    private String accountId

    @Shared
    private String accountName

    @Shared
    private String crmId

    @Shared
    private String newCurrency

    @Shared
    private Map nonResellerAccount

    @Shared
    private List supportedPaymentTypes

    def setupSpec() {
        TenantSettingRestAndGqlSpec.updateTenantSettingSeal(client, "OFF")
        TenantSettingRestAndGqlSpec.addAllSupportedCurrencies(client)
        TenantSettingRestAndGqlSpec.updateTenantSettingSeal(client, "ON")
        HttpResponse paymentConfigResponse = PaymentConfigSpec.getPaymentConfig(client)
        supportedPaymentTypes = paymentConfigResponse.body.supportedPaymentTypes
    }

    def "add invalid accounts"() {
        when:
        Map nameTooLong = getAccountJson()
        nameTooLong.name = StringUtils.getRandomString(256)
        HttpResponse nameTooLongResponse = createCustomerAccount(client, nameTooLong)

        Map descriptionTooLong = getAccountJson()
        descriptionTooLong.description = StringUtils.getRandomString(5000)
        HttpResponse descriptionTooLongResponse = createCustomerAccount(client, descriptionTooLong)

        then:
        nameTooLongResponse.status == 400
        nameTooLongResponse.error.contains("255 characters")
        nameTooLongResponse.error.contains("Name")

        descriptionTooLongResponse.status == 400
        descriptionTooLongResponse.error.contains("4000 characters")
        descriptionTooLongResponse.error.contains("Description")
    }

    def "add new account"() {
        when:
        Map accountJson = getAccountJson()
        accountJson.address = getAddressJson()
        HttpResponse addAccountResponse = createCustomerAccount(client, accountJson)
        accountId = addAccountResponse.locationId
        HttpResponse getAccountResponse = client.get(getAccountPath(addAccountResponse.locationId))
        def savedAccount = getAccountResponse.body
        crmId = savedAccount.crmId
        def events = EventControl.getEvents(client, "ACCOUNT_CREATED")

        then:
        addAccountResponse.status == 201
        getAccountResponse.status == 200
        savedAccount.currency == "USD"
        savedAccount.name == accountJson.name
        savedAccount.legalName == accountJson.legalName
        savedAccount.description == accountJson.description
        savedAccount.phoneNumber == accountJson.phoneNumber
        savedAccount.crmId == accountJson.crmId
        savedAccount.erpId == accountJson.erpId
        savedAccount.isReseller == accountJson.isReseller
        savedAccount.excludeFromBatchOperations == accountJson.excludeFromBatchOperations
        savedAccount.excludeFromDunning == accountJson.excludeFromDunning
        savedAccount.supportedPaymentTypes.sort() == supportedPaymentTypes.sort()
        savedAccount.entityIds.contains("*")
        savedAccount.address.city == getAddressJson().city
        events.last().payload.id == savedAccount.id
    }

    def "get account by CRM ID"() {
        when:
        Map accountJson = getAccountJson()
        accountJson.address = getAddressJson()
        HttpResponse addAccountResponse = createCustomerAccount(client, accountJson)
        String path = getAccountPath(accountJson.crmId as String, "CRM_ID")
        Map account = client.get(path).body

        then:
        addAccountResponse.status == 201
        account.crmId == accountJson.crmId
        addAccountResponse.locationId == account.id
        account.address.zipcode == getAddressJson().zipcode
    }

    def "add new account without phone number"() {
        when:
        Map json = getAccountJson()
        json.remove("phoneNumber")
        HttpResponse addAccountResponse = createCustomerAccount(client, json)
        accountId = addAccountResponse.locationId
        HttpResponse getAccountResponse = client.get(getAccountPath(addAccountResponse.locationId))
        accountName = getAccountResponse.body.name
        crmId = getAccountResponse.body.crmId

        then:
        json.phoneNumber == null
        addAccountResponse.status == 201
        getAccountResponse.status == 200
        accountName == json.name
        getAccountResponse.body.crmId == json.crmId
        getAccountResponse.body.currency == "USD"
        getAccountResponse.body.phoneNumber == null
        getAccountResponse.body.updatedOn != null
        getAccountResponse.body.entityIds.contains("*")
        getAccountResponse.body.address == null
    }

    def "add new account without legal name"() {
        when:
        Map json = getAccountJson()
        json.remove("legalName")
        HttpResponse addAccountResponse = createCustomerAccount(client, json)
        accountId = addAccountResponse.locationId
        HttpResponse getAccountResponse = client.get(getAccountPath(addAccountResponse.locationId))
        accountName = getAccountResponse.body.name
        crmId = getAccountResponse.body.crmId

        then:
        json.legalName == null
        addAccountResponse.status == 201
        getAccountResponse.status == 200
        accountName == json.name
        getAccountResponse.body.crmId == json.crmId
        getAccountResponse.body.currency == "USD"
        getAccountResponse.body.legalName == null
        getAccountResponse.body.updatedOn != null
        getAccountResponse.body.entityIds.contains("*")
        getAccountResponse.body.address == null
    }

    def "add account with existing name"() {
        when:
        Map json = getAccountJson()
        json.name = accountName
        HttpResponse response = createCustomerAccount(client, json)

        then:
        response.status == 409
    }

    def "update account"() {
        when:
        newCurrency = TenantSettingRestAndGqlSpec.USED_CURRENCY
        String path = getAccountPath(accountId)
        Map account = client.get(path).body
        account.name = "updated name-" + StringUtils.getRandomString(8)
        account.legalName = "updated legal-name-" + StringUtils.getRandomString(8)
        account.description = "updated description"
        account.phoneNumber = "************"
        account.timezone = DateTimeHelper.LA_TZ
        account.currency = newCurrency
        account.excludeFromBatchOperations = true
        account.excludeFromDunning = true

        // Trying to update supported payment types should not work through this endpoint
        def originalSupportedPaymentTypes = account.supportedPaymentTypes
        account.supportedPaymentTypes = [
            PaymentType.CARD
        ]
        HttpResponse updateResponse = client.put(path, account)
        Map updatedAccount = client.get(path).body

        then:
        updateResponse.status == 200
        updatedAccount.name == account.name
        updatedAccount.legalName == account.legalName
        updatedAccount.description == account.description
        updatedAccount.phoneNumber == account.phoneNumber
        updatedAccount.currency == account.currency
        updatedAccount.excludeFromBatchOperations == account.excludeFromBatchOperations
        updatedAccount.excludeFromDunning == account.excludeFromDunning
        updatedAccount.supportedPaymentTypes.sort() == originalSupportedPaymentTypes.sort()
        updatedAccount.entityIds.contains("*")
    }

    def "update account with existing crm id fails"() {
        when:
        String otherAccountCrmId = getTestCrmId()
        createCustomerAccount(client, getAccountJson() + [crmId: otherAccountCrmId])

        String path = getAccountPath(accountId)
        Map account = client.get(path).body
        account.crmId = otherAccountCrmId
        HttpResponse updateResponse = client.put(path, account)

        then:
        updateResponse.status == 409
    }

    def "add new account with erp id"() {
        when:
        Map accountJson = getAccountJson(false, false, false, [erpId: "123"])
        Map account = createAndGetCustomerAccount(client, accountJson).body

        then:
        account.erpId == accountJson.erpId
    }

    def "add new payment method"() {
        when:
        HttpResponse addPaymentMethodJson = addPaymentMethodToAccount(client, accountId)
        HttpResponse getPaymentMethodsResponse = client.get(getAccountPaymentMethodsPath(accountId))

        then:
        addPaymentMethodJson.status == 201
        getPaymentMethodsResponse.status == 200
        getPaymentMethodsResponse.body.size() == 1
    }

    def "delete account"() {
        when:
        String path = getAccountPath(accountId)
        HttpResponse deleteResponse = client.delete(path)
        HttpResponse getResponse = client.get(path)
        def events = EventControl.getEvents(client, "ACCOUNT_DELETED")

        then:
        deleteResponse.status == 200
        deleteResponse.body.id == accountId
        getResponse.status == 404
        events.last().payload.id == accountId
    }

    def "delete account with order not allowed"() {
        when:
        Map order = OrderSpec.createAndGetOrder(client)
        String accountId = order.accountId
        HttpResponse deleteResponse = client.delete(getAccountPath(accountId))

        then:
        deleteResponse.status == 409
    }

    def "when account added with same external id we have conflict"() {
        when:
        Map json = getAccountJson()
        var externalId = externalId()
        json[EXTERNAL_ID] = externalId
        HttpResponse account1Response = createCustomerAccount(client, json)
        HttpResponse getAccountResponse = client.get(getAccountPath(account1Response.locationId))

        json = getAccountJson()
        json[EXTERNAL_ID] = externalId
        HttpResponse account2Response = createCustomerAccount(client, json)

        then:
        getAccountResponse.body[EXTERNAL_ID] == externalId
        account1Response.status == 201
        account2Response.status == 409
        account2Response.error.contains("account exists with external id")
    }

    def "when account is updated with external id then it retains it"() {
        when:
        Map json = getAccountJson()
        HttpResponse addResponse = createCustomerAccount(client, json)
        HttpResponse firstGetResponse = client.get(getAccountPath(addResponse.locationId))
        var externalId = externalId()
        json[EXTERNAL_ID] = externalId
        client.put(getAccountPath(addResponse.locationId), json)
        HttpResponse secondGetResponse = client.get(getAccountPath(addResponse.locationId))
        then:
        firstGetResponse.body[EXTERNAL_ID] == null
        secondGetResponse.body[EXTERNAL_ID] == externalId
    }

    def "Currency of a used account cannot be updated"() {
        when:
        Map order = OrderSpec.createAndGetOrder(client)
        String accountId = order.accountId

        String path = getAccountPath(accountId)
        Map account = client.get(path).body
        account.currency = TenantSettingRestAndGqlSpec.USED_CURRENCY
        HttpResponse updateResponse = client.put(path, account)

        then:
        updateResponse.status == 400
    }

    def "add reseller account"() {
        when:
        Map json = getAccountJson(true)
        HttpResponse addAccountResponse = createCustomerAccount(client, json)
        accountId = addAccountResponse.locationId
        HttpResponse getAccountResponse = client.get(getAccountPath(addAccountResponse.locationId))

        then:
        addAccountResponse.status == 201
        getAccountResponse.status == 200
        getAccountResponse.body.isReseller == true
    }

    def "create a non reseller account"() {
        when:
        Map json = getAccountJson()
        HttpResponse addAccountResponse = createCustomerAccount(client, json)
        accountId = addAccountResponse.locationId
        HttpResponse getAccountResponse = client.get(getAccountPath(addAccountResponse.locationId))
        nonResellerAccount = getAccountResponse.body

        then:
        addAccountResponse.status == 201
        getAccountResponse.status == 200
        nonResellerAccount.isReseller == false
    }

    def "update non reseller account to reseller account"() {
        when:
        nonResellerAccount.isReseller = true
        HttpResponse putAccountResponse = client.put(getAccountPath(nonResellerAccount.id as String), nonResellerAccount)
        HttpResponse getAccountResponse = client.get(getAccountPath(nonResellerAccount.id as String))

        then:
        putAccountResponse.status == 200
        getAccountResponse.status == 200
        getAccountResponse.body.isReseller == true
    }

    def "fetch list of reseller accounts"() {
        when:
        String query = ACCOUNT_PATH + "?type=reseller"
        HttpResponse getAccountResponse = client.get(query)
        List<Map> accounts = getAccountResponse.body.data

        then:
        getAccountResponse.status == 200
        accounts != null
        accounts.size() > 0
        accounts.every {
            it.isReseller == true
        }
    }

    def "fetch list of non reseller accounts"() {
        when:
        String query = ACCOUNT_PATH + "?type=non_reseller"
        HttpResponse getAccountResponse = client.get(query)
        List<Map> accounts = getAccountResponse.body.data

        then:
        getAccountResponse.status == 200
        accounts != null
        accounts.size() > 0
        accounts.every {
            it.isReseller == false
        }
    }
}
