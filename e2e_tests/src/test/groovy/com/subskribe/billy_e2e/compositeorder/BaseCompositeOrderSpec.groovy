package com.subskribe.billy_e2e.compositeorder

import com.subskribe.billy_e2e.EnumType
import com.subskribe.billy_e2e.GqlUtils
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.graphql.GqlSpec

class BaseCompositeOrderSpec extends GqlSpec {

    static String COMPOSITE_ORDER_PATH = "/compositeOrders"

    static String fields = /{
            id
            type
            status
            orderIds
            crmOpportunityId
            orders {
                id
                compositeOrderId
                orderType
                startDate
                endDate
                lineItems {
                    id
                    plan {
                        id
                    }
                    charge {
                        id
                    }
                    quantity
                    effectiveDate
                    endDate
                    amount
                    discountAmount
                }
            }
            documentMasterTemplateId
            isPrimaryCompositeOrderForCrmOpportunity
        }/

    static String  cancelAndRestructureQuery = /{
            id
            type
            name
            subscriptionId
            purchaseOrderNumber
            paymentTerm
            crmOpportunityId
            documentMasterTemplateId
            restructureForSubscriptionId
            status
            billingContact {
                id
                firstName
                lastName
                email
                address {
                    city
                }
            }
            shippingContact {
                id
                firstName
                lastName
                email
                address {
                    city
                }
            }
            predefinedDiscounts {
                id
                name
                type
                description
                percent
            }
            billingCycle {
              cycle
              step
            }
            owner {
                id
            }
            lineItems {
                ${orderItemDetailFragment}
            }
            removedLineItems {
                ${orderItemDetailFragment}
            }
            orders {
                ${orderDetailMinimalFragments}
            }
            compositeOrderId
            arr
            deltaArr
            tcv
            recurringTotal
            nonRecurringTotal
            rampInterval
            currency
            account {
                id
                name
            }
        }/

    static String orderItemDetailFragment = /
        action
        id
        plan {
            id
            name
            productId
        }
        charge {
            id
            name
        }
        subscriptionChargeId
        quantity
        effectiveDate
        endDate
        discounts {
            name
            percent
            discountAmount
            amount
        }
        predefinedDiscounts {
            id
            name
            type
            description
            percent
            amount
        }
        listUnitPrice
        sellUnitPrice
        listAmount
        taxEstimate
        amount
        restructureAmount
    /

    static String orderDetailMinimalFragments = /
        id
        name
        orderType
        ownerId
        status
        effectiveDate
        totalAmount
        customBillingEligibleOrderLineIds
    /

    static Map getCompositeOrderJson(HttpClient client, String compositeOrderId) {
        return client.get("${COMPOSITE_ORDER_PATH}/${compositeOrderId}").body
    }

    static Map getCompositeOrder(HttpClient client, String compositeOrderId) {
        Map query = [query: GqlUtils.query("compositeOrder", [id: compositeOrderId], fields)]
        return client.post(GQL_PATH, query).body.data.compositeOrder
    }

    static Map getCompositeOrderQuery(String compositeOrderId) {
        return [query: GqlUtils.query("compositeOrder", [id: compositeOrderId], fields)]
    }

    static HttpResponse moveCompositeOrderStatus(HttpClient client, String id, String status) {
        Map arguments = [
            id: id,
            status: new EnumType(status)
        ]
        Map mutation = [query: GqlUtils.mutation("updateCompositeOrderStatus", arguments, fields)]
        return client.post(GQL_PATH, mutation)
    }

    static HttpResponse deleteCompositeOrder(HttpClient client, String compositeOrderId) {
        Map mutation = [query: GqlUtils.mutation("deleteUpsellWithEarlyRenewal", [id: compositeOrderId], fields)]
        return client.post(GQL_PATH, mutation)
    }

    static HttpResponse updatePrimaryCompositeOrderIdForCrmOpportunity(HttpClient client, String compositeOrderId) {
        Map arguments = [
            compositeOrderId: compositeOrderId
        ]
        Map mutation =  [query: GqlUtils.mutation("updatePrimaryCompositeOrderIdForCrmOpportunity", arguments, fields)]
        return client.post(GQL_PATH, mutation)
    }
}
