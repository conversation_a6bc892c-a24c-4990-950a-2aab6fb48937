package com.subskribe.billy_e2e.tax

import static TaxRateSpec.addNewTaxRate

import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.productcatalog.ProductSpec
import com.subskribe.billy_e2e.taxjar.TaxJarBaseSpec
import com.subskribe.billy_e2e.taxjar.TaxJarIntegrationSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.FeatureFlag

class TaxRatePercentBasedSpec extends Authenticated {

    private static final String TAX_RATE_PATH = "/taxrates"
    public static final DEFAULT_TAX_CODE = "P000000"

    def setupSpec() {
        FeatureFlag.updateFlag(client, "TAX_PERCENT", true)
    }

    def "add new tax rate"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def getTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")

        then:
        addTaxRateResponse.status == 201
        getTaxRateResponse.status == 200
        !getTaxRateResponse.body.inUse
        getTaxRateResponse.body.taxPercentage == 0.1
    }

    def "add new tax rate without code or percentage should fail"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, null)

        then:
        addTaxRateResponse.status == 400
    }

    def "add new tax rate with both code and percentage should fail"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, DEFAULT_TAX_CODE, 0.1)

        then:
        addTaxRateResponse.status == 400
        addTaxRateResponse.error.contains("tax code and tax percentage")
    }

    def "add new tax rate with no percentage does not fail"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, DEFAULT_TAX_CODE, null)
        def getTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")

        then:
        addTaxRateResponse.status == 201
        getTaxRateResponse.status == 200
        !getTaxRateResponse.body.inUse
        getTaxRateResponse.body.taxPercentage == null
    }

    def "add new tax rate with negative rate should fail"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, -0.1)

        then:
        addTaxRateResponse.status == 400
    }

    def "add new tax rate percent greater than 100% should fail"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 1.2)

        then:
        addTaxRateResponse.status == 400
    }

    def "add new tax rate with tax inclusive fails"() {
        when:
        def response = addNewTaxRate(client, null, 2.0, true)

        then:
        response.status == 400
        response.error.contains("tax inclusive")
    }

    def "updating tax rate without charge reference works correctly"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def getTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")
        //modify tax percentage
        getTaxRateResponse.body.taxPercentage = 0.2
        def updateTaxRateResponse = client.put("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}", getTaxRateResponse.body)

        // get again to verify update
        def updatedTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")

        then:
        addTaxRateResponse.status == 201
        getTaxRateResponse.status == 200
        updateTaxRateResponse.status == 200
        updatedTaxRateResponse.status == 200
        updatedTaxRateResponse.body.taxPercentage == 0.2
        !updatedTaxRateResponse.body.inUse
    }

    def "updating non existent tax rate fails"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def getTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")
        def randomUUID = UUID.randomUUID().toString()
        getTaxRateResponse.body.id = randomUUID
        def updateTaxRateResponse = client.put("${TAX_RATE_PATH}/${randomUUID}", getTaxRateResponse.body)

        then:
        addTaxRateResponse.status == 201
        getTaxRateResponse.status == 200
        updateTaxRateResponse.status == 404
    }

    def "updating tax rate with charge reference fails"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def createProductResponse = ProductSpec.createProduct(client)
        PlanSpec.createPlanWithGivenTaxRateId(client, createProductResponse.locationId, addTaxRateResponse.locationId)
        def getTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")
        //modify tax percentage
        getTaxRateResponse.body.taxPercentage = 0.2
        def updateTaxRateResponse = client.put("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}", getTaxRateResponse.body)

        then:
        addTaxRateResponse.status == 201
        getTaxRateResponse.body.inUse
        updateTaxRateResponse.status == 400
    }

    def "deleting tax rate without charge reference works correctly"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def getTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")
        def deleteTaxRateResponse = client.delete("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")
        def deletedTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")

        then:
        addTaxRateResponse.status == 201
        getTaxRateResponse.status == 200
        !getTaxRateResponse.body.inUse
        deleteTaxRateResponse.status == 200
        deletedTaxRateResponse.status == 404
    }

    def "deleting tax rate with charge reference fails"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def createProductResponse = ProductSpec.createProduct(client)
        PlanSpec.createPlanWithGivenTaxRateId(client, createProductResponse.locationId, addTaxRateResponse.locationId)
        def getTaxRateResponse = client.get("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")
        def deleteTaxRateResponse = client.delete("${TAX_RATE_PATH}/${addTaxRateResponse.locationId}")

        then:
        addTaxRateResponse.status == 201
        getTaxRateResponse.body.inUse
        deleteTaxRateResponse.status == 400
    }

    def "deleting non existent tax rate fails"() {
        when:
        def randomUUID = UUID.randomUUID().toString()
        def deleteTaxRateResponse = client.delete("${TAX_RATE_PATH}/${randomUUID}")

        then:
        deleteTaxRateResponse.status == 404
    }

    def "add avalara integration fails when percent based charges exist"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def avalaraResponse = AvalaraIntegrationSpec.addNewIntegration(client, AvalaraIntegrationSpec.ACCOUNT_ID, AvalaraIntegrationSpec.ACCOUNT_LICENSE_KEY)

        then:
        addTaxRateResponse.status == 201
        avalaraResponse.status == 409
        avalaraResponse.error.contains("because percent based tax rates exist")
    }

    def "add TaxJar integration fails when percent based charges exist"() {
        when:
        def addTaxRateResponse = addNewTaxRate(client, null, 0.1)
        def taxJarResponse = TaxJarBaseSpec.addIntegration(client, TaxJarIntegrationSpec.TAX_JAR_SANDBOX_API_KEY, true)

        then:
        addTaxRateResponse.status == 201
        taxJarResponse.status == 409
        taxJarResponse.error.contains("because percent based tax rates exist")
    }
}
