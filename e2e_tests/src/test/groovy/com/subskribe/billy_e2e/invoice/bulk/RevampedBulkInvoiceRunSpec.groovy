package com.subskribe.billy_e2e.invoice.bulk

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.invoice.automated.AutomatedInvoiceRuleSpec
import java.time.Instant
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class RevampedBulkInvoiceRunSpec extends BaseBulkInvoiceSpec {

    @Shared
    String automatedInvoiceRuleId

    @Shared
    Map automatedInvoiceRequestJson

    @Shared
    String bulkInvoiceRunId

    def setupSpec() {
        initialize()
        createAutomaticInvoiceRule(client)
    }

    def cleanupSpec() {
        automatedInvoiceRequestJson.enabled = false
        AutomatedInvoiceRuleSpec.updateAutomatedInvoiceRule(client, automatedInvoiceRuleId, automatedInvoiceRequestJson)
    }

    def "Step 1: Create bulk invoice run"() {
        when:
        Long invoiceTargetDate = DateTimeHelper.plusMonths(BULK_INVOICE_TEST_START_DATE, 2, tenantTimeZone)
        invoiceDate = DateTimeHelper.plusDays(Instant.now().getEpochSecond(), 1, tenantTimeZone)
        Map bulkInvoiceRunInput = getBulkInvoiceInput("First step", invoiceTargetDate, invoiceDate)
        HttpResponse response = client.post("/admin/local/bulk-invoice-run/automated/" + automatedInvoiceRuleId, bulkInvoiceRunInput)
        bulkInvoiceRunId = response.body

        then:
        response.status == 200
        bulkInvoiceRunId.startsWith("BIR")
    }

    def "Step 2: Confirm bulk invoice run generated"() {
        when:
        HttpResponse response = client.get("/admin/local/bulk-invoice-run/automated/" + automatedInvoiceRuleId)

        then:
        response.status == 200
        response.body == bulkInvoiceRunId
    }

    def "Step 3: Process first run and create a second manual bulk invoice run"() {
        when:
        processJob()

        Long invoiceTargetDate = DateTimeHelper.plusMonths(BULK_INVOICE_TEST_START_DATE, 2, tenantTimeZone)

        Map bulkInvoiceRunInput = getBulkInvoiceInput("Third step", invoiceTargetDate)
        HttpResponse createResponse = createBulkInvoiceRun(client, bulkInvoiceRunInput)

        then:
        createResponse.status == 201
    }

    def "Step 4: Verify automated bulk run processing and generated invoices"() {
        when:
        processJob()
        HttpResponse invoiceRunResponse = getBulkInvoiceRun(client, bulkInvoiceRunId)
        HttpResponse invoiceRunItemsResponse = getBulkInvoiceRunItems(client, bulkInvoiceRunId)

        List<String> draftInvoiceNumbers = invoiceRunItemsResponse.body.collect { it.draftInvoiceNumber }.findAll { it != null }.toList()

        then:
        invoiceRunResponse.status == 200
        invoiceRunResponse.body.phase == "INVOICES_GENERATED"
        invoiceRunResponse.body.status == "PROCESSING"

        invoiceRunItemsResponse.status == 200
        Set<String> subsInRunItems = invoiceRunItemsResponse.body
                .collect { it.subscriptionId }
                .toSet()
        subsInRunItems.containsAll(relevantSubscriptionIds)
        subsInRunItems.contains(subscriptionMissingPONumber)

        draftInvoiceNumbers.every { it.startsWith("DRAFT-") }
        !draftInvoiceNumbers.isEmpty()

        invoiceRunItemsResponse.body.findAll { it -> it.subscriptionId != subscriptionMissingPONumber }.each {
            it.accountName != null
            it.invoiceAmount != null
            it.invoiceCreatedOn != null
            it.invoiceCurrencyCode != null
        }

        invoiceRunItemsResponse.body.find { it -> it.subscriptionId == subscriptionMissingPONumber }.failureReason.contains("purchase order")
    }

    def "Step 5: Verify automated bulk run processing and invoices posting"() {
        when:
        processJob()
        HttpResponse invoiceRunResponse = getBulkInvoiceRun(client, bulkInvoiceRunId)

        then:
        invoiceRunResponse.status == 200
        invoiceRunResponse.body.phase == "INVOICES_POSTING"
        invoiceRunResponse.body.status == "PROCESSING"
    }

    def "Step 6: Verify automated bulk run processing and invoices posted"() {
        when:
        processJob()
        HttpResponse invoiceRunResponse = getBulkInvoiceRun(client, bulkInvoiceRunId)
        HttpResponse invoiceRunItemsResponse = getBulkInvoiceRunItems(client, bulkInvoiceRunId)

        // This is an automated invoice run with auto post so all invoices should have posted invoice numbers
        List<String> draftInvoiceNumbers = invoiceRunItemsResponse.body.collect { it.draftInvoiceNumber }.findAll { it != null }.toList()
        List<String> postedInvoiceNumbers = invoiceRunItemsResponse.body.collect { it.postedInvoiceNumber }.findAll { it != null }.toList()

        then:
        invoiceRunResponse.status == 200
        invoiceRunResponse.body.phase == "INVOICES_POSTED"
        invoiceRunResponse.body.status == "PROCESSING"
        invoiceRunItemsResponse.status == 200

        draftInvoiceNumbers.every { it.startsWith("DRAFT-") }
        draftInvoiceNumbers.size() == postedInvoiceNumbers.size()
        postedInvoiceNumbers.every { it.startsWith("INV-") }
    }

    def "Step 7: Verify automated bulk run processing and invoices emailing"() {
        when:
        processJob()
        HttpResponse invoiceRunResponse = getBulkInvoiceRun(client, bulkInvoiceRunId)

        then:
        invoiceRunResponse.status == 200
        invoiceRunResponse.body.phase == "INVOICES_EMAILING"
        invoiceRunResponse.body.status == "PROCESSING"
    }

    def "Step 8: Verify automated bulk run processing and invoices emailed"() {
        when:
        processJob()
        HttpResponse invoiceRunResponse = getBulkInvoiceRun(client, bulkInvoiceRunId)

        then:
        invoiceRunResponse.status == 200
        invoiceRunResponse.body.phase == "INVOICES_EMAILED"
        invoiceRunResponse.body.status == "COMPLETED"
    }

    private void processJob() {
        HttpResponse kickOffJobResponse = callBulkInvoiceRunBackgroundJob(client)
        if (kickOffJobResponse.status != 200) {
            throw new IllegalStateException(String.format("could not run bulk invoice job successfully %s", kickOffJobResponse.body))
        }
    }

    private String createAutomaticInvoiceRule(HttpClient client) {
        String onceEveryMinuteCronExpression = "0 15 6 * * ?"

        automatedInvoiceRequestJson = AutomatedInvoiceRuleSpec.getAutomatedInvoiceRequestJson("Revamp bulk Invoice Run: Automated Invoice Rule",
                "Revamp bulk Invoice Run:", onceEveryMinuteCronExpression, Instant.now().getEpochSecond(), true, true, entityId, true)

        HttpResponse createAutomatedInvoiceRuleResponse = AutomatedInvoiceRuleSpec.createAutomatedInvoiceRule(client, automatedInvoiceRequestJson)
        automatedInvoiceRuleId = createAutomatedInvoiceRuleResponse.locationId
    }
}