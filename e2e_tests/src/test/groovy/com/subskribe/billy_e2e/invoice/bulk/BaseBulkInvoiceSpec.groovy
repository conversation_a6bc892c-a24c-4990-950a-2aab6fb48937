package com.subskribe.billy_e2e.invoice.bulk

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.account.AccountSpec
import com.subskribe.billy_e2e.accounting.AccountingPeriodSpec
import com.subskribe.billy_e2e.invoice.BaseInvoiceSpec
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.utils.Recurrence
import java.time.Instant
import java.time.temporal.ChronoUnit
import spock.lang.Shared

class BaseBulkInvoiceSpec extends BaseInvoiceSpec {

    protected static final String BULK_INVOICE_PATH = INVOICE_PATH + "/bulk"
    protected static final String KICKOFF_JOB_PATH = INVOICE_PATH + "/test/processBulkInvoiceRun"

    protected static final int NUMBER_OF_ORDERS = 4
    protected static final long DAYS_TO_GO_BACK = 365 * 5
    protected static final long BULK_INVOICE_TEST_START_DATE = Instant.now().minus(DAYS_TO_GO_BACK, ChronoUnit.DAYS).epochSecond

    @Shared
    protected List<Map> relevantOrders = new ArrayList<>()

    @Shared
    protected List<String> relevantSubscriptionIds = new ArrayList<>()

    @Shared
    protected String subscriptionExcludedFromGenerating

    @Shared
    protected String subscriptionMissingPONumber

    @Shared
    protected Long invoiceDate

    @Shared
    protected TimeZone tenantTimeZone

    def setupSpec() {
        String timeZoneString = getTenantTimeZoneSetting(client)
        tenantTimeZone = TimeZone.getTimeZone(timeZoneString)
    }

    protected void initialize() {
        for (int i = 0; i < NUMBER_OF_ORDERS; i++) {
            Map order = OrderSpec.createOrderWithRecurringPlan(client,
                    Recurrence.ONE_MONTH_CYCLE,
                    Recurrence.ONE_MONTH_CYCLE,
                    BULK_INVOICE_TEST_START_DATE)
            relevantOrders.add(order)
            relevantSubscriptionIds.add(order.subscriptionId as String)
        }

        Map order = OrderSpec.createOrderWithPurchaseOrder(client, null, true, BULK_INVOICE_TEST_START_DATE)
        subscriptionMissingPONumber = order.subscriptionId

        String excludedAccountId = AccountSpec.createAndGetCustomerAccount(client, AccountSpec.getAccountJson(false, true)).body.id
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        String orderId = OrderSpec.createDraftOrder(client, plan, Recurrence.ONE_MONTH_CYCLE, Recurrence.ONE_MONTH_CYCLE,
                BULK_INVOICE_TEST_START_DATE, null, null, excludedAccountId, false).locationId
        OrderSpec.executeDraftOrder(client, orderId)
        subscriptionExcludedFromGenerating = OrderSpec.getOrder(client, orderId).subscriptionId

        // specify accounting period
        long startDate = DateTimeHelper.plusDays(Instant.now().getEpochSecond(), 0, DateTimeHelper.DEFAULT_TZ)
        AccountingPeriodSpec.specifyAccountingPeriod(client, startDate)
    }

    static Map getBulkInvoiceInput(String name,
            Long targetDate,
            Long invoiceDate = null,
            InvoiceChargeInclusionOption chargeInclusionOption = InvoiceChargeInclusionOption.INCLUDE_USAGE,
            String description = "bulk invoice description") {
        return [name: name, targetDate: targetDate, invoiceDate: invoiceDate, chargeInclusionOption: chargeInclusionOption, description: description]
    }

    static String getBulkInvoiceRunInternalIdFromLocation(String location) {
        return location.substring(location.lastIndexOf("/") + 1)
    }

    static HttpResponse createBulkInvoiceRun(HttpClient client, Map bulkInvoiceRunInput) {
        return client.post(getCreateBulkInvoicePath(), bulkInvoiceRunInput)
    }

    static HttpResponse getBulkInvoiceRun(HttpClient client, String runId) {
        return client.get(getBulkInvoicePath(runId))
    }

    static HttpResponse getBulkInvoiceRunItems(HttpClient client, String runId) {
        return client.get("${getBulkInvoicePath(runId)}/runItems")
    }

    static HttpResponse postBulkInvoiceRunItems(HttpClient client, String runId) {
        return client.put("${getBulkInvoicePath(runId)}/post", [])
    }

    static HttpResponse callBulkInvoiceRunBackgroundJob(HttpClient client) {
        return client.putNoBody(KICKOFF_JOB_PATH)
    }

    static String getBulkInvoicePath(String runId) {
        return "${BULK_INVOICE_PATH}/${runId}"
    }

    static String getCreateBulkInvoicePath() {
        return BULK_INVOICE_PATH
    }
}
