package com.subskribe.billy_e2e.invoice

import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.subscription.SubscriptionSpec
import com.subskribe.billy_e2e.utils.FeatureFlag
import spock.lang.Stepwise

// run specs in sequence to avoid concurrency issues with feature flag updates
@Stepwise
class InvoiceOnActivationSpec extends BaseInvoiceSpec {

    def "generate invoice without considering subscription activation"() {
        when:
        FeatureFlag.disableFeature(client, FeatureFlag.INVOICE_ON_ACTIVATION_FEATURE)
        Map order = OrderSpec.createAndGetOrder(client)
        String subscriptionId = order.subscriptionId
        long startDate = order.startDate as Long
        HttpResponse response = generateInvoice(client, subscriptionId, startDate)

        then:
        response.status == 200
        response.body.invoiceNumber != null
        response.body.subscriptionId == subscriptionId
    }

    def "no invoice generated with empty activation date"() {
        when:
        FeatureFlag.enableFeature(client, FeatureFlag.INVOICE_ON_ACTIVATION_FEATURE)
        Map order = OrderSpec.createAndGetOrder(client)
        String subscriptionId = order.subscriptionId
        long startDate = order.startDate as Long
        HttpResponse generateInvoiceResponse = generateInvoice(client, subscriptionId, startDate)

        then:
        generateInvoiceResponse.status == 200
        generateInvoiceResponse.body == null
    }

    def "generate invoice with activation date same as start date"() {
        when:
        Map order = OrderSpec.createAndGetOrder(client)
        String subscriptionId = order.subscriptionId
        long startDate = order.startDate as Long
        HttpResponse updateSubscriptionResponse = SubscriptionSpec.updateSubscriptionAttributes(client, subscriptionId, order.shippingContactId as String, order.billingContactId as String, null, null, null, null, startDate)
        HttpResponse generateInvoiceResponse = generateInvoice(client, subscriptionId, startDate)

        then:
        updateSubscriptionResponse.status == 200
        generateInvoiceResponse.status == 200
        generateInvoiceResponse.body.invoiceNumber != null
        generateInvoiceResponse.body.subscriptionId == subscriptionId
    }

    def "generate invoice with activation date in the future"() {
        when:
        Map order = OrderSpec.createAndGetOrder(client)
        String subscriptionId = order.subscriptionId
        long startDate = order.startDate as Long
        long activationDate = startDate + 1
        HttpResponse updateSubscriptionResponse = SubscriptionSpec.updateSubscriptionAttributes(client, subscriptionId, order.shippingContactId as String, order.billingContactId as String, null, null, null, null, activationDate)
        HttpResponse generateInvoiceResponse = generateInvoice(client, subscriptionId, startDate)

        then:
        updateSubscriptionResponse.status == 200
        generateInvoiceResponse.status == 200
        generateInvoiceResponse.body == null
    }
}
