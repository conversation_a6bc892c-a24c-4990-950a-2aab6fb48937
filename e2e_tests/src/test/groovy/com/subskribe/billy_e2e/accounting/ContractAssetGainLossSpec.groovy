package com.subskribe.billy_e2e.accounting

import com.subskribe.billy_e2e.CsvUtils
import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.entity.EntityBaseGqlSpec
import com.subskribe.billy_e2e.entity.EntitySpec
import com.subskribe.billy_e2e.graphql.OrderGqlSpec
import com.subskribe.billy_e2e.invoice.BaseInvoiceSpec
import com.subskribe.billy_e2e.platformfeature.PlatformFeatureSpec
import com.subskribe.billy_e2e.revrec.RecognitionScheduleSpec
import com.subskribe.billy_e2e.utils.FeatureFlag
import com.subskribe.billy_e2e.utils.Recurrence
import com.subskribe.billy_e2e.utils.Role
import java.time.Instant
import org.apache.commons.csv.CSVRecord
import spock.lang.IgnoreIf
import spock.lang.Shared
import spock.lang.Stepwise
import spock.lang.Unroll

/*
 FX - contract asset gain loss
 differently rated contract asset are offset by realized gain/loss entry, when invoice is posted
 1. execute order:
 - billing cycle: yearly
 - revenue rule: straight line
 2. close accounting period for 3 months
 - this debits contract asset with different exchange rates
 3. post invoice on 4th month
 - this credits to contract asset with another new exchange rate
 - the realized gain/loss entry should offset the exchange rate differences in contract asset account
 */

@Stepwise
class ContractAssetGainLossSpec extends BaseAccountingSpec {

    private static Long START_DATE = DateTimeHelper.parseToInstant("2021-01-01T08:00:00")

    @Shared
    String accountingPeriodId

    @Shared
    List<String> accountingPeriodIds = []

    @Shared
    String subscriptionId

    @Shared
    Long orderStartDate

    @Shared
    Long accountingPeriodStart

    @Shared
    Long accountingPeriodEnd

    @Shared
    Map invoice

    @Shared
    String invoiceNumber

    @Shared
    Long invoiceDate

    @Shared
    BigDecimal contractDebitTotal = 0

    @Shared
    String exchangeRate

    def setupSpec() {
        FeatureFlag.updateFlag(client, "TRANSACTIONAL_FOREIGN_EXCHANGE", true)
        EntityBaseGqlSpec.updateEntity(client, [
            entityId: entityId,
            functionalCurrency: "EUR",
        ])
        EntitySpec.initMultiEntityWithAccounting(client, entityId)
    }

    def "create order"() {
        when:
        def acctPeriodResponse = AccountingPeriodSpec.specifyAccountingPeriod(client, START_DATE)
        accountingPeriodId = acctPeriodResponse.body.id
        accountingPeriodStart = acctPeriodResponse.body.startDate
        accountingPeriodEnd = acctPeriodResponse.body.endDate
        accountingPeriodIds << accountingPeriodId
        orderStartDate = accountingPeriodStart
        invoiceDate = accountingPeriodStart

        // enable accounting after accounting period is specified
        PlatformFeatureSpec.enablePlatformFeature(getUserClient(Role.ADMIN), PlatformFeatureSpec.ACCOUNTING_FEATURE_NAME)
        PlatformFeatureSpec.enablePlatformFeature(getUserClient(Role.ADMIN), PlatformFeatureSpec.REVENUE_RECOGNITION_FEATURE_NAME)

        // create and execute order
        def order = createASC606Order(orderStartDate, Recurrence.ONE_YEAR_CYCLE)
        assert order.id
        Map orderDetail = OrderGqlSpec.getOrderById(client, order.id as String)

        RecognitionScheduleSpec.generateRecognitionSchedule(client, RecognitionScheduleSpec.TRANSACTION_TYPE_ORDER, order.id as String)
        subscriptionId = order.subscriptionId

        then:
        acctPeriodResponse.status == 200
        orderDetail.lineItems.every { lineItem -> lineItem.recognitionRule.id }
        orderDetail.lineItems.every { lineItem -> lineItem.recognitionRule.recognitionType }
    }

    @Unroll
    def "month #month - increasing contract asset"() {
        when:
        def accountingPeriod = AccountingPeriodSpec.getCurrentAccountingPeriod(client)
        accountingPeriodId = accountingPeriod.id as String
        accountingPeriodIds << accountingPeriodId
        assert AccountingPeriodSpec.recognizeRevenue(client)
        HttpResponse pumpEventsResponse = pumpEvents(client)
        assert pumpEventsResponse.status == 200
        HttpResponse initiateAccountingPeriodCloseResponse = initiateAccountingPeriodCloseResponse(client, accountingPeriodId)
        assert initiateAccountingPeriodCloseResponse.status == 200
        assert initiateAccountingPeriodCloseResponse.body.errors.size() == 0
        assert initiateAccountingPeriodCloseResponse.body.data.initiateAccountingPeriodClose.status == "CLOSE_IN_PROGRESS"
        List<CSVRecord> journalEntryRecords = getJournalEntryRecords(client, accountingPeriodId)
        def contractDebitRecord = journalEntryRecords.find {
            it.get(SOURCE_TXN_TYPE_HEADER) == "REVENUE_RECOGNIZED" &&
                    it.get(LEDGER_ACCOUNT_TYPE_HEADER) == "CONTRACT_ASSET"
        }
        assert contractDebitRecord
        def debitAmountRaw = contractDebitRecord.get(FUNCTIONAL_DEBIT_AMOUNT_HEADER)
        assert debitAmountRaw
        def debitAmount = debitAmountRaw as BigDecimal
        contractDebitTotal += debitAmount
        HttpResponse accountingPeriodCloseResponse = AccountingPeriodSpec.closeAccountingPeriodResponse(client, accountingPeriodId)

        then:
        accountingPeriodCloseResponse.body.errors.size() == 0
        accountingPeriodCloseResponse.body.data.updateAccountingPeriodStatus.id == accountingPeriodId
        accountingPeriodCloseResponse.body.data.updateAccountingPeriodStatus.status == "CLOSED"

        where:
        month << (1..3)
    }

    def "post invoice"() {
        when:
        def accountingPeriod = AccountingPeriodSpec.getCurrentAccountingPeriod(client)
        accountingPeriodId = accountingPeriod.id as String
        accountingPeriodIds << accountingPeriodId
        accountingPeriodStart = Instant.parse(accountingPeriod.startDate as String).getEpochSecond()
        invoiceDate = accountingPeriodStart
        HttpResponse generateInvoiceResponse = BaseInvoiceSpec.generateInvoiceWithInvoiceDate(client, subscriptionId, invoiceDate, invoiceDate)
        invoice = generateInvoiceResponse.body
        def draftInvoiceNumber = invoice.invoiceNumber as String

        // now post the invoice which should be in the current new accounting period
        HttpResponse postInvoiceResponse = BaseInvoiceSpec.postInvoice(client, draftInvoiceNumber)
        def postedInvoice = postInvoiceResponse.body
        invoice = postedInvoice

        then:
        generateInvoiceResponse.status == 200
        postedInvoice.invoiceDate == invoiceDate
        postInvoiceResponse.status == 200
    }

    def "create journal entries"() {
        when:
        HttpResponse pumpEventsResponse = pumpEvents(client)
        HttpResponse createJournalEntriesResponse = createJournalEntries(client, accountingPeriodId)

        then:
        pumpEventsResponse.status == 200
        createJournalEntriesResponse.status == 200
    }

    @IgnoreIf({ System.getProperty("spock.runner.saveResults") != "true" })
    def "optionally save journal entries"() {
        when:
        for (String accountingPeriodId in accountingPeriodIds) {
            HttpResponse response = getJournalEntries(client, accountingPeriodId as String)
            CsvUtils.writeCsvTestResults(response, "journal-entries-${accountingPeriodId}.csv")
        }

        then:
        noExceptionThrown()
    }

    def "verify journal entries"() {
        when:
        List<CSVRecord> journalEntryRecords = getJournalEntryRecords(client, accountingPeriodId)
        def contractCreditEntry = journalEntryRecords.find {
            it.get(SOURCE_TXN_TYPE_HEADER) == "INVOICE_POSTED" &&
                    it.get(LEDGER_ACCOUNT_TYPE_HEADER) == "CONTRACT_ASSET"
        }
        assert contractCreditEntry
        def creditAmountRaw = contractCreditEntry.get(FUNCTIONAL_CREDIT_AMOUNT_HEADER)
        assert creditAmountRaw
        def creditAmount = creditAmountRaw as BigDecimal
        def realizedGainLossEntry = journalEntryRecords.find {
            it.get(SOURCE_TXN_TYPE_HEADER) == "REALIZED_GAIN_LOSS_POSTED" &&
                    it.get(LEDGER_ACCOUNT_TYPE_HEADER) == "CONTRACT_ASSET"
        }
        assert realizedGainLossEntry
        boolean isCredit = realizedGainLossEntry.get(CREDIT_AMOUNT_HEADER)
        def realizedGainLossAmountRaw = isCredit ? realizedGainLossEntry.get(FUNCTIONAL_CREDIT_AMOUNT_HEADER) : realizedGainLossEntry.get(FUNCTIONAL_DEBIT_AMOUNT_HEADER)
        assert realizedGainLossAmountRaw
        def realizedGainLossAmount = realizedGainLossAmountRaw as BigDecimal
        def realizedGainLossAmountWithSign = isCredit ? realizedGainLossAmount : -realizedGainLossAmount
        def contractBalance = creditAmount - contractDebitTotal
        def shouldBeCredit = contractBalance < 0 // if contract balance is negative, then realized gain/loss should be a credit

        then:
        journalEntryRecords.size() == 5
        contractDebitTotal == creditAmount + realizedGainLossAmountWithSign
        contractBalance.abs() == realizedGainLossAmount
        shouldBeCredit == isCredit
    }
}
