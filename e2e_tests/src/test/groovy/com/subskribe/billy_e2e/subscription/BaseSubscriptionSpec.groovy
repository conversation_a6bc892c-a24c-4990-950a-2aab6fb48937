package com.subskribe.billy_e2e.subscription

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.utils.Authenticated

class BaseSubscriptionSpec extends Authenticated {

    static final String SUBSCRIPTION_PATH = "/subscriptions"

    static Map getSubscription(HttpClient client, String subscriptionId) {
        return client.get(getSubscriptionPath(subscriptionId)).body
    }

    static String getSubscriptionPath(String subscriptionId) {
        return "${SUBSCRIPTION_PATH}/${subscriptionId}"
    }

    static String getSubscriptionMetricsPath(String subscriptionId) {
        return "${SUBSCRIPTION_PATH}/${subscriptionId}/metrics"
    }

    static String getSubscriptionsPath(int limit = 10) {
        return "${SUBSCRIPTION_PATH}?limit=${limit}"
    }

    static String getSubscriptionsPath(String pageToken, int limit = 10) {
        return "${SUBSCRIPTION_PATH}?limit=${limit}&pageToken=${pageToken}"
    }

    static String getSubscriptionsPathWithAccount(String accountId, int limit = 10) {
        return "${SUBSCRIPTION_PATH}?limit=${limit}&accountId=${accountId}"
    }

    static String getGenerateDraftAmendmentPath(String subscriptionId) {
        return "${SUBSCRIPTION_PATH}/${subscriptionId}/draftAmendment"
    }

    static String getChangeEventsPath(String subscriptionId) {
        return "${SUBSCRIPTION_PATH}/${subscriptionId}/change-events"
    }
}
