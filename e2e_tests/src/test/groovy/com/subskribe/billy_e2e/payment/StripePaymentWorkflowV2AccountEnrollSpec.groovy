package com.subskribe.billy_e2e.payment

import static com.subskribe.billy_e2e.account.AccountSpec.getAccountPaymentMethodPath
import static com.subskribe.billy_e2e.payment.PaymentConstants.*

import com.subskribe.billy_e2e.GqlUtils
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.account.AccountSpec
import com.subskribe.billy_e2e.dunning.DunningSettingSpec
import com.subskribe.billy_e2e.graphql.DunningSettingGqlSpec
import com.subskribe.billy_e2e.graphql.GqlSpec
import com.subskribe.billy_e2e.graphql.PaymentGqlSpec
import com.subskribe.billy_e2e.graphql.PaymentIntegrationSpec
import com.subskribe.billy_e2e.invoice.RecurringInvoiceSpec
import com.subskribe.billy_e2e.invoicesettlement.BaseSettlementApplicationSpec
import com.subskribe.billy_e2e.utils.EventControl
import com.subskribe.billy_e2e.utils.QueuedTaskWait
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class StripePaymentWorkflowV2AccountEnrollSpec extends GqlSpec {

    @Shared
    private TestDataCollector testDataCollector

    def setupSpec() {
        testDataCollector = new TestDataCollector()
        PaymentIntegrationSpec.createTestStripeIntegration(client)
        PaymentConfigSpec.updatePaymentConfig(client, [
            PaymentConfigSpec.PaymentType.WIRE,
            PaymentConfigSpec.PaymentType.CARD,
            PaymentConfigSpec.PaymentType.ACH
        ])
        HttpResponse getTenantResponse = client.get("/tenants")
        testDataCollector.tenantId = getTenantResponse.body.tenantId
    }

    def "Create account and link"() {
        when:
        def accountCreationJson = getAccountCreationJson("Payment-Account-Enroll",
                [
                    PaymentConfigSpec.PaymentType.WIRE,
                    PaymentConfigSpec.PaymentType.CARD
                ])
        HttpResponse accountResponse = AccountSpec.createCustomerAccount(client, accountCreationJson)
        testDataCollector.accountId = accountResponse.locationId

        def query = getAccountPaymentLinkMutation(testDataCollector.accountId)
        def linkResponse = client.post(GQL_PATH, query)
        testDataCollector.accountLinkId = linkResponse.body.data.generateAccountPaymentLink.linkId

        then:
        linkResponse.status == 200
        linkResponse.body.errors.size() == 0
    }

    def "Create setup intent and complete it"() {
        when:
        def query = getCreateSetupIntentMutation(testDataCollector.accountLinkId)
        def response = client.post(GQL_PATH, query)
        def createSetupIntentResult = response.body.data.createSetupIntent
        testDataCollector.setupIntentId = createSetupIntentResult.setupIntentId
        testDataCollector.customerId = createSetupIntentResult.customerId

        def successPayload = StripeWebhookFactory.getSetupSucceededPayload(testDataCollector.accountId, testDataCollector.accountLinkId, testDataCollector.tenantId, testDataCollector.setupIntentId, testDataCollector.customerId)
        HttpResponse webhookResponseForSetupSucceeded = client.post(WEBHOOK_ENDPOINT, successPayload, true)
        QueuedTaskWait.waitForTenantTasksToExistAndComplete(30, client, "incoming-webhook")

        then:
        response.status == 200
        response.body.errors.size() == 0
        webhookResponseForSetupSucceeded.status == 200
    }


    def "Post Invoice and Observe Payment triggered"() {
        when:
        Map firstInvoice = RecurringInvoiceSpec.generateAndPostInvoice(client, false, testDataCollector.accountId, true, IMMEDIATE_PROCESSING_PAYMENT_TERM)
        testDataCollector.invoiceNumber = firstInvoice.invoiceNumber
        testDataCollector.invoiceAmount = firstInvoice.total as BigDecimal
        triggerJob(PAYMENT_PROCESSOR_JOB_NAME, PAYMENT_PROCESSOR_JOB_GROUP_NAME, client)
        triggerJob(PAYMENT_RECONCILER_JOB_NAME, PAYMENT_RECONCILER_JOB_GROUP_NAME, client)
        HttpResponse getPaymentApplicationsResponse = client.get("${BaseSettlementApplicationSpec.SETTLEMENT_PATH}?invoiceNumber=${testDataCollector.invoiceNumber}")
        Map settlementApplication = getPaymentApplicationsResponse.body.get(0)

        then:
        firstInvoice.invoiceNumber != null
        getPaymentApplicationsResponse.status == 200
        settlementApplication.amount == testDataCollector.invoiceAmount
        getPaymentApplicationsResponse.body.get(0).status == "APPLIED_PAYMENT"
    }

    def "Run complete workflow with failing card payment method"() {
        when:
        def accountCreationJson = getAccountCreationJson("Payment-Account-Enroll-Complete-Workflow",
                [
                    PaymentConfigSpec.PaymentType.WIRE,
                    PaymentConfigSpec.PaymentType.CARD
                ])
        HttpResponse accountResponse = AccountSpec.createCustomerAccount(client, accountCreationJson)
        testDataCollector.accountId = accountResponse.locationId

        def query = getAccountPaymentLinkMutation(testDataCollector.accountId)
        def linkResponse = client.post(GQL_PATH, query)
        testDataCollector.accountLinkId = linkResponse.body.data.generateAccountPaymentLink.linkId
        DunningSettingSpec.enableDunningSetting(client, "AUTO_PAYMENT_FAILED")

        then:
        linkResponse.status == 200
        linkResponse.body.errors.size() == 0

        when: "Create setup intent and complete it"
        query = getCreateSetupIntentMutation(testDataCollector.accountLinkId)
        def response = client.post(GQL_PATH, query)
        def createSetupIntentResult = response.body.data.createSetupIntent
        testDataCollector.setupIntentId = createSetupIntentResult.setupIntentId
        testDataCollector.customerId = createSetupIntentResult.customerId

        def invalidCardSetupPayload = StripeWebhookFactory.getSetupSucceededWithInvalidCardPayload(testDataCollector.accountId, testDataCollector.accountLinkId, testDataCollector.tenantId, testDataCollector.setupIntentId, testDataCollector.customerId)
        HttpResponse webhookResponseForSetupSucceeded = client.post(WEBHOOK_ENDPOINT, invalidCardSetupPayload, true)
        QueuedTaskWait.waitForTenantTasksToExistAndComplete(20, client, "incoming-webhook")

        then:
        response.status == 200
        response.body.errors.size() == 0
        webhookResponseForSetupSucceeded.status == 200

        when: "Post Invoice and Observe Payment triggered"
        Map firstInvoice = RecurringInvoiceSpec.generateAndPostInvoice(client, false, testDataCollector.accountId, true, IMMEDIATE_PROCESSING_PAYMENT_TERM)
        testDataCollector.invoiceNumber = firstInvoice.invoiceNumber
        testDataCollector.invoiceAmount = firstInvoice.total as BigDecimal

        triggerJob(PAYMENT_PROCESSOR_JOB_NAME, PAYMENT_PROCESSOR_JOB_GROUP_NAME, client)
        triggerJob(PAYMENT_RECONCILER_JOB_NAME, PAYMENT_RECONCILER_JOB_GROUP_NAME, client)

        HttpResponse getPaymentApplicationsResponse = client.get("${BaseSettlementApplicationSpec.SETTLEMENT_PATH}?invoiceNumber=${testDataCollector.invoiceNumber}")
        assert getPaymentApplicationsResponse.body == []


        // Make sure payment is failed, payment attempt is failed and settlement application is failed, also account payment method should be in suspended state
        Map settlementApplicationAndPaymentDetails = getSettlementApplicationAndPaymentDetails(client, testDataCollector.invoiceNumber)

        def settlementApplication = settlementApplicationAndPaymentDetails.settlementApplication
        def paymentDetails = settlementApplicationAndPaymentDetails.paymentDetails
        Map paymentAttempt = paymentDetails[0].paymentAttempts[0] as Map
        String paymentMethodId = paymentDetails[0].paymentMethodId

        HttpResponse getPaymentMethodsResponse = client.get(getAccountPaymentMethodPath(testDataCollector.accountId, paymentMethodId))
        def accountPaymentMethod = getPaymentMethodsResponse.body

        def mutation = DunningSettingGqlSpec.invoiceDunningDetails(firstInvoice.invoiceNumber as String)
        def gqlResponse = client.post(GQL_PATH, mutation)
        assert gqlResponse.body.errors.size() == 0
        assert gqlResponse.body.data.invoiceDunningDetails.size() == 1
        Map dunningDetails = gqlResponse.body.data.invoiceDunningDetails[0]
        def events = EventControl.getEvents(client, "DUNNING_EMAIL_SENT")

        then:
        firstInvoice.invoiceNumber != null
        getPaymentApplicationsResponse.status == 200
        settlementApplicationAndPaymentDetails != null
        settlementApplication != null
        paymentDetails != null
        paymentAttempt != null
        settlementApplication.status == "FAILED"
        paymentDetails[0].state == "FAILED"
        paymentAttempt.state == "FAILED"
        paymentAttempt.failureReason != null
        getPaymentMethodsResponse != null
        accountPaymentMethod.id == paymentMethodId
        accountPaymentMethod.status == "SUSPENDED"
        dunningDetails != null
        dunningDetails.invoiceNumber == firstInvoice.invoiceNumber
        dunningDetails.amount == testDataCollector.invoiceAmount
        dunningDetails.emailId != null
        dunningDetails.id != null
        dunningDetails.emailSentOn != null
        dunningDetails.reminderType == "AUTO_PAYMENT_FAILED"
        events[0].payload.invoiceNumber == firstInvoice.invoiceNumber
    }

    // TODO : use it later
    private def getPaymentFailedWebhookPayload(String tenantId, Map invoice) {
        def query = getCompletedStripeConnectIntegration()
        def res = client.post(GQL_PATH, query)

        if (res.body.data.stripeConnectIntegration == null) {
            throw new RuntimeException("Stripe Connect Integration is not completed.")
        }

        String connectAccountId = res.body.data.stripeConnectIntegration.connectAccountId

        Map settlementApplicationAndPaymentDetails = getSettlementApplicationAndPaymentDetails(client, invoice.invoiceNumber)

        def paymentDetails = settlementApplicationAndPaymentDetails.paymentDetails

        Map paymentAttempt = paymentDetails[0].paymentAttempts[0] as Map

        String paymentIntentId = paymentAttempt.transactionId

        return StripeWebhookFactory.getPaymentFailedPayload(
                connectAccountId,
                paymentIntentId,
                testDataCollector.invoiceNumber,
                "dummy-link",
                testDataCollector.invoiceAmount,
                tenantId,
                true
                )
    }

    static Map getAccountCreationJson(String paymentDescription, List<PaymentConfigSpec.PaymentType> supportedPaymentTypes) {
        return [
            name                 : "Stripe-" + paymentDescription + "-" + StringUtils.getRandomString(4),
            description          : "Account with stripe payment: " + paymentDescription,
            phoneNumber          : "************",
            isReseller           : false,
            supportedPaymentTypes: supportedPaymentTypes
        ]
    }

    static def getAccountPaymentLinkMutation(String accountId) {
        Map arguments = [accountId: accountId]
        String fields = """{
            accountId
            linkId
        }"""
        return [query: GqlUtils.mutation("generateAccountPaymentLink", arguments, fields)]
    }

    static def getCreateSetupIntentMutation(String linkId) {
        Map arguments = [paymentLinkId: linkId]
        String fields = """{
            clientSecret
            setupIntentId
            customerId
        }"""
        return [query: GqlUtils.mutation("createSetupIntent", arguments, fields)]
    }

    static def getCompletedStripeConnectIntegration() {

        String fields = /{
            connectAccountId
            __typename
        }/
        return [query: GqlUtils.query("stripeConnectIntegration", null , fields)]
    }


    static def getSettlementApplicationAndPaymentDetails(HttpClient client, String invoiceNumber) {
        HttpResponse getSettlementApplicationsResponse = BaseSettlementApplicationSpec.getSettlementApplications(client, invoiceNumber, true)
        if (getSettlementApplicationsResponse.status != 200 || getSettlementApplicationsResponse.body.isEmpty()) {
            throw new RuntimeException("No settlement applications found for invoice: ${invoiceNumber}")
        }

        def settlementApplication = getSettlementApplicationsResponse.body.get(0)
        String paymentId = settlementApplication.paymentId

        HttpResponse paymentDetailResponse = client.post(GQL_PATH, PaymentGqlSpec.getPaymentQuery(paymentId))
        def paymentDetails = paymentDetailResponse.body.data.getPayment

        if (paymentDetails == null) {
            throw new RuntimeException("Payment details not found for invoice: ${invoiceNumber}")
        }

        return [settlementApplication : settlementApplication,
            paymentDetails : paymentDetails]
    }

    private class TestDataCollector {
        String tenantId, accountId, invoiceNumber, accountLinkId, setupIntentId, customerId
        BigDecimal invoiceAmount
    }
}
