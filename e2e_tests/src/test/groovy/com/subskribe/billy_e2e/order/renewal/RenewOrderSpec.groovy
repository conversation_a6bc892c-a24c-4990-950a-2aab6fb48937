package com.subskribe.billy_e2e.order.renewal

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.order.BaseOrderSpec
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.subscription.SubscriptionSpec

class RenewOrderSpec extends BaseOrderSpec {

    def "list price override percent is carried through to amendment"() {
        when:
        Map oneTimePlan = PlanSpec.createPlanWithOneTimeCharge(client, [:], [amount: 20, isListPriceEditable: true, isRenewable: true])
        Map recurringPlan = PlanSpec.createPlanWithRecurringCharge(client, [:], [amount: 100, isListPriceEditable: true, isRenewable: true])
        Map orderJson = OrderSpec.generateOrderJson(client, oneTimePlan)
        orderJson.lineItems[0].quantity = 1
        orderJson.lineItems[0].listPriceOverrideRatio = 1.5
        OrderSpec.addPlanToOrder(orderJson, recurringPlan)
        orderJson.lineItems[1].quantity = 100
        orderJson.lineItems[1].listPriceOverrideRatio = 2

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        OrderSpec.executeDraftOrder(client, response.locationId)
        Map order = OrderSpec.getOrder(client, response.locationId)

        then:
        response.status == 201
        order.lineItems[0].listPriceOverrideRatio == 1.5
        order.lineItems[1].listPriceOverrideRatio == 2.0

        when:
        Map renewOrderJson = getRenewOrderJson(order.subscriptionId)
        Map subscription = SubscriptionSpec.getSubscription(client, order.subscriptionId)
        renewOrderJson.startDate = subscription.endDate
        HttpResponse renewalResponse = client.post(ORDER_PATH, renewOrderJson)
        String renewalId = renewalResponse.locationId

        Map renewOrder = OrderSpec.getOrder(client, renewalId)

        then:
        response.status == 201
        renewOrder.lineItems.size() == order.lineItems.size()
        renewOrder.lineItems.collect { it.listPriceOverrideRatio }.sort() == [1.5, 2.0]
    }

    def "can revert order to draft even if another renewal has been executed"() {
        when:
        Map recurringPlan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = OrderSpec.generateOrderJson(client, recurringPlan)
        Map order = OrderSpec.createAndGetOrder(client, orderJson)

        Map renewOrder1 = createRenewalOrderFromSubscription(client, order.subscriptionId)
        HttpResponse approvalResponse = OrderSpec.changeOrderStatus(client, renewOrder1.id, "SUBMITTED")
        renewOrder1 = OrderSpec.getOrder(client, renewOrder1.id)

        Map renewOrder2 = createRenewalOrderFromSubscription(client, order.subscriptionId)
        OrderSpec.executeDraftOrder(client, renewOrder2.id)
        renewOrder2 = OrderSpec.getOrder(client, renewOrder2.id)

        HttpResponse revertResponse = OrderSpec.changeOrderStatus(client, renewOrder1.id, "DRAFT")
        HttpResponse reApproveResponse = OrderSpec.changeOrderStatus(client, renewOrder1.id, "SUBMITTED")
        OrderSpec.deleteOrder(client, renewOrder1.id)

        then:
        approvalResponse.status == 200
        renewOrder1.status == "APPROVED"
        renewOrder2.status == "EXECUTED"
        revertResponse.status == 200
        reApproveResponse.status == 400
    }

    def "can renew an order with an added deprecated plan if it exists on subscription"() {
        when:
        Map recurringPlan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = OrderSpec.generateOrderJson(client, recurringPlan)
        Map order = OrderSpec.createAndGetOrder(client, orderJson)

        HttpResponse deprecatedPlanResponse = PlanSpec.deprecatePlan(client, recurringPlan.id)

        Map renewOrder = createRenewalOrderFromSubscription(client, order.subscriptionId)
        Map lineItem = renewOrder.lineItems[0]
        Map addLineItemOfDeprecatedPlan = [
            action: "ADD",
            planId: recurringPlan.id,
            chargeId: recurringPlan.charges[0].id,
            quantity: 200
        ]
        renewOrder.lineItems.add(addLineItemOfDeprecatedPlan)
        HttpResponse updateRenewOrderResponse = OrderSpec.updateOrder(client, renewOrder)
        renewOrder = OrderSpec.getOrder(client, renewOrder.id)

        then:
        deprecatedPlanResponse.status == 200
        updateRenewOrderResponse.status == 200
        renewOrder.status == "DRAFT"
        renewOrder.lineItems.size() == 2
    }

    def "cannot add a new deprecated plan on a renewing subscription which doesn't have it"() {
        when:
        Map recurringPlan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = OrderSpec.generateOrderJson(client, recurringPlan)
        Map order = OrderSpec.createAndGetOrder(client, orderJson)

        Map deprecatedPlan = PlanSpec.createPlanWithRecurringCharge(client)
        HttpResponse deprecatePlanResponse = PlanSpec.deprecatePlan(client, deprecatedPlan.id)
        deprecatedPlan = PlanSpec.getPlan(client, deprecatedPlan.id)

        Map renewOrder = createRenewalOrderFromSubscription(client, order.subscriptionId)
        Map addLineItemOfDeprecatedPlan = [
            action: "ADD",
            planId: deprecatedPlan.id,
            chargeId: deprecatedPlan.charges[0].id,
            quantity: 200
        ]
        renewOrder.lineItems.add(addLineItemOfDeprecatedPlan)
        HttpResponse updateRenewOrderResponse = OrderSpec.updateOrder(client, renewOrder)
        renewOrder = OrderSpec.getOrder(client, renewOrder.id)

        then:
        deprecatePlanResponse.status == 200
        deprecatedPlan.status == "DEPRECATED"
        updateRenewOrderResponse.status == 400
        renewOrder.status == "DRAFT"
        renewOrder.lineItems.size() == 1
    }

    static Map createRenewalOrderFromSubscription(HttpClient client, String subscriptionId) {
        Map renewOrderJson = getRenewOrderJson(subscriptionId)
        Map subscription = SubscriptionSpec.getSubscription(client, subscriptionId)
        renewOrderJson.startDate = subscription.endDate
        HttpResponse renewalResponse = client.post(ORDER_PATH, renewOrderJson)
        String renewalId = renewalResponse.locationId
        return OrderSpec.getOrder(client, renewalId)
    }

    static Map getRenewOrderJson(String subscriptionId) {
        return [
            orderType               : "RENEWAL",
            renewalForSubscriptionId   : subscriptionId,
            termLength              : [
                cycle: "YEAR",
                step : 3
            ],
            ownerId                 : "USER-ADMIN"
        ]
    }

    static Map renewExistingSubscription(HttpClient client, String subscriptionId) {
        Map currentSubscription = SubscriptionSpec.getSubscription(client, subscriptionId)

        Map renewOrderJson = getRenewOrderJson(subscriptionId)
        renewOrderJson.startDate = currentSubscription.endDate
        HttpResponse renewOrderResponse = client.post(ORDER_PATH, renewOrderJson)
        if (renewOrderResponse.status != 201) {
            throw new RuntimeException(renewOrderResponse)
        }

        OrderSpec.executeDraftOrder(client, renewOrderResponse.locationId)
        HttpResponse renewedOrderResponse = client.get(getOrderUrl(renewOrderResponse.locationId))
        if (renewedOrderResponse.status != 200) {
            throw new RuntimeException(renewedOrderResponse)
        }

        return SubscriptionSpec.getSubscription(client, renewedOrderResponse.body.subscriptionId)
    }
}
