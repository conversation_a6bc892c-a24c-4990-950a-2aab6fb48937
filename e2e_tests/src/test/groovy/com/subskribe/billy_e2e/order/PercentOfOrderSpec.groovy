package com.subskribe.billy_e2e.order

import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.account.AccountSpec
import com.subskribe.billy_e2e.account.ContactSpec
import com.subskribe.billy_e2e.discount.DiscountSpec
import com.subskribe.billy_e2e.invoice.BaseInvoiceSpec
import com.subskribe.billy_e2e.productcatalog.PlanJsonBuilder
import com.subskribe.billy_e2e.productcatalog.PlanRelationshipSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.productcatalog.ProductSpec
import com.subskribe.billy_e2e.subscription.SubscriptionMetricsSpec
import com.subskribe.billy_e2e.utils.Recurrence
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class PercentOfOrderSpec extends BaseOrderSpec {

    @Shared
    Map targetPlan

    @Shared
    Map flatFeePercentPlan

    @Shared
    Map perUnitPercentPlan

    @Shared
    Map createdOrder

    @Shared
    Map recurringCharge

    @Shared
    Map flatFeePercentCharge

    @Shared
    Map perUnitPercentCharge

    @Shared
    Map account

    @Shared
    Map contact

    @Shared
    String subscriptionId

    @Shared
    Map predefinedDiscount

    def "create charges and make sure to link them as percent of relationship"() {
        when:
        // create plan with all charges
        HttpResponse response = ProductSpec.createProduct(client)
        def productId = response.locationId
        Map planJson = PlanJsonBuilder.getPlanWithAllChargesJson(productId)
        String planId = PlanSpec.createPlan(client, planJson).locationId
        targetPlan = PlanSpec.getPlanWithClient(client, planId)

        // create two percent of charges plans as well
        Map flatFeePercentJson = PlanJsonBuilder.getPlanJsonWithPercentOfCharge(productId, "FLAT_FEE", 10.00)
        String flatFeePercentPlanId = PlanSpec.createPlan(client, flatFeePercentJson).locationId
        flatFeePercentPlan = PlanSpec.getPlanWithClient(client, flatFeePercentPlanId)
        Map perUnitPercentJson = PlanJsonBuilder.getPlanJsonWithPercentOfCharge(productId, "PER_UNIT", 15.00)
        String perUnitPercentPlanId = PlanSpec.createPlan(client, perUnitPercentJson).locationId
        perUnitPercentPlan = PlanSpec.getPlanWithClient(client, perUnitPercentPlanId)

        // now add the relationship between these product and plans
        HttpResponse relationShipOneResponse = PlanRelationshipSpec.createPercentOfRelationship(client, flatFeePercentPlan, targetPlan)
        HttpResponse relationShipTwoResponse = PlanRelationshipSpec.createPercentOfRelationship(client, perUnitPercentPlan, targetPlan)
        predefinedDiscount = DiscountSpec.addAndGetDiscount(client, 0.20)

        then:
        response.status == 201
        planId != null
        relationShipOneResponse.status == 201
        relationShipTwoResponse.status == 201
    }

    def "create order with all the plans and verify flat fee percent of charge shows quantity of 1 when quantity is null"() {
        when:
        account = AccountSpec.createAndGetCustomerAccount(client).body
        contact = ContactSpec.createAndGetContact(client, account.id as String).body
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_YEAR_CYCLE)

        planToOrderLineItems(targetPlan, testOrder, null, 3)
        planToOrderLineItems(flatFeePercentPlan, testOrder, null, 0)

        def flatFeeCharge = flatFeePercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]
        def percentOfChargeLineIndex = testOrder.lineItems.findIndexOf { it.chargeId == flatFeeCharge.chargeId}
        testOrder.lineItems[percentOfChargeLineIndex].quantity = null

        HttpResponse response = client.post(ORDER_PATH, testOrder)
        def newOrder = OrderSpec.getOrder(client, response.locationId)

        // get recurring charges from target plan
        recurringCharge = targetPlan.charges.findAll { it.type == "RECURRING" }[0]
        flatFeePercentCharge = flatFeePercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]

        then:
        response.status == 201

        recurringCharge != null
        flatFeePercentCharge != null

        newOrder.lineItemsNetEffect.size() == 4

        // verify charges
        def targetLineItem = newOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 31.50

        // the flat fee percent of charge should be 10% of target amount 31.52 * (10 / 100) = 3.15
        def flatFeePercentLine = newOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.listUnitPrice == 3.15
        flatFeePercentLine.sellUnitPrice == 3.15
        flatFeePercentLine.amount == 3.15
        flatFeePercentLine.listAmount == 3.15
        flatFeePercentLine.quantity == 1
    }

    def "create order with all the plans and verify flat fee percent of charge shows quantity of 1 when quantity is 0"() {
        when:
        account = AccountSpec.createAndGetCustomerAccount(client).body
        contact = ContactSpec.createAndGetContact(client, account.id as String).body
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_YEAR_CYCLE)

        planToOrderLineItems(targetPlan, testOrder, null, 3)
        planToOrderLineItems(flatFeePercentPlan, testOrder, null, 0)

        def flatFeeCharge = flatFeePercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]
        def percentOfChargeLineIndex = testOrder.lineItems.findIndexOf { it.chargeId == flatFeeCharge.chargeId}
        testOrder.lineItems[percentOfChargeLineIndex].quantity = 0

        HttpResponse response = client.post(ORDER_PATH, testOrder)
        def newOrder = OrderSpec.getOrder(client, response.locationId)

        // get recurring charges from target plan
        recurringCharge = targetPlan.charges.findAll { it.type == "RECURRING" }[0]
        flatFeePercentCharge = flatFeePercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]

        then:
        response.status == 201

        recurringCharge != null
        flatFeePercentCharge != null

        newOrder.lineItemsNetEffect.size() == 4

        // verify charges
        def targetLineItem = newOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 31.50

        // the flat fee percent of charge should be 10% of target amount 31.52 * (10 / 100) = 3.15
        def flatFeePercentLine = newOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.listUnitPrice == 3.15
        flatFeePercentLine.sellUnitPrice == 3.15
        flatFeePercentLine.amount == 3.15
        flatFeePercentLine.listAmount == 3.15
        flatFeePercentLine.quantity == 1
    }

    def "create order with all the plans and verify percent of charge works correctly"() {
        when:
        account = AccountSpec.createAndGetCustomerAccount(client).body
        contact = ContactSpec.createAndGetContact(client, account.id as String).body
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_YEAR_CYCLE)

        planToOrderLineItems(targetPlan, testOrder, null, 3)
        planToOrderLineItems(flatFeePercentPlan, testOrder, null, 1)
        planToOrderLineItems(perUnitPercentPlan, testOrder)

        HttpResponse response = client.post(ORDER_PATH, testOrder)
        createdOrder = OrderSpec.getOrder(client, response.locationId)

        // get recurring charges from target plan
        recurringCharge = targetPlan.charges.findAll { it.type == "RECURRING" }[0]
        flatFeePercentCharge = flatFeePercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]
        perUnitPercentCharge = perUnitPercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]

        then:
        response.status == 201

        recurringCharge != null
        flatFeePercentCharge != null
        perUnitPercentCharge != null

        createdOrder.lineItemsNetEffect.size() == 5

        // verify charges
        def targetLineItem = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 31.50

        // the flat fee percent of charge should be 10% of target amount 31.52 * (10 / 100) = 3.15
        def flatFeePercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.listUnitPrice == 3.15
        flatFeePercentLine.sellUnitPrice == 3.15
        flatFeePercentLine.amount == 3.15
        flatFeePercentLine.listAmount == 3.15
        flatFeePercentLine.quantity == 1

        // the per unit percent of charge should be 15% of target amount 31.52 * (15 / 100) = 4.73
        def perUnitPercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentLine != null
        perUnitPercentLine.listUnitPrice == 4.725
        perUnitPercentLine.sellUnitPrice == 4.725
        perUnitPercentLine.amount == 9.45  // 2 * 4.725
        perUnitPercentLine.listAmount == 9.45 // 2 * 4.725
        perUnitPercentLine.quantity == 2
    }

    def "verify new order metrics"() {
        when:
        def expectedTCV = createdOrder.totalAmount
        def expectedARR = new BigDecimal("44.10") // 31.50 + 3.15 + 9.45

        HttpResponse getOrderMetricsResponse = client.get(OrderMetricsSpec.getOrderMetricsPath(createdOrder.id as String, createdOrder.startDate as Long))

        then:
        getOrderMetricsResponse.status == 200
        getOrderMetricsResponse.body.tcv == expectedTCV
        getOrderMetricsResponse.body.entryArr == expectedARR
    }

    def "when the target line item is updated with discounts the percent of charge is calculated correctly"() {
        when:
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_YEAR_CYCLE)
        testOrder.id = createdOrder.id

        // make target plan twice the quantity with discount of 10 percent
        planToOrderLineItems(targetPlan, testOrder, "0.1", 6)
        planToOrderLineItems(flatFeePercentPlan, testOrder, null, 1)
        planToOrderLineItems(perUnitPercentPlan, testOrder)

        // update the order
        HttpResponse updateResponse = client.put(ORDER_PATH, testOrder)

        // get the order again
        Map updatedOrder = OrderSpec.getOrder(client, createdOrder.id as String)
        then:
        updateResponse.status == 200

        def targetLineItem = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 63.00
        // discount applied: list amount 63 / year, discount = 6.3 / year -> sell amount = 56.7 / year
        targetLineItem.amount == 56.70
        targetLineItem.quantity == 6

        // the flat fee percent of charge should be 10% of target amount 56.7 * (10 / 100) = 5.67
        def flatFeePercentLine = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.listUnitPrice == 6.30
        flatFeePercentLine.sellUnitPrice == 6.30
        flatFeePercentLine.amount == 6.30
        flatFeePercentLine.listAmount == 6.30
        flatFeePercentLine.quantity == 1

        // the per unit percent of charge should be 15% of target amount 56.7 * (15 / 100) = 8.51
        def perUnitPercentLine = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentLine != null
        perUnitPercentLine.listUnitPrice == 9.45
        perUnitPercentLine.sellUnitPrice == 9.45
        perUnitPercentLine.amount == 18.90  // 2 * 9.45
        perUnitPercentLine.listAmount == 18.90 // 2 * 9.45
        perUnitPercentLine.quantity == 2
    }

    def "when the order billing cycle is updated to quarterly the percent of charge is calculated correctly"() {
        when:
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_QUARTER_CYCLE)
        testOrder.id = createdOrder.id

        // make target plan twice the quantity with discount of 10 percent
        planToOrderLineItems(targetPlan, testOrder, "0.1", 6)
        planToOrderLineItems(flatFeePercentPlan, testOrder, null, 1)
        planToOrderLineItems(perUnitPercentPlan, testOrder)

        // update the order
        HttpResponse updateResponse = client.put(ORDER_PATH, testOrder)

        // get the order again
        Map updatedOrder = OrderSpec.getOrder(client, createdOrder.id as String)
        then:
        updateResponse.status == 200

        def targetLineItem = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 63.00
        // discount applied: list amount 15.75 / quarter, discount = 1.575 / quarter -> sell amount =  14.175 => 14.18 / quarter
        // sell amount of 14.18 * 4 = 56.72 / year
        targetLineItem.amount == 56.72
        targetLineItem.quantity == 6

        def flatFeePercentLine = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        // 6.3 / year => ~1.58 / quarter
        flatFeePercentLine.listUnitPrice == 1.575
        flatFeePercentLine.sellUnitPrice == 1.575
        flatFeePercentLine.amount == 6.30
        flatFeePercentLine.listAmount == 6.30
        flatFeePercentLine.quantity == 1

        def perUnitPercentLine = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentLine != null

        // 18.9 / year => ~4.73 / quarter
        perUnitPercentLine.listUnitPrice == 2.3625
        perUnitPercentLine.sellUnitPrice == 2.3625
        perUnitPercentLine.amount == 18.90  // 4.73 * 4
        perUnitPercentLine.listAmount == 18.90 // 4.73 * 4
        perUnitPercentLine.quantity == 2
    }

    def "when discount is present in percent of line items then percent of charge is calculated correctly"() {
        when:
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_QUARTER_CYCLE)
        testOrder.id = createdOrder.id

        // make target plan twice the quantity with discount of 10 percent
        planToOrderLineItems(targetPlan, testOrder, "0.1", 6)
        // now offer 10 percent discount on flat fee percent of
        planToOrderLineItems(flatFeePercentPlan, testOrder, "0.1", 1)
        // now offer 15 percent discount on per unit percent of
        planToOrderLineItems(perUnitPercentPlan, testOrder, "0.15")

        // update the order
        HttpResponse updateResponse = client.put(ORDER_PATH, testOrder)

        // get the order again
        Map updatedOrder = OrderSpec.getOrder(client, createdOrder.id as String)
        createdOrder = updatedOrder

        then:
        updateResponse.status == 200

        def targetLineItem = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 63.00
        // discount is applied
        targetLineItem.amount == 56.72
        targetLineItem.quantity == 6

        // the flat fee percent of charge should be 10% of target amount 56.7 * (10 / 100) = 5.67
        def flatFeePercentLine = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.listUnitPrice == 1.575 // 6.3 / 4
<<<<<<< HEAD
        flatFeePercentLine.sellUnitPrice == 1.4175 // 5.67 (10 percent discount) / 4
        flatFeePercentLine.amount == 5.67 // 1.42 * 4
        flatFeePercentLine.listAmount == 6.30 // 1.58 * 4
||||||| parent of 6adc921962 (additional simplification of percent of calculations)
        flatFeePercentLine.listUnitPrice == 1.58 // 6.3 / 4
        flatFeePercentLine.sellUnitPrice == 1.42 // 5.67 (10 percent discount) / 4
        flatFeePercentLine.amount == 5.68 // 1.42 * 4
        flatFeePercentLine.listAmount == 6.32 // 1.58 * 4
=======
        flatFeePercentLine.sellUnitPrice == 1.415 // 5.67 (10 percent discount) / 4
        flatFeePercentLine.amount == 5.68 // 1.415 * 4
        flatFeePercentLine.listAmount == 6.32 // 1.58 * 4
>>>>>>> 6adc921962 (additional simplification of percent of calculations)
        flatFeePercentLine.quantity == 1


        // the per unit percent of charge should be 15% of target amount 56.7 * (15 / 100) = 8.51
        def perUnitPercentLine = updatedOrder.lineItemsNetEffect.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentLine != null
        // 18.9 / year => ~4.73 / quarter
        perUnitPercentLine.listUnitPrice == 2.3625
<<<<<<< HEAD
        perUnitPercentLine.sellUnitPrice == 2.0075
        perUnitPercentLine.amount == 16.06  // 4.02 * 8
        perUnitPercentLine.listAmount == 18.90 // 4.73 * 4
||||||| parent of 6adc921962 (additional simplification of percent of calculations)
        perUnitPercentLine.listUnitPrice == 2.36
        perUnitPercentLine.sellUnitPrice == 2.01
        perUnitPercentLine.amount == 16.08  // 4.02 * 8
        perUnitPercentLine.listAmount == 18.92 // 4.73 * 4
=======
        perUnitPercentLine.sellUnitPrice == 2.0125
        perUnitPercentLine.amount == 16.08  // 4.02 * 8
        perUnitPercentLine.listAmount == 18.92 // 4.73 * 4
>>>>>>> 6adc921962 (additional simplification of percent of calculations)
        perUnitPercentLine.quantity == 2
    }

    def "when predefined discount is present in percent of line items then percent of charge is calculated correctly for yearly billing"() {
        when:
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_YEAR_CYCLE,
                null,
                DEFAULT_START_DATE,
                List.of(predefinedDiscount))

        // make target plan with discount of 10 percent and predefined discount of 20%
        planToOrderLineItems(targetPlan, testOrder, "0.1", 6, predefinedDiscount.id)
        // now offer 10 percent discount on flat fee percent of with predefined discount of 20%
        planToOrderLineItems(flatFeePercentPlan, testOrder, "0.1", 1, predefinedDiscount.id)
        // now offer 15 percent discount on per unit percent of with predefined discount of 20%
        planToOrderLineItems(perUnitPercentPlan, testOrder, "0.15", 1, predefinedDiscount.id)

        // update the order
        HttpResponse createOrderResponse = client.post(ORDER_PATH, testOrder)

        // get the order again
        Map createdOrder = OrderSpec.getOrder(client, createOrderResponse.locationId as String)

        then:
        createOrderResponse.status == 201

        def targetLineItem = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 63.00
        // discount is applied
        targetLineItem.amount == 45.36 // 63 * 0.9 * 0.8 (predefined discount)
        targetLineItem.quantity == 6
        targetLineItem.discountAmount == 17.64
        targetLineItem.discounts.size() == 1
        targetLineItem.discounts[0].name == "default"
        targetLineItem.discounts[0].percent == 0.1
        targetLineItem.discounts[0].amount == 6.3 // 63 * 0.1
        targetLineItem.predefinedDiscounts.size() == 1
        targetLineItem.predefinedDiscounts[0].id == predefinedDiscount.id
        targetLineItem.predefinedDiscounts[0].percent == 0.2
        targetLineItem.predefinedDiscounts[0].amount == 11.34 // 63 * 0.9 * 0.2

        // the flat fee percent of charge should be 10% of target list amount 63 * (10 / 100) * (0.8 for predefined discount) = 5.67
        def flatFeePercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.listUnitPrice == 6.3 // 63 * 0.1
        flatFeePercentLine.sellUnitPrice == 4.54 // 63 * 0.1 * 0.9 * 0.8
        flatFeePercentLine.amount == 4.54 // 63 * 0.1 * 0.9 * 0.8
        flatFeePercentLine.listAmount == 6.3
        flatFeePercentLine.quantity == 1
        flatFeePercentLine.discountAmount == 1.76
        flatFeePercentLine.discounts.size() == 1
        flatFeePercentLine.discounts[0].name == "default"
        flatFeePercentLine.discounts[0].percent == 0.1
        flatFeePercentLine.discounts[0].amount == 0.63 // 6.3 * 0.1
        flatFeePercentLine.predefinedDiscounts.size() == 1
        flatFeePercentLine.predefinedDiscounts[0].id == predefinedDiscount.id
        flatFeePercentLine.predefinedDiscounts[0].percent == 0.2
        flatFeePercentLine.predefinedDiscounts[0].amount == 1.13 // 6.3 * 0.9 * 0.2

        // the per unit percent of charge should be 15% of target amount 63 * (15 / 100) = 9.45
        def perUnitPercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentLine != null
        perUnitPercentLine.listUnitPrice == 9.45  // 63 * 0.15
        perUnitPercentLine.sellUnitPrice == 6.42 // 63 * 0.15 * 0.85 * 0.8
        perUnitPercentLine.amount == 6.42
        perUnitPercentLine.listAmount == 9.45
        perUnitPercentLine.quantity == 1
        perUnitPercentLine.discountAmount == 3.03
        perUnitPercentLine.discounts.size() == 1
        perUnitPercentLine.discounts[0].name == "default"
        perUnitPercentLine.discounts[0].percent == 0.15
        perUnitPercentLine.discounts[0].amount == 1.42
        perUnitPercentLine.predefinedDiscounts.size() == 1
        perUnitPercentLine.predefinedDiscounts[0].id == predefinedDiscount.id
        perUnitPercentLine.predefinedDiscounts[0].percent == 0.2
        perUnitPercentLine.predefinedDiscounts[0].amount == 1.61
    }

    def "when predefined discount is present in percent of line items then percent of charge is calculated correctly for quarterly billing"() {
        when:
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.ONE_YEAR_CYCLE,
                Recurrence.ONE_QUARTER_CYCLE,
                null,
                DEFAULT_START_DATE,
                List.of(predefinedDiscount))

        // make target plan with discount of 10 percent and predefined discount of 20%
        planToOrderLineItems(targetPlan, testOrder, "0.1", 6, predefinedDiscount.id)
        // now offer 10 percent discount on flat fee percent of with predefined discount of 20%
        planToOrderLineItems(flatFeePercentPlan, testOrder, "0.1", 1, predefinedDiscount.id)
        // now offer 15 percent discount on per unit percent of with predefined discount of 20%
        planToOrderLineItems(perUnitPercentPlan, testOrder, "0.15", 1, predefinedDiscount.id)

        // update the order
        HttpResponse createOrderResponse = client.post(ORDER_PATH, testOrder)

        // get the order again
        Map createdOrder = OrderSpec.getOrder(client, createOrderResponse.locationId as String)

        then:
        createOrderResponse.status == 201

        def targetLineItem = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 63.00
        // discount is applied
        targetLineItem.amount == 45.36 // 63 * 0.9 * 0.8 (predefined discount)
        targetLineItem.quantity == 6
        targetLineItem.discountAmount == 17.64
        targetLineItem.discounts.size() == 1
        targetLineItem.discounts[0].name == "default"
        targetLineItem.discounts[0].percent == 0.1
        targetLineItem.discounts[0].amount == 6.28
        targetLineItem.predefinedDiscounts.size() == 1
        targetLineItem.predefinedDiscounts[0].id == predefinedDiscount.id
        targetLineItem.predefinedDiscounts[0].percent == 0.2
        targetLineItem.predefinedDiscounts[0].amount == 11.36

        // the flat fee percent of charge should be 10% of target list amount 63 * (10 / 100) * (0.8 for predefined discount) = 5.67
        def flatFeePercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.listUnitPrice == 1.575 // 6.3 (for yearly) / 4
        flatFeePercentLine.sellUnitPrice == 1.135
<<<<<<< HEAD
        flatFeePercentLine.amount == 4.54
        flatFeePercentLine.listAmount == 6.30
||||||| parent of 6adc921962 (additional simplification of percent of calculations)
        flatFeePercentLine.listUnitPrice == 1.58 // 6.3 (for yearly) / 4
        flatFeePercentLine.sellUnitPrice == 1.14
        flatFeePercentLine.amount == 4.56
        flatFeePercentLine.listAmount == 6.32
=======
        flatFeePercentLine.amount == 4.56
        flatFeePercentLine.listAmount == 6.32
>>>>>>> 6adc921962 (additional simplification of percent of calculations)
        flatFeePercentLine.quantity == 1
        flatFeePercentLine.discountAmount == 1.76
        flatFeePercentLine.discounts.size() == 1
        flatFeePercentLine.discounts[0].name == "default"
        flatFeePercentLine.discounts[0].percent == 0.1
        flatFeePercentLine.discounts[0].amount == 0.64
        flatFeePercentLine.predefinedDiscounts.size() == 1
        flatFeePercentLine.predefinedDiscounts[0].id == predefinedDiscount.id
        flatFeePercentLine.predefinedDiscounts[0].percent == 0.2
        flatFeePercentLine.predefinedDiscounts[0].amount == 1.12

        // the per unit percent of charge should be 15% of target amount 63 * (15 / 100) = 9.45
        def perUnitPercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentLine != null
<<<<<<< HEAD
        perUnitPercentLine.listUnitPrice == 2.3625  // 63 * 0.15 / 4
        perUnitPercentLine.sellUnitPrice == 1.605 // 63 * 0.15 * 0.85 * 0.8 / 4
        perUnitPercentLine.amount == 6.42
        perUnitPercentLine.listAmount == 9.45
||||||| parent of 6adc921962 (additional simplification of percent of calculations)
        perUnitPercentLine.listUnitPrice == 2.36  // 63 * 0.15 / 4
        perUnitPercentLine.sellUnitPrice == 1.61 // 63 * 0.15 * 0.85 * 0.8 / 4
        perUnitPercentLine.amount == 6.44
        perUnitPercentLine.listAmount == 9.44
=======
        perUnitPercentLine.listUnitPrice == 2.36250  // 63 * 0.15 / 4
        perUnitPercentLine.sellUnitPrice == 1.61250 // 63 * 0.15 * 0.85 * 0.8 / 4
        perUnitPercentLine.amount == 6.44
        perUnitPercentLine.listAmount == 9.44
>>>>>>> 6adc921962 (additional simplification of percent of calculations)
        perUnitPercentLine.quantity == 1
        perUnitPercentLine.discountAmount == 3.03
        perUnitPercentLine.discounts.size() == 1
        perUnitPercentLine.discounts[0].name == "default"
        perUnitPercentLine.discounts[0].percent == 0.15
        perUnitPercentLine.discounts[0].amount == 1.44
        perUnitPercentLine.predefinedDiscounts.size() == 1
        perUnitPercentLine.predefinedDiscounts[0].id == predefinedDiscount.id
        perUnitPercentLine.predefinedDiscounts[0].percent == 0.2
        perUnitPercentLine.predefinedDiscounts[0].amount == 1.6
    }

    def "verify updated order metrics"() {
        when:
        def expectedTCV = createdOrder.totalAmount
        def expectedARR = new BigDecimal("78.43") // 56.7 + 5.68 + 16.08

        HttpResponse getOrderMetricsResponse = client.get(OrderMetricsSpec.getOrderMetricsPath(createdOrder.id as String, createdOrder.startDate as Long))

        then:
        getOrderMetricsResponse.status == 200
        getOrderMetricsResponse.body.tcv == expectedTCV
        getOrderMetricsResponse.body.entryArr == expectedARR
    }

    def "invoice created out of subscription has the correct amount based on proration"() {
        when:
        OrderSpec.executeDraftOrder(client, createdOrder.id as String)
        Map executedOrder = OrderSpec.getOrder(client, createdOrder.id as String)
        Long startDate = executedOrder.startDate as Long
        subscriptionId = executedOrder.subscriptionId
        HttpResponse generateResponse = client.post(BaseInvoiceSpec.getGenerateInvoicePath(subscriptionId, startDate), [:])

        then:
        generateResponse.status == 200
        def invoice = generateResponse.body

        // subscription is yearly but billing is quarterly so 1/4 applies with calender proration
        def targetInvoiceItem = invoice.invoiceItems.findAll({ it.chargeId == recurringCharge.id })[0]
        targetInvoiceItem != null
        targetInvoiceItem.listAmount == 15.75 // 63.00/4 = 15.75
        // discount is applied
        targetInvoiceItem.amount == 14.18 // discount applied
        targetInvoiceItem.quantity == 6

        def flatFeePercentInvoiceItem = invoice.invoiceItems.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentInvoiceItem != null
        flatFeePercentInvoiceItem.listUnitPrice == 1.575 // 1.58 (Already prorated during order execution)
<<<<<<< HEAD
        flatFeePercentInvoiceItem.sellUnitPrice == 1.4175 // 1.42 (Already prorated during order execution)
        flatFeePercentInvoiceItem.listAmount == 1.56 // 10 percent of 15.75 ROUNDED UP
        flatFeePercentInvoiceItem.amount == 1.41 // with discount percent ROUNDED UP
||||||| parent of 6adc921962 (additional simplification of percent of calculations)
        flatFeePercentInvoiceItem.listUnitPrice == 1.58 // 1.58 (Already prorated during order execution)
        flatFeePercentInvoiceItem.sellUnitPrice == 1.42 // 1.42 (Already prorated during order execution)
        flatFeePercentInvoiceItem.listAmount == 1.58 // 10 percent of 15.75 ROUNDED UP
        flatFeePercentInvoiceItem.amount == 1.42 // with discount percent ROUNDED UP
=======
        flatFeePercentInvoiceItem.sellUnitPrice == 1.42 // 1.42 (Already prorated during order execution)
        flatFeePercentInvoiceItem.listAmount == 1.58 // 10 percent of 15.75 ROUNDED UP
        flatFeePercentInvoiceItem.amount == 1.42 // with discount percent ROUNDED UP
>>>>>>> 6adc921962 (additional simplification of percent of calculations)
        flatFeePercentInvoiceItem.quantity == 1

        def perUnitPercentInvoiceItem = invoice.invoiceItems.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentInvoiceItem.listUnitPrice == 2.3625 // 15 percent of 15.75 = 2.3625 (rounded to 2.36) (Already prorated during order execution)
        perUnitPercentInvoiceItem.sellUnitPrice == 2.0075 // with discount (Already prorated during order execution)
        perUnitPercentInvoiceItem.listAmount == 4.71 // 2 * 2.3625 ROUNDED UP with proration
        perUnitPercentInvoiceItem.amount == 4.00 // 2 * 2.01
        perUnitPercentInvoiceItem.quantity == 2
    }

    def "verify subscription metrics"() {
        when:
        def expectedTCV = createdOrder.totalAmount
        def expectedARR = new BigDecimal("78.43") // 56.7 + 5.68 + 16.08

        HttpResponse getSubscriptionMetricsResponse = client.get(SubscriptionMetricsSpec.getSubscriptionMetricsPath(subscriptionId, createdOrder.startDate as Long))

        then:
        getSubscriptionMetricsResponse.status == 200
        getSubscriptionMetricsResponse.body.tcv == expectedTCV
        getSubscriptionMetricsResponse.body.entryArr == expectedARR
    }

    def "create 3 year order that includes percent of charge"() {
        when:
        account = AccountSpec.createAndGetCustomerAccount(client).body
        contact = ContactSpec.createAndGetContact(client, account.id as String).body
        Map testOrder = getOrderRequestJson(account.id as String,
                contact.id as String,
                "NEW",
                Recurrence.THREE_YEAR_CYCLE,
                Recurrence.ONE_YEAR_CYCLE)

        planToOrderLineItems(targetPlan, testOrder, null, 3)
        planToOrderLineItems(flatFeePercentPlan, testOrder, null, 1)
        planToOrderLineItems(perUnitPercentPlan, testOrder)

        HttpResponse response = client.post(ORDER_PATH, testOrder)
        createdOrder = OrderSpec.getOrder(client, response.locationId)

        // get recurring charges from target plan
        recurringCharge = targetPlan.charges.findAll { it.type == "RECURRING" }[0]
        flatFeePercentCharge = flatFeePercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]
        perUnitPercentCharge = perUnitPercentPlan.charges.findAll { it.type == "PERCENTAGE_OF" }[0]

        then:
        response.status == 201

        // verify charges
        def targetLineItem = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == recurringCharge.id })[0]
        targetLineItem != null
        targetLineItem.listAmount == 94.50

        // the flat fee percent of charge should be 10% of target amount 31.5 * (10 / 100) * 3 = 9.45
        def flatFeePercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentLine != null
        flatFeePercentLine.amount == 9.45

        // the per unit percent of charge should be 15% of target amount 31.5 * (15 / 100) * 2 * 3 = 28.35
        def perUnitPercentLine = createdOrder.lineItemsNetEffect.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentLine.amount == 28.36
        perUnitPercentLine.quantity == 2
    }

    def "verify memoized invoice line items for the order"() {
        when:
        HttpResponse memoizedInvoiceItemsResponse = client.get("/invoices/test/memoizedInvoiceItems?subscriptionId=${subscriptionId}")

        then:
        memoizedInvoiceItemsResponse.status == 200
        def memoizedInvoiceItems = memoizedInvoiceItemsResponse.body

        // Verify the memoized invoice items
        def targetInvoiceItem = memoizedInvoiceItems.findAll({ it.chargeId == recurringCharge.id })[0]
        targetInvoiceItem != null
        targetInvoiceItem.listAmount == 15.75 // 63.00/4 = 15.75
        targetInvoiceItem.amount == 14.18 // discount applied
        targetInvoiceItem.quantity == 6

        def flatFeePercentInvoiceItem = memoizedInvoiceItems.findAll({ it.chargeId == flatFeePercentCharge.id })[0]
        flatFeePercentInvoiceItem != null
        flatFeePercentInvoiceItem.listUnitPrice == 1.575
        flatFeePercentInvoiceItem.sellUnitPrice == 1.4175
        flatFeePercentInvoiceItem.listAmount == 1.56 // 10 percent of 15.75 ROUNDED UP
        flatFeePercentInvoiceItem.amount == 1.41 // with discount percent ROUNDED UP
        flatFeePercentInvoiceItem.quantity == 1

        def perUnitPercentInvoiceItem = memoizedInvoiceItems.findAll({ it.chargeId == perUnitPercentCharge.id })[0]
        perUnitPercentInvoiceItem.listUnitPrice == 2.3625 // 4.73/qty(2)
        perUnitPercentInvoiceItem.sellUnitPrice == 2.0075 // 4.02/qty(2)
        perUnitPercentInvoiceItem.listAmount == 4.71 // 2 * 2.36 ROUNDED UP with proration
        perUnitPercentInvoiceItem.amount == 4.00 // 2 * 2.01
        perUnitPercentInvoiceItem.quantity == 2
    }

    static void planToOrderLineItems(Map targetPlan, LinkedHashMap<String, Object> testOrder, String discountPercent = null, int quantity = 2, String predefinedDiscount = null) {
        for (def charge : targetPlan.charges) {
            def orderLineItem =
                    OrderSpec.getOrderLineItemJson(targetPlan.id as String,
                    charge.id as String,
                    DEFAULT_START_DATE,
                    quantity)
            if (discountPercent != null) {
                orderLineItem.discounts = [
                    [
                        name   : "default",
                        percent: discountPercent
                    ]
                ]
            }
            if (predefinedDiscount != null) {
                orderLineItem.predefinedDiscounts = [
                    predefinedDiscount
                ]
            }
            testOrder.lineItems.add(orderLineItem)
        }
    }
}
