package com.subskribe.billy_e2e.order

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.graphql.OrderGqlSpec
import com.subskribe.billy_e2e.order.amendment.AmendmentOrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.tax.TaxRateSpec

class OrderTaxPreviewSpec extends BaseOrderSpec {

    def "create order with tax percent"() {
        when:
        Map taxRate = TaxRateSpec.createAndGetTaxRate(client, [taxCode: null, taxPercentage: 0.2, taxInclusive: false])
        Map plan = PlanSpec.createPlanWithRecurringCharge(client, [:], [taxRateId: taxRate.id, amount: 100])
        Map order = OrderSpec.createOrderWithPlan(client, plan)

        then:
        order.lineItems.size() == 1
        order.lineItems.get(0).taxEstimate == 200
        order.lineItems.get(0).amount == 1000

        order.lineItemsNetEffect.size() == 1
        order.lineItemsNetEffect.get(0).taxEstimate == 200
        order.lineItemsNetEffect.get(0).amount == 1000

        order.taxEstimate == 200
        order.totalAmount == 1000
    }

    def "create order with no tax percent"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringCharge(client, [:], [amount: 100])
        Map order = OrderSpec.createOrderWithPlan(client, plan)

        then:
        order.lineItems.size() == 1
        order.lineItems.get(0).taxEstimate == null
        order.lineItems.get(0).amount == 1000

        order.lineItemsNetEffect.size() == 1
        order.lineItemsNetEffect.get(0).taxEstimate == null
        order.lineItemsNetEffect.get(0).amount == 1000

        order.taxEstimate == null
        order.totalAmount == 1000
    }

    def "create order with integration tax rate"() {
        when:
        Map taxRate = TaxRateSpec.createAndGetTaxRate(client)
        Map plan = PlanSpec.createPlanWithRecurringCharge(client, [:], [taxRateId: taxRate.id, amount: 100])
        Map order = OrderSpec.createOrderWithPlan(client, plan)

        then:
        order.lineItems.size() == 1
        order.lineItems.get(0).taxEstimate == null
        order.lineItems.get(0).amount == 1000

        order.lineItemsNetEffect.size() == 1
        order.lineItemsNetEffect.get(0).taxEstimate == null
        order.lineItemsNetEffect.get(0).amount == 1000

        order.taxEstimate == null
        order.totalAmount == 1000
    }

    def "create order with percent tax and no tax works"() {
        when:
        Map taxRate = TaxRateSpec.createAndGetTaxRate(client, [taxCode: null, taxPercentage: 0.2, taxInclusive: false])
        Map planWithPercent = PlanSpec.createPlanWithRecurringCharge(client, [:], [taxRateId: taxRate.id, amount: 100])
        Map planWithNoTax = PlanSpec.createPlanWithRecurringCharge(client, [:], [amount: 100])
        Map orderJson = OrderSpec.generateOrderJson(client, planWithPercent)
        OrderSpec.addPlanToOrder(orderJson, planWithNoTax)
        Map order = OrderSpec.createAndGetOrder(client, orderJson)

        Map taxPercentLine = order.lineItems.find { it.chargeId == planWithPercent.charges.get(0).id }
        Map noTaxLine = order.lineItems.find { it.chargeId == planWithNoTax.charges.get(0).id }

        then:
        order.lineItems.size() == 2

        taxPercentLine.taxEstimate == 200
        taxPercentLine.amount == 1000

        noTaxLine.taxEstimate == null
        noTaxLine.amount == 1000

        order.taxEstimate == 200
        order.totalAmount == 2000
    }

    def "create order with mixed fails"() {
        when:
        Map taxRate = TaxRateSpec.createAndGetTaxRate(client, [taxCode: null, taxPercentage: 0.2, taxInclusive: false])
        Map integrationTaxRate = TaxRateSpec.createAndGetTaxRate(client)
        Map planWithPercent = PlanSpec.createPlanWithRecurringCharge(client, [:], [taxRateId: taxRate.id, amount: 100])
        Map planWithIntegrationTax = PlanSpec.createPlanWithRecurringCharge(client, [:], [taxRateId: integrationTaxRate.id, amount: 100])
        Map orderJson = OrderSpec.generateOrderJson(client, planWithPercent)
        OrderSpec.addPlanToOrder(orderJson, planWithIntegrationTax)
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("cannot mix tax rates")
    }

    def "create amendment order with tax percent"() {
        when:
        Map taxRate = TaxRateSpec.createAndGetTaxRate(client, [taxCode: null, taxPercentage: 0.2, taxInclusive: false])
        Map plan = PlanSpec.createPlanWithRecurringCharge(client, [:], [taxRateId: taxRate.id, amount: 100])
        Map order = OrderSpec.createOrderWithPlan(client, plan)

        then:
        order.lineItems.size() == 1
        order.lineItems.get(0).taxEstimate == 200
        order.lineItems.get(0).amount == 1000

        order.lineItemsNetEffect.size() == 1
        order.lineItemsNetEffect.get(0).taxEstimate == 200
        order.lineItemsNetEffect.get(0).amount == 1000

        order.taxEstimate == 200
        order.totalAmount == 1000

        when:
        long amendmentDate = DateTimeHelper.plusMonths(order.startDate, 6)
        Map amendmentJson = AmendmentOrderSpec.getAmendmentJson(order, amendmentDate)
        Map upsellLine = order.lineItems.get(0).clone()
        upsellLine.action = "UPDATE"
        upsellLine.quantity = 20
        upsellLine.effectiveDate = amendmentDate
        amendmentJson.lineItems = [upsellLine]
        Map amendment = OrderSpec.createAndGetOrder(client, amendmentJson)
        Map amendmentGql = OrderGqlSpec.getOrderById(client, amendment.id)

        then:
        amendment.lineItems.size() == 1
        amendment.lineItems.get(0).amount == 500 // 1000 - 500
        amendment.lineItems.get(0).taxEstimate == 100 // 200 - 100

        amendment.lineItemsNetEffect.size() == 2
        amendment.lineItemsNetEffect.collect { it.amount }.sort() == [-500, 1000]
        amendment.lineItemsNetEffect.collect { it.taxEstimate }.sort() == [-100, 200]
    }

}