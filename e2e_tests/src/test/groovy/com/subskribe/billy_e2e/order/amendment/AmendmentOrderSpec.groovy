package com.subskribe.billy_e2e.order.amendment

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.order.BaseOrderSpec
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanJsonBuilder
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.productcatalog.ProductSpec
import com.subskribe.billy_e2e.subscription.BaseSubscriptionSpec
import com.subskribe.billy_e2e.subscription.SubscriptionSpec
import com.subskribe.billy_e2e.utils.Recurrence
import java.time.Instant

class AmendmentOrderSpec extends BaseOrderSpec {

    def "user provided order id in request should not be used"() {
        when:
        Map order = OrderSpec.createAndGetOrder(client)
        Map updateOrderJson = getAmendmentJson(order, order.lineItems.get(0), order.startDate as Long)
        String orderId = 'ORD-' + StringUtils.getRandomString(5)
        updateOrderJson.id = orderId

        HttpResponse addOrderResponse = client.post(ORDER_PATH, updateOrderJson)

        then:
        addOrderResponse.status == 400
    }

    def "Amendment order without a line item action returns 400"() {
        when:
        Map order = OrderSpec.createOrderWithTenantDiscounts(client)
        Map lineItem = order.lineItems.get(0)
        lineItem.action = null
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentJson = getAmendmentJson(order, lineItem, targetDate)
        amendmentJson.lineItems[0].action = null
        HttpResponse response = client.post(ORDER_PATH, amendmentJson)

        then:
        response.status == 400
        response.error.contains("line item action is required")
    }

    def "Amend recurring charge missing tenant discounts updates the predefined discounts list on POST"() {
        when:
        Map order = OrderSpec.createOrderWithTenantDiscounts(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentJson = getAmendmentJson(order, lineItem, targetDate)
        HttpResponse response = client.post(ORDER_PATH, amendmentJson)

        Map updatedOrder = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        updatedOrder.subscriptionId == order.subscriptionId
        updatedOrder.totalAmount == 98.44
        updatedOrder.lineItems.size() == 1
        updatedOrder.lineItems[0].discountAmount == 32.81
        updatedOrder.lineItems[0].subscriptionChargeId == order.lineItems[0].subscriptionChargeId
        updatedOrder.lineItems[0].subscriptionChargeGroupId == order.lineItems[0].subscriptionChargeGroupId
        updatedOrder.lineItems[0].isRamp == order.lineItems[0].isRamp
        updatedOrder.lineItemsNetEffect.size() == 2
        updatedOrder.lineItemsNetEffect.any {
            it.quantity == -10
            it.amount == -196.88
            it.discountAmount == -65.62
        }
        updatedOrder.lineItemsNetEffect.any {
            it.quantity == 15
            it.amount == 295.32
            it.discountAmount == 98.43
        }
        updatedOrder.ownerId == "USER-ADMIN"
        updatedOrder.autoRenew == true

        order.discounts == updatedOrder.discounts
        order.autoRenew == true
        order.lineItems[0].predefinedDiscounts[0].id == updatedOrder.lineItems[0].predefinedDiscounts[0].id
        order.lineItems[0].predefinedDiscounts[0].percent == updatedOrder.lineItems[0].predefinedDiscounts[0].percent
        order.lineItems[0].listAmount == 315
        order.lineItems[0].amount == 236.25
        order.lineItems[0].discountAmount == 78.75


        amendmentJson.predefinedDiscounts == null
        updatedOrder.predefinedDiscounts.size() == 1
        updatedOrder.predefinedDiscounts == order.predefinedDiscounts
    }

    def "Amend recurring charge without any changes return amounts and discounts as 0"() {
        when:
        Map order = OrderSpec.createOrderWithTenantDiscounts(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentJson = getAmendmentJson(order, lineItem, targetDate)
        amendmentJson.lineItems[0].quantity = lineItem.quantity
        amendmentJson.lineItems[0].action = "NONE"
        HttpResponse response = client.post(ORDER_PATH, amendmentJson)

        Map updatedOrder = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        updatedOrder.subscriptionId == order.subscriptionId
        updatedOrder.totalAmount == 0
        updatedOrder.lineItems.size() == 1
        updatedOrder.lineItems[0].discountAmount == 0
        updatedOrder.lineItems[0].amount == 0
        updatedOrder.lineItems[0].subscriptionChargeId == order.lineItems[0].subscriptionChargeId
        updatedOrder.lineItems[0].subscriptionChargeGroupId == order.lineItems[0].subscriptionChargeGroupId
        updatedOrder.lineItems[0].isRamp == order.lineItems[0].isRamp
        updatedOrder.lineItemsNetEffect.size() == 0
        updatedOrder.ownerId == "USER-ADMIN"
        updatedOrder.autoRenew == true

        order.discounts == updatedOrder.discounts
        order.autoRenew == true
        order.lineItems[0].predefinedDiscounts[0].id == updatedOrder.lineItems[0].predefinedDiscounts[0].id
        order.lineItems[0].predefinedDiscounts[0].percent == updatedOrder.lineItems[0].predefinedDiscounts[0].percent
        order.lineItems[0].listAmount == 315
        order.lineItems[0].amount == 236.25
        order.lineItems[0].discountAmount == 78.75

        amendmentJson.predefinedDiscounts == null
        updatedOrder.predefinedDiscounts.size() == 1
        updatedOrder.predefinedDiscounts == order.predefinedDiscounts
    }

    def "Amend recurring charge missing tenant discounts throws returns 400 on PUT"() {
        when:
        Map order = OrderSpec.createOrderWithTenantDiscounts(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentOrderJson = getAmendmentJson(order, lineItem, targetDate)
        HttpResponse amendmentResponse = client.post(ORDER_PATH, amendmentOrderJson)
        Map amendmentOrder = client.get(getOrderUrl(amendmentResponse.locationId)).body

        // delete the predefined discounts at the order level for amendment updates
        amendmentOrderJson.id = amendmentOrder.id
        HttpResponse updateAmendmentResponse = client.put(ORDER_PATH, amendmentOrderJson)

        then:
        amendmentResponse.status == 201
        amendmentOrder.subscriptionId == order.subscriptionId
        order.lineItems[0].predefinedDiscounts[0].id == amendmentOrder.lineItems[0].predefinedDiscounts[0].id
        order.lineItems[0].predefinedDiscounts[0].percent == amendmentOrder.lineItems[0].predefinedDiscounts[0].percent
        updateAmendmentResponse.status == 400
        updateAmendmentResponse.error.contains("Following discounts are missing at the order level. Discount Ids:")
    }

    def "amend recurring charge with inline discounts subscription"() {
        when:
        Map order = OrderSpec.createRecurringOrderWithDiscount(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate, lineItem.discounts as List<Map>)
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        Map updatedOrder = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        updatedOrder.subscriptionId == order.subscriptionId
        updatedOrder.lineItems[0].subscriptionChargeId == order.lineItems[0].subscriptionChargeId
        updatedOrder.lineItems[0].subscriptionChargeGroupId == order.lineItems[0].subscriptionChargeGroupId
        updatedOrder.lineItems[0].isRamp == order.lineItems[0].isRamp
        updatedOrder.lineItems[0].discounts[0].name == order.lineItems[0].discounts[0].name
        updatedOrder.lineItems[0].discounts[0].percent == order.lineItems[0].discounts[0].percent
        updatedOrder.ownerId == "USER-ADMIN"
    }

    def "multiple amendments to same subscription"() {
        when:
        Map order = OrderSpec.createRecurringOrderWithDiscount(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 3, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate, lineItem.discounts as List<Map>, 15)
        HttpResponse saveAmendmentResponse = client.post(ORDER_PATH, updateOrderJson)
        String amendment1Id = saveAmendmentResponse.locationId
        OrderSpec.executeDraftOrder(client, amendment1Id as String)
        Map amendment1 = client.get(getOrderUrl(amendment1Id)).body
        Map subscription = SubscriptionSpec.getSubscription(client, order.subscriptionId as String)

        // find eligible subscription charges to amend directly from the current subscription
        Map targetSubscriptionCharges = subscription.charges.find { it.startDate <= targetDate && it.endDate > targetDate} as Map

        then:
        targetSubscriptionCharges.size() > 0

        when:
        targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        lineItem = amendment1.lineItems.get(0)

        // update lineItem subscriptionChargeId to eligible subscription charge's id.
        // This is needed because when an UPDATE amendment is processed, the existing subscription charge is cut in 2: 1 representing the state before the amendment and another after
        // the amendment line item subscriptionChargeId may be the subscription charge state before the amendment effective date, which leads to an error when amended again with a later effective date
        lineItem.subscriptionChargeId = targetSubscriptionCharges.id
        updateOrderJson = getAmendmentJson(order, lineItem, targetDate, lineItem.discounts as List<Map>, 20)
        saveAmendmentResponse = client.post(ORDER_PATH, updateOrderJson)
        String amendment2Id = saveAmendmentResponse.locationId
        OrderSpec.executeDraftOrder(client, amendment2Id as String)

        subscription = SubscriptionSpec.getSubscription(client, order.subscriptionId as String)
        HttpResponse getFirstOrderResponse = client.get("${BaseSubscriptionSpec.getSubscriptionPath(order.subscriptionId as String)}/firstOrder")

        then:
        subscription.version == 3
        subscription.orders == [
            order.id,
            amendment1Id,
            amendment2Id
        ]
        getFirstOrderResponse.body.id == order.id
    }

    def "amend recurring ramp subscription"() {
        when:
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate)
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        Map updatedOrder = client.get(getOrderUrl(response.locationId)).body

        OrderSpec.executeDraftOrder(client, updatedOrder.id)
        addMetricsToOrderMap(updatedOrder.id, updatedOrder, client)

        Map subscription = SubscriptionSpec.getSubscription(client, updatedOrder.subscriptionId)
        Map subscriptionMetrics = SubscriptionSpec.getSubscriptionMetrics(client, updatedOrder.subscriptionId).body

        then:
        response.status == 201
        order.subscriptionTargetVersion == 1
        updatedOrder.subscriptionId == order.subscriptionId
        updatedOrder.lineItems[0].subscriptionChargeId == order.lineItems[0].subscriptionChargeId
        updatedOrder.lineItems[0].subscriptionChargeGroupId == order.lineItems[0].subscriptionChargeGroupId
        updatedOrder.lineItems[0].isRamp == order.lineItems[0].isRamp
        updatedOrder.status == "DRAFT"
        updatedOrder.subscriptionTargetVersion == 2
        updatedOrder.metrics.averageArr == new BigDecimal("10.50") // 26.25 / 2.5 years
        updatedOrder.ownerId == "USER-ADMIN"

        subscription.charges.size() == 4
        subscription.charges[0].startDate == lineItem.effectiveDate
        subscription.charges[0].endDate == targetDate
        subscription.charges[0].isRamp == true
        subscription.charges[1].startDate == targetDate
        subscription.charges[1].endDate == lineItem.endDate
        subscription.charges[1].isRamp == true
        subscriptionMetrics.averageArr == new BigDecimal("113.75")
    }

    def "amendment order with orderLine before start date returns 400"() {
        when:
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 18, DateTimeHelper.DEFAULT_TZ)
        lineItem.action = "UPDATE"
        lineItem.quantity += 10
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate)
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 400
    }

    def "amendment order with deprecated plans"() {
        when:
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        Map orderJson = OrderSpec.generateOrderJson(client, plan)
        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = OrderSpec.getOrder(client, response.locationId)
        String orderId = order.id
        OrderSpec.executeDraftOrder(client, orderId)
        order = OrderSpec.getOrder(client, orderId)

        order.id = null
        order.lineItems[0].effectiveDate = DEFAULT_START_DATE
        order.lineItems[0].action = "UPDATE"
        order.lineItems[0].quantity = 25
        order.orderType = "AMENDMENT"

        Map addLineItem = OrderSpec.getOrderLineItemJson(plan.id, plan.charges.get(0).id, DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ))
        order.lineItems.add(addLineItem)
        addLineItem.action = "ADD"

        // deprecate legacy plan
        HttpResponse deprecateResponse = PlanSpec.deprecatePlan(client, plan.id as String)

        // create amendment order
        HttpResponse amendmentResponse = client.post(ORDER_PATH, order)
        Map amendmentOrder = OrderSpec.getOrder(client, amendmentResponse.locationId)

        // update order with new deprecated plan is not allowed
        Map plan2 = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        HttpResponse deprecateResponse2 = PlanSpec.deprecatePlan(client, plan2.id as String)
        amendmentOrder = OrderSpec.addPlanToOrder(amendmentOrder, plan2)
        amendmentOrder.lineItems[2].action = "ADD"
        HttpResponse amendmentResponse2 = client.put(ORDER_PATH, amendmentOrder)

        then:
        response.status == 201
        deprecateResponse.status == 200
        amendmentResponse.status == 201
        deprecateResponse2.status == 200
        amendmentResponse2.status == 400
        amendmentResponse2.error.contains("deprecated and cannot be added to order")
    }

    def "amendment order with disallowed line item action"() {
        when:
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate)
        updateOrderJson.lineItems[0].action = "RENEWAL"
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 400
        response.error.contains("is not allowed for change order service route")
    }

    def "amendment line item block pricing above maximum quantity"() {
        when:
        Map plan = PlanSpec.createPlanWithBlockPriceCharge(client, [:], [maxQuantity: 50L])
        Map order = OrderSpec.createOrderWithPlan(client, plan)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate, null, 100)
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 400
        response.error.contains("cannot be greater than maximum quantity")
    }

    def "add new line item to amendment triggers minimum quantity check"() {
        when:
        Map plan = PlanSpec.createPlanWithBlockPriceCharge(client, [:], [minQuantity: 10L, maxQuantity: 50L])
        Map order = OrderSpec.createOrderWithPlan(client, plan)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate, null, 25)
        lineItem.effectiveDate = targetDate
        lineItem.quantity = 5L
        lineItem.subscriptionChargeId = null
        updateOrderJson.lineItems.add(lineItem)
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 400
        response.error.contains("cannot be less than minimum quantity")
    }

    def "remove line item from amendment does not trigger minimum quantity check"() {
        when:
        Map plan = PlanSpec.createPlanWithBlockPriceCharge(client, [:], [minQuantity: 10L, maxQuantity: 50L])
        Map order = OrderSpec.createOrderWithPlan(client, plan)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate, null, 25)
        lineItem.effectiveDate = targetDate
        lineItem.quantity = 20L
        lineItem.subscriptionChargeId = null
        updateOrderJson.lineItems.add(lineItem)
        updateOrderJson.lineItems.get(0).action = "REMOVE"
        updateOrderJson.lineItems.get(0).quantity = 0
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 201
    }

    def "duplicate amendment charge line item with improper effective date"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map order = OrderSpec.createOrderWithPlan(client, plan)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate, null, 25)
        lineItem.effectiveDate = DateTimeHelper.plusMonths(targetDate, -3, DateTimeHelper.DEFAULT_TZ)
        lineItem.endDate = null
        lineItem.subscriptionChargeId = null
        lineItem.subscriptionChargeGroupId = null
        lineItem.quantity = 20L
        lineItem.action = "ADD"
        updateOrderJson.lineItems.add(lineItem)
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 400
        response.error.contains("lineItem effective date")
    }

    def "duplicate amendment charge line item with improper end date"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map order = OrderSpec.createOrderWithPlan(client, plan)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, lineItem, targetDate, null, 25)
        lineItem.effectiveDate = targetDate
        lineItem.endDate = DateTimeHelper.plusMonths(targetDate, 12, DateTimeHelper.DEFAULT_TZ)
        lineItem.subscriptionChargeId = null
        lineItem.subscriptionChargeGroupId = null
        lineItem.quantity = 20L
        lineItem.action = "ADD"
        updateOrderJson.lineItems.add(lineItem)
        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 400
        response.error.contains("lineItem end date")
    }

    def "create amendment with plan that has multiple charges"() {
        when:
        // create order with plan that has 2 charges
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getRecurringPerUnitChargeJson(),
            PlanJsonBuilder.getVolumeChargeJson()
        ])
        String planId = PlanSpec.createPlan(client, planJson).locationId as String
        Map plan = PlanSpec.getPlan(client, planId)
        Map order = OrderSpec.createOrderWithPlan(client, plan)

        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, targetDate)
        updateOrderJson.lineItems.add(order.lineItems.get(0))
        updateOrderJson.lineItems.get(0).action = 'NONE'
        updateOrderJson.lineItems.get(0).effectiveDate = null
        updateOrderJson.lineItems.add(order.lineItems.get(1))
        updateOrderJson.lineItems.get(1).action = 'NONE'
        updateOrderJson.lineItems.get(1).effectiveDate = null

        // add duplicate of only 1 line from original plan
        updateOrderJson.lineItems.add(OrderSpec.getOrderLineItemJson(planId, plan.charges.get(0).id, targetDate))
        updateOrderJson.lineItems.get(2).action = 'ADD'
        HttpResponse duplicateOfOneChargeResponse = client.post(ORDER_PATH, updateOrderJson)

        // new plan with 2 charges
        Map planJson2 = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getRecurringPerUnitChargeJson(),
            PlanJsonBuilder.getVolumeChargeJson()
        ])
        String planId2 = PlanSpec.createPlan(client, planJson2).locationId as String
        Map plan2 = PlanSpec.getPlan(client, planId2)

        // only add 1 of the 2 charges of the new plan
        updateOrderJson.lineItems.add(OrderSpec.getOrderLineItemJson(planId2, plan2.charges.get(0).id, targetDate))
        updateOrderJson.lineItems.get(3).action = 'ADD'
        HttpResponse notAllChargesPresentResponse = client.post(ORDER_PATH, updateOrderJson)

        // add second charge of the new plan
        updateOrderJson.lineItems.add(OrderSpec.getOrderLineItemJson(planId2, plan2.charges.get(1).id, targetDate))
        updateOrderJson.lineItems.get(4).action = 'ADD'
        HttpResponse allChargesPresentInPlanResponse = client.post(ORDER_PATH, updateOrderJson)

        then:
        duplicateOfOneChargeResponse.status == 201

        notAllChargesPresentResponse.status == 400
        notAllChargesPresentResponse.error.matches(".*charges .* missing in order.*")

        allChargesPresentInPlanResponse.status == 201
    }

    def "prepaid drawdown can be added to amendment without existing prepaid drawdown"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map order = OrderSpec.createOrderWithPlan(client, plan)

        Map prepaidDrawdownPlan = PlanSpec.createPrepaidDrawdownPlan(client)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, targetDate)
        Map lineItem1 = OrderSpec.getOrderLineItemJson(prepaidDrawdownPlan.id, prepaidDrawdownPlan.charges.get(0).id, targetDate, 10, [action: "ADD"])
        Map lineItem2 = OrderSpec.getOrderLineItemJson(prepaidDrawdownPlan.id, prepaidDrawdownPlan.charges.get(1).id, targetDate, 10, [action: "ADD"])
        updateOrderJson.lineItems = [lineItem1, lineItem2]

        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)
        Map amendment = OrderSpec.getOrder(client, response.locationId)

        then:
        response.status == 201
        amendment.lineItems.size() == 2
    }

    def "new prepaid drawdown plan can be added to amendment with other existing prepaid drawdown plan"() {
        when:
        Map prepaidDrawdownPlan1 = PlanSpec.createPrepaidDrawdownPlan(client)
        Map order = OrderSpec.createOrderWithPlan(client, prepaidDrawdownPlan1)

        Map prepaidDrawdownPlan2 = PlanSpec.createPrepaidDrawdownPlan(client)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, targetDate)
        Map lineItem1 = OrderSpec.getOrderLineItemJson(prepaidDrawdownPlan2.id, prepaidDrawdownPlan2.charges.get(0).id, targetDate, 10, [action: "ADD"])
        Map lineItem2 = OrderSpec.getOrderLineItemJson(prepaidDrawdownPlan2.id, prepaidDrawdownPlan2.charges.get(1).id, targetDate, 10, [action: "ADD"])
        updateOrderJson.lineItems = [lineItem1, lineItem2]

        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)
        Map amendment = OrderSpec.getOrder(client, response.locationId)

        then:
        response.status == 201
        amendment.lineItems.size() == 2
    }

    def "adding another usage drawdown charge in amendment fails for existing drawdown plan"() {
        when:
        Map prepaidDrawdownPlan = PlanSpec.createPrepaidDrawdownPlan(client)
        Map order = OrderSpec.createOrderWithPlan(client, prepaidDrawdownPlan)

        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map updateOrderJson = getAmendmentJson(order, targetDate)
        Map lineItem1 = OrderSpec.getOrderLineItemJson(prepaidDrawdownPlan.id, prepaidDrawdownPlan.charges.get(0).id, targetDate, 10, [action: "ADD"])
        Map lineItem2 = OrderSpec.getOrderLineItemJson(prepaidDrawdownPlan.id, prepaidDrawdownPlan.charges.get(1).id, targetDate, 10, [action: "ADD"])
        updateOrderJson.lineItems = [lineItem1, lineItem2]

        HttpResponse response = client.post(ORDER_PATH, updateOrderJson)

        then:
        response.status == 400
        response.error.contains("cannot have multiple line items for drawdown charge")
    }

    def "update amendment populates expected fields"() {
        when:
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        Map orderJson = OrderSpec.generateOrderJson(client, plan)
        String orderId = client.post(ORDER_PATH, orderJson).locationId
        OrderSpec.executeDraftOrder(client, orderId)
        Map order = OrderSpec.getOrder(client, orderId)

        Map amendmentJson = OrderSpec.getOrder(client, orderId)
        amendmentJson.id = null
        amendmentJson.orderType = "AMENDMENT"
        amendmentJson.lineItems[0].effectiveDate = DEFAULT_START_DATE
        amendmentJson.lineItems[0].action = "UPDATE"
        amendmentJson.lineItems[0].quantity = 25

        amendmentJson.purchaseOrderNumber = "PO-22"
        amendmentJson.purchaseOrderRequiredForInvoicing = true
        // todo: add more fields
        HttpResponse amendmentResponse = client.post(ORDER_PATH, amendmentJson)
        Map amendment = OrderSpec.getOrder(client, amendmentResponse.locationId)

        Map updateAmendmentJson = OrderSpec.getOrder(client, amendment.id)
        updateAmendmentJson.purchaseOrderNumber = "PO-333"
        updateAmendmentJson.purchaseOrderRequiredForInvoicing = false
        // todo: add more fields
        HttpResponse updateAmendmentResponse = client.put(ORDER_PATH, updateAmendmentJson)
        Map updateAmendment = OrderSpec.getOrder(client, amendment.id)

        then:
        order.purchaseOrderNumber == null
        order.purchaseOrderRequiredForInvoicing == false

        amendmentResponse.status == 201
        amendment.purchaseOrderNumber == "PO-22"
        amendment.purchaseOrderRequiredForInvoicing == true

        updateAmendmentResponse.status == 200
        updateAmendment.purchaseOrderNumber == "PO-333"
        updateAmendment.purchaseOrderRequiredForInvoicing == false
    }

    def "cannot override list price of existing line in amendment"() {
        when:
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client, [:], [isListPriceEditable: true])
        Map orderJson = OrderSpec.generateOrderJson(client, plan)
        orderJson.lineItems[0].listPriceOverrideRatio = 2.0
        orderJson.lineItems[0].quantity = 10
        String orderId = client.post(ORDER_PATH, orderJson).locationId
        OrderSpec.executeDraftOrder(client, orderId)
        Map order = OrderSpec.getOrder(client, orderId)

        Map amendmentJson = OrderSpec.getOrder(client, orderId)
        amendmentJson.id = null
        amendmentJson.orderType = "AMENDMENT"
        amendmentJson.lineItems[0].effectiveDate = DEFAULT_START_DATE
        amendmentJson.lineItems[0].action = "UPDATE"
        amendmentJson.lineItems[0].quantity = 20
        amendmentJson.lineItems[0].listPriceOverrideRatio = 3.0

        HttpResponse amendmentResponse = client.post(ORDER_PATH, amendmentJson)

        then:
        order.lineItems[0].listUnitPrice == 21.0
        amendmentResponse.status == 400
        amendmentResponse.error.contains("cannot modify list price override ratio")
    }

    def "create amendment order with past expiry date and submit"() {
        when:
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)

        Map amendmentOrderJson = getAmendmentJson(order, lineItem, targetDate)
        amendmentOrderJson[EXPIRES_ON] = DateTimeHelper.plusMonths(Instant.now().epochSecond, -6, DateTimeHelper.DEFAULT_TZ)
        HttpResponse postAmendmentOrderJson = client.post(ORDER_PATH, amendmentOrderJson)
        HttpResponse getAmendmentOrderResponse = client.get(getOrderUrl(postAmendmentOrderJson.locationId))
        HttpResponse submitExpiredAmendmentOrderResponse = client.put("${ORDER_PATH}/${getAmendmentOrderResponse.body.id}/status/submitted", null)
        HttpResponse getExpiredAmendmentOrderResponse = client.get(getOrderUrl(postAmendmentOrderJson.locationId))

        then:
        postAmendmentOrderJson.status == 201
        getAmendmentOrderResponse.status == 200
        submitExpiredAmendmentOrderResponse.status == 409
        submitExpiredAmendmentOrderResponse.error.contains("expired")
        getExpiredAmendmentOrderResponse.body.status == "EXPIRED"
    }

    def "updating expired amendment order with a valid expiry date succeeds"() {
        when:
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)

        Map amendmentOrderJson = getAmendmentJson(order, lineItem, targetDate)
        amendmentOrderJson[EXPIRES_ON] = DateTimeHelper.plusMonths(Instant.now().epochSecond, -6, DateTimeHelper.DEFAULT_TZ)
        HttpResponse postAmendmentOrderJson = client.post(ORDER_PATH, amendmentOrderJson)
        HttpResponse getAmendmentOrderResponse = client.get(getOrderUrl(postAmendmentOrderJson.locationId))
        HttpResponse submitExpiredAmendmentOrderResponse = client.put("${ORDER_PATH}/${getAmendmentOrderResponse.body.id}/status/submitted", null)
        HttpResponse getExpiredAmendmentOrderResponse = client.get(getOrderUrl(postAmendmentOrderJson.locationId))

        // Update expiresOn date
        getExpiredAmendmentOrderResponse.body[EXPIRES_ON] = DateTimeHelper.plusMonths(Instant.now().epochSecond, 9, DateTimeHelper.DEFAULT_TZ)
        getExpiredAmendmentOrderResponse.body[END_DATE] = null
        HttpResponse putUpdatedOrderResponse = client.put("${ORDER_PATH}", getExpiredAmendmentOrderResponse.body)
        HttpResponse getUpdatedOrderResponse = client.get("${ORDER_PATH}/${postAmendmentOrderJson.locationId}")
        HttpResponse submitUpdatedOrderResponse = client.put("${ORDER_PATH}/${getUpdatedOrderResponse.body.id}/status/submitted", null)
        HttpResponse getSubmittedUpdatedOrderResponse = client.get("${ORDER_PATH}/${postAmendmentOrderJson.locationId}")

        then:
        submitExpiredAmendmentOrderResponse.status == 409
        submitExpiredAmendmentOrderResponse.error.contains("expired")
        getExpiredAmendmentOrderResponse.body.status == "EXPIRED"

        // Updated amendment order should be in DRAFT status. Approval should succeed.
        putUpdatedOrderResponse.status == 200
        getUpdatedOrderResponse.status == 200
        getUpdatedOrderResponse.body.status == "DRAFT"
        submitUpdatedOrderResponse.status == 200
        getSubmittedUpdatedOrderResponse.body.status == "APPROVED"
    }

    def "amendment with removal of plan having both charges - RECURRING and USAGE"() {
        when:
        Map plan = PlanSpec.createPlanWithMinimumCommit(client, [amount: 150, isListPriceEditable: true], [amount: 2])
        String  minimumCommitChargeId = plan.charges.find { it.type == "RECURRING" }.id
        String usageChargeId = plan.charges.find { it.type == "USAGE" }.id
        long startDate = DateTimeHelper.parseToInstant("2023-01-01T00:00:00")
        Map order = OrderSpec.executeOrderForPlan(client, [billingCycle: Recurrence.ONE_MONTH_CYCLE, startDate: startDate, termLength: Recurrence.THREE_YEAR_CYCLE], plan)
        String subscriptionId = order.subscriptionId
        long subscriptionStartDate = order.startDate as Long
        Long amendmentDate = DateTimeHelper.plusMonths(subscriptionStartDate, 12)
        Map amendmentJson = getAmendmentJson(order, amendmentDate)
        Map minCommitLine = order.lineItems.find { it.chargeId == minimumCommitChargeId } as Map
        minCommitLine.action = 'REMOVE'
        minCommitLine.effectiveDate = amendmentDate
        minCommitLine.quantity = 0
        amendmentJson.lineItems.add(minCommitLine)

        Map usageLine = order.lineItems.find { it.chargeId == usageChargeId } as Map
        usageLine.action = 'REMOVE'
        usageLine.effectiveDate = amendmentDate
        amendmentJson.lineItems.add(usageLine)

        HttpResponse amendmentResponse = client.post(ORDER_PATH, amendmentJson)
        HttpResponse getAmendmentOrderResponse = client.get(getOrderUrl(amendmentResponse.locationId))
        Map amendment = getAmendmentOrderResponse.body
        Map subscription = SubscriptionSpec.getSubscription(client, subscriptionId)

        then:
        amendment.lineItems.size() == 2
        subscription.charges.size() == 2
        amendment.lineItems[0].effectiveDate == amendmentDate
        amendment.lineItems[1].effectiveDate == amendmentDate
        amendment.lineItems[0].endDate == order.endDate
        amendment.lineItems[1].endDate == order.endDate
    }

    def "amendment with removal of plan having a RECURRING charge with 0 quantity"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        String  recurringChargeId = plan.charges.find { it.type == "RECURRING" }.id
        long startDate = DateTimeHelper.parseToInstant("2023-01-01T00:00:00")
        Map order = OrderSpec.executeOrderForPlan(client, [billingCycle: Recurrence.ONE_MONTH_CYCLE, startDate: startDate, termLength: Recurrence.THREE_YEAR_CYCLE], plan, 0)
        String subscriptionId = order.subscriptionId
        long subscriptionStartDate = order.startDate as Long
        Long amendmentDate = DateTimeHelper.plusMonths(subscriptionStartDate, 11)
        Map amendmentJson = getAmendmentJson(order, amendmentDate)
        Map recurringLine = order.lineItems.find { it.chargeId == recurringChargeId } as Map
        recurringLine.action = 'REMOVE'
        recurringLine.effectiveDate = amendmentDate
        amendmentJson.lineItems.add(recurringLine)

        HttpResponse amendmentResponse = client.post(ORDER_PATH, amendmentJson)
        HttpResponse getAmendmentOrderResponse = client.get(getOrderUrl(amendmentResponse.locationId))
        Map amendment = getAmendmentOrderResponse.body
        Map subscription = SubscriptionSpec.getSubscription(client, subscriptionId)

        then:
        amendment.lineItems.size() == 1
        subscription.charges.size() == 1
        amendment.lineItems[0].effectiveDate == amendmentDate
        amendment.lineItems[0].endDate == order.endDate
    }

    def "validate populateMissingLineItems is populating all the lines from the  during amendment"() {
        when:
        def createOrderResponse = OrderSpec.createDraftOrder(client)
        String orderId = createOrderResponse.locationId
        OrderSpec.executeDraftOrder(client, orderId)
        Map newOrder = client.get(getOrderUrl(orderId)).body

        Map lineItem = newOrder.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentJson = getAmendmentJson(newOrder, lineItem, targetDate)
        HttpResponse response = client.post(ORDER_PATH + "?populateMissingLines=true", amendmentJson)

        Map updatedOrder = client.get(getOrderUrl(response.locationId)).body

        then:
        createOrderResponse.status == 201
        newOrder != null
        newOrder.status == "EXECUTED"
        newOrder.lineItems.size() == 3
        newOrder.lineItemsNetEffect.size() == 3

        updatedOrder != null
        updatedOrder.lineItems.size() == 3
        updatedOrder.lineItemsNetEffect.size() == 2
    }

    def "cannot change payment term during amendment"() {
        when:
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentOrderJson = getAmendmentJson(order, lineItem, targetDate)

        // payment term change not allowed when creating amendment order
        amendmentOrderJson.paymentTerm = "NET45"
        HttpResponse amendmentOrderBadRequestResponse = client.post(ORDER_PATH, amendmentOrderJson)

        // change payment term back to default and create amendment order
        amendmentOrderJson.paymentTerm = DEFAULT_PAYMENT_TERM
        HttpResponse amendmentOrderResponse = client.post(ORDER_PATH, amendmentOrderJson)
        Map amendmentOrder = client.get(getOrderUrl(amendmentOrderResponse.locationId)).body

        // update payment term for existing amendment order - not allowed again
        Map updateAmendPayload = client.get(getOrderUrl(amendmentOrderResponse.locationId)).body
        updateAmendPayload.paymentTerm = "NET45"
        HttpResponse amendmentOrderUpdateResponse = client.put(ORDER_PATH, updateAmendPayload)

        then:
        order.paymentTerm == DEFAULT_PAYMENT_TERM
        amendmentOrderBadRequestResponse.status == 400
        amendmentOrderBadRequestResponse.error.contains("Payment term cannot be changed during amendment")

        amendmentOrderResponse.status == 201
        amendmentOrder.paymentTerm == DEFAULT_PAYMENT_TERM

        amendmentOrderUpdateResponse.status == 400
        amendmentOrderUpdateResponse.error.contains("Payment term cannot be changed during amendment")
    }

    def "billTo and shipTo should be changed as per user input while creating/updating amendment order"() {
        when:
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Map lineItem = order.lineItems.get(0)
        Long targetDate = DateTimeHelper.plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentOrderJson = getAmendmentJson(order, lineItem, targetDate)

        // create amendment order with empty billTo and shipTo
        amendmentOrderJson.billingContactId = null
        amendmentOrderJson.shippingContactId = null
        HttpResponse amendmentOrderWithEmptyContactIdsResponse = client.post(ORDER_PATH, amendmentOrderJson)
        Map amendmentOrderWithEmptyContactIds = client.get(getOrderUrl(amendmentOrderWithEmptyContactIdsResponse.locationId)).body

        // update amendment order with valid billTo and shipTo
        Map updateAmendPayload = client.get(getOrderUrl(amendmentOrderWithEmptyContactIdsResponse.locationId)).body
        updateAmendPayload.billingContactId = order.billingContactId as String
        updateAmendPayload.shippingContactId = order.shippingContactId as String
        HttpResponse amendmentOrderUpdateResponse = client.put(ORDER_PATH, updateAmendPayload)
        Map amendmentOrderWithContactIds = client.get(getOrderUrl(amendmentOrderWithEmptyContactIds.id)).body

        then:
        order.paymentTerm == DEFAULT_PAYMENT_TERM
        amendmentOrderWithEmptyContactIdsResponse.status == 201
        amendmentOrderWithEmptyContactIds.id
        amendmentOrderWithEmptyContactIds.billingContactId == null
        amendmentOrderWithEmptyContactIds.billingContactId == null
        amendmentOrderUpdateResponse.status == 200
        amendmentOrderWithContactIds.id == amendmentOrderWithEmptyContactIds.id
        amendmentOrderWithContactIds.billingContactId == order.billingContactId
        amendmentOrderWithContactIds.shippingContactId == order.shippingContactId
    }

    static Map getAmendmentJson(Map order, Long effectiveDate) {
        return getOrderRequestJson(order.accountId as String, order.shippingContactId as String, "AMENDMENT", Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, order.subscriptionId as String, effectiveDate)
    }

    static Map getAmendmentJson(Map order, Map lineItem, Long effectiveDate, List<Map> discounts = null, int quantity = 15, BigDecimal listUnitPrice = null) {
        Map orderJson = getOrderRequestJson(order.accountId as String, order.shippingContactId as String, "AMENDMENT", Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, order.subscriptionId as String, effectiveDate)
        orderJson.autoRenew = order.autoRenew
        Map orderLineItemJson = getOrderLineItemJson(lineItem, effectiveDate, [discounts: discounts, quantity: quantity, listUnitPrice: listUnitPrice])
        orderJson.lineItems = [
            orderLineItemJson
        ]

        return orderJson
    }

    static Map getAmendmentJsonNew(Map order, Map lineItem, Long effectiveDate, Map lineItemOverrides = [:]) {
        Map orderJson = getOrderRequestJson(order.accountId as String, order.shippingContactId as String, "AMENDMENT", Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, order.subscriptionId as String, effectiveDate)
        orderJson.autoRenew = order.autoRenew
        Map orderLineItemJson = getOrderLineItemJson(lineItem, effectiveDate, lineItemOverrides)
        orderJson.lineItems = [
            orderLineItemJson
        ]

        return orderJson
    }

    static HttpResponse createAmendmentOrder(HttpClient client, String orderId, Long targetDate, List<Map> discounts = null) {
        Map originalOrder = client.get(getOrderUrl(orderId)).body
        Map updateOrderJson = getAmendmentJson(originalOrder, originalOrder.lineItems.get(0), targetDate, discounts)
        return client.post(ORDER_PATH, updateOrderJson)
    }

    static Map postAddQuantityOrder(HttpClient client, String orderId, Long targetDate, List<Map> discounts = null) {
        HttpResponse postOrderResponse = createAmendmentOrder(client, orderId, targetDate, discounts)
        Map updatedOrder = client.get(getOrderUrl(postOrderResponse.locationId)).body
        OrderSpec.executeDraftOrder(client, updatedOrder.id as String)
        return client.get(getOrderUrl(postOrderResponse.locationId)).body
    }

    static Map postAddQuantityOrder(HttpClient client, String orderId, String orderLineItemId, Long targetDate) {
        Map originalOrder = client.get(getOrderUrl(orderId)).body
        Map orderLineItem = originalOrder.lineItems.find { it -> it.id == orderLineItemId }
        Map updateOrderJson = getAmendmentJson(originalOrder, orderLineItem, targetDate, null)
        HttpResponse postOrderResponse = client.post(ORDER_PATH, updateOrderJson)

        Map updatedOrder = client.get(getOrderUrl(postOrderResponse.locationId)).body
        OrderSpec.executeDraftOrder(client, updatedOrder.id as String)
        return client.get(getOrderUrl(postOrderResponse.locationId)).body
    }

    static Map getOrderLineItemJson(Map lineItem, Long effectiveDate, Map overrides = [:]) {
        return [
            planId              : lineItem.planId,
            chargeId            : lineItem.chargeId,
            subscriptionChargeId: lineItem.subscriptionChargeId,
            action              : "UPDATE",
            quantity            : lineItem.quantity,
            effectiveDate       : effectiveDate,
            discounts           : null,
            listUnitPrice       : null,
        ] + overrides
    }
}
