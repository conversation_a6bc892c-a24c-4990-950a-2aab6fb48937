package com.subskribe.billy_e2e.order

import static com.subskribe.billy_e2e.DateTimeHelper.plusMonths

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.account.AccountSpec
import com.subskribe.billy_e2e.account.ContactSpec
import com.subskribe.billy_e2e.attachments.AttachmentsSpec
import com.subskribe.billy_e2e.discount.DiscountSpec
import com.subskribe.billy_e2e.graphql.InvoiceGqlSpec
import com.subskribe.billy_e2e.invoice.BaseInvoiceSpec
import com.subskribe.billy_e2e.order.amendment.AmendmentOrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanJsonBuilder
import com.subskribe.billy_e2e.productcatalog.PlanRelationshipSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.productcatalog.ProductCatalogTypes
import com.subskribe.billy_e2e.productcatalog.ProductSpec
import com.subskribe.billy_e2e.salesforce.SalesforceCompleteSpec
import com.subskribe.billy_e2e.settings.TenantSettingRestAndGqlSpec
import com.subskribe.billy_e2e.subscription.SubscriptionSpec
import com.subskribe.billy_e2e.template.PredefinedTermsSpec
import com.subskribe.billy_e2e.utils.Recurrence
import java.time.Instant
import java.util.logging.Logger

class OrderSpec extends BaseOrderSpec {

    private static final Logger logger = Logger.getLogger("OrderSpec")

    def setupSpec() {
        updateTenantConfig(client)
        SalesforceCompleteSpec.createSFDCIntegrationIfNeeded(client)
    }

    def "when order a new order is created with same external id then create fails"() {
        when:
        String externalId = StringUtils.getRandomString(10)
        def createOrderResponse1 = createDraftOrder(client, null, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, externalId)
        def createOrderResponse2 = createDraftOrder(client, null, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, externalId)

        then:
        createOrderResponse1.status == 201
        createOrderResponse2.status == 409
        createOrderResponse2.error.contains("another order exists with external id")
    }

    def "when a new order is created in dry run mode it returns 204 status"() {
        when:
        String externalId = StringUtils.getRandomString(10)
        def createOrderResponse = createDraftOrderInDryRun(client, null, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, externalId)

        then:
        createOrderResponse.status == 204
        createOrderResponse.body == null
        createOrderResponse.error == null
        createOrderResponse.location == null
    }

    def "Order should be able to save when billing and shipping contact ids are null for draft orders"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson.billingContactId = null
        orderJson.shippingContactId = null
        def postResponse = client.post(ORDER_PATH, orderJson)

        def getOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")

        then:
        postResponse.status == 201
        getOrderResponse.status == 200
        getOrderResponse.body.billingContactId == null
        getOrderResponse.body.shippingContactId == null
    }

    def "user provided order id in request should not be used"() {
        when:
        Map orderJson = generateOrderJson(client)
        String orderId = 'ORD-' + StringUtils.getRandomString(5)
        orderJson.id = orderId

        HttpResponse addOrderResponse = client.post(ORDER_PATH, orderJson)

        then:
        addOrderResponse.status == 400
    }

    def "when same usage charge is referenced more than once order creation fails"() {
        when:
        Map plan = PlanSpec.createPlanWithUsageCharge(client)
        // adding the charge again to create two same charges in order
        plan.charges.add(plan.charges[0])
        def response = createDraftOrder(client, plan)

        then:
        plan.charges.size() == 2
        response.status == 400
        response.error.contains("usage charge with id CHRG-")
        response.error.contains("occurs more than once in the order")
    }

    def "Order name greater than 255 chars should return 400"() {
        when:
        Map orderJson = generateOrderJson(client)
        def stringArray = new String[256]
        Arrays.fill(stringArray, "A")
        orderJson.name = String.join("", stringArray)
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("field: name expected length between 1 and 255, found: 256")
    }

    def "Order name upto than 255 chars should return 201"() {
        when:
        Map orderJson = generateOrderJson(client)
        def stringArray = new String[255]
        Arrays.fill(stringArray, "A")
        orderJson.name = String.join("", stringArray)
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 201
    }

    def "Order name with an empty string should return 201"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson.name = ""
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 201
    }

    def "Create order and update by adding another new plan with a random orderLineId is successful"() {
        when:
        Map orderJson = generateOrderJson(client)
        def createOrderResponse = client.post(ORDER_PATH, orderJson)
        assert createOrderResponse.status == 201

        def getOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")
        assert getOrderResponse.status == 200

        Map order = getOrderResponse.body
        order.endDate = null
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        String randomId = UUID.randomUUID().toString()
        order.lineItems.add([
            id: randomId, // add some random id mimicking the behavior of dry run
            planId: plan.id,
            chargeId: plan.charges[0].id,
            quantity: 10,
            isDryRunItem: true
        ])
        HttpResponse updateDryRunOrderResponse = client.put("${ORDER_PATH}?dryRun=true", order)
        assert updateDryRunOrderResponse.status == 200

        HttpResponse updateOrderResponse = client.put("${ORDER_PATH}", order)
        assert updateOrderResponse.status == 200

        def getUpdatedOrderResponse = client.get("${ORDER_PATH}/${order.id}")
        assert getUpdatedOrderResponse.status == 200
        Map updatedOrder = getUpdatedOrderResponse.body

        then:
        updatedOrder != null
        updatedOrder.id == order.id
        updatedOrder.status == "DRAFT"
        updatedOrder.lineItems.size() == 4
        updatedOrder.lineItems[3].id != randomId
        updatedOrder.lineItems[3].planId == plan.id
        updatedOrder.lineItems[3].chargeId == plan.charges[0].id
        updatedOrder.lineItems[3].quantity == 10
        updatedOrder.lineItems[3].isDryRunItem == false
    }

    def "Create order with custom endDate and no termLength returns 201"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson.termLength = null
        Long customEndDate = plusMonths(DEFAULT_START_DATE, 18, DateTimeHelper.DEFAULT_TZ)
        orderJson.endDate = customEndDate
        def postResponse = client.post(ORDER_PATH, orderJson)

        def getOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")

        then:
        postResponse.status == 201
        getOrderResponse.status == 200
        getOrderResponse.body.termLength == null
        getOrderResponse.body.endDate == customEndDate
    }

    def "Updating a custom order end date is successful"() {
        when:
        Map orderJson = generateOrderJson(client)
        HttpResponse postResponse = client.post(ORDER_PATH, orderJson)

        HttpResponse getOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")
        Map storedOrder = getOrderResponse.body

        storedOrder.termLength = null
        Long customEndDate = plusMonths(DEFAULT_START_DATE, 12, DateTimeHelper.DEFAULT_TZ)
        storedOrder.endDate = customEndDate
        HttpResponse putResponse = client.put(ORDER_PATH, storedOrder)

        HttpResponse updatedOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")
        Map updatedOrder = updatedOrderResponse.body

        then:
        postResponse.status == 201
        getOrderResponse.status == 200
        putResponse.status == 200

        updatedOrder != null
        updatedOrder.termLength == null
        updatedOrder.endDate == customEndDate
    }

    def "Create order with both custom endDate and termLength which are logically unequal fails"() {
        when:
        Map orderJson = generateOrderJson(client, null, Recurrence.ONE_YEAR_CYCLE)
        orderJson.endDate = plusMonths(DEFAULT_START_DATE, 18, DateTimeHelper.DEFAULT_TZ)
        Long correctEndDateForTermLength = plusMonths(DEFAULT_START_DATE, 12, DateTimeHelper.DEFAULT_TZ)
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains(String.format("termLength %s end date %s does not match provided endDate %s", orderJson.termLength, correctEndDateForTermLength ,orderJson.endDate))
    }

    def "Create order with both custom endDate and termLength which are logically equal succeeds"() {
        when:
        Map orderJson = generateOrderJson(client, null, Recurrence.ONE_YEAR_CYCLE)
        orderJson.endDate = plusMonths(DEFAULT_START_DATE, 12, DateTimeHelper.DEFAULT_TZ)
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 201
    }

    def "Create order followed by get and put succeeds"() {
        when:
        Map orderJson = generateOrderJson(client, null, Recurrence.ONE_YEAR_CYCLE )
        orderJson.endDate = plusMonths(DEFAULT_START_DATE, 12, DateTimeHelper.DEFAULT_TZ)
        def postResponse = client.post(ORDER_PATH, orderJson)

        def getOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")

        def putOrderResponse = client.put(ORDER_PATH, getOrderResponse.body)

        then:
        putOrderResponse.status == 200
    }

    def "Create order without custom endDate and termLength returns 400"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson.endDate = null
        orderJson.termLength = null
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("At least one of termLength or endDate is expected")
    }

    def "when same recurring charge is referenced more than once order creation succeeds"() {
        when:
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        // adding the charge again to create two same charges in order
        plan.charges.add(plan.charges[0])
        def response = createDraftOrder(client, plan)
        then:
        plan.charges.size() == 2
        response.status == 201
    }

    def "when order is updated with same external Id then update order fails"() {
        when:
        String externalId = StringUtils.getRandomString(10)
        def createOrderResponse1 = createDraftOrder(client, null, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, externalId)
        def createOrderResponse2 = createDraftOrder(client)
        def getOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse2.locationId}")
        getOrderResponse.body[EXTERNAL_ID] = externalId
        getOrderResponse.body[END_DATE] = null
        def updateOrderResponse = client.put("${ORDER_PATH}", getOrderResponse.body)

        then:
        createOrderResponse1.status == 201
        createOrderResponse2.status == 201
        updateOrderResponse.status == 409
        updateOrderResponse.error.contains("another order exists with external id")
    }

    def "create new order"() {
        when:
        def createOrderResponse = createDraftOrder(client)
        def getOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")
        def submitOrderResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/submitted", null)
        def executedOrderResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/executed", null)

        then:
        getOrderResponse.status == 200
        getOrderResponse.body.sfdcOpportunityId.contains("TEST_OP_ID")
        getOrderResponse.body.isPrimaryOrderForSfdcOpportunity
        getOrderResponse.body.sfdcOpportunityName == "TEST_OP_NAME"
        getOrderResponse.body.sfdcOpportunityType == "TEST_OP_TYPE"
        getOrderResponse.body.sfdcOpportunityStage == "TEST_OP_STAGE"
        getOrderResponse.body.ownerId == "USER-ADMIN"
        getOrderResponse.body.updatedOn > 0
        submitOrderResponse.status == 200
        executedOrderResponse.status == 200
    }

    def "create new order without sfdc opportunity name"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson.sfdcOpportunityName = null
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("sfdcOpportunityName")
    }

    def "create new order with deprecated predefined terms"() {
        when:
        Map orderJson = generateOrderJson(client)
        String templateId = orderJson.orderFormTemplateIds.get(0)
        PredefinedTermsSpec.updatePredefinedTermsStatus(client, templateId, "active")
        PredefinedTermsSpec.updatePredefinedTermsStatus(client, templateId, "deprecated")
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("Predefined terms")
    }

    def "create new order with an attachment of non-PDF filetype"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        def fileName = "accountDocument.doc"
        def attachmentId = AttachmentsSpec.uploadAttachment(multiPartClient, account.id as String, fileName, new byte[1])
        Map orderJson = generateOrderJson(client)
        orderJson.attachmentId = attachmentId
        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("The Account Document must be a PDF.")
    }

    def "create draft order and execute it"() {
        when:
        def createOrderResponse = createDraftOrder(client)
        def getOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")
        def executeOrderResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/execute?executedOn=**********", null)
        def executedOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")

        then:
        getOrderResponse.status == 200
        getOrderResponse.body.status == "DRAFT"
        executeOrderResponse.status == 200
        executedOrderResponse.status == 200
        executedOrderResponse.body.status == "EXECUTED"
        executedOrderResponse.body.updatedOn > 0
        executedOrderResponse.body.executedOn == **********
    }

    def "auto approved order can be executed"() {
        when:
        def createOrderResponse = createDraftOrder(client)
        def getOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")
        client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/submitted", null)
        def executeOrderResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/execute", null)
        def executedOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")
        then:
        executeOrderResponse.status == 200
        executedOrderResponse.status == 200
        executedOrderResponse.body.status == "EXECUTED"
    }

    def "executed order can be executed no-op"() {
        when:
        def createOrderResponse = createDraftOrder(client)
        def getOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")
        client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/submitted", null)
        client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/approved", null)
        def executeStatusResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/executed", null)
        def executeOrderResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/execute", null)
        def executedOrderResponse = client.get("${ORDER_PATH}/${createOrderResponse.locationId}")
        then:
        executeStatusResponse.status == 200
        executeOrderResponse.status == 200
        executedOrderResponse.status == 200
        executedOrderResponse.body.status == "EXECUTED"
    }

    def "create order with percent discount"() {
        when:
        def order = createOrderWithPercentDiscount(client)

        then:
        order.subscriptionId != null
        order.lineItems != null
        order.lineItems[0].discounts != null
        order.totalAmount == new BigDecimal("252.00")
    }

    def "create order with fixed amount discount"() {
        when:
        def order = createOrderWithFixedAmountDiscount(client)

        then:
        order.subscriptionId != null
        order.lineItems != null
        order.lineItems[0].discounts != null
        order.totalAmount == new BigDecimal("170.00")
    }

    def "create new order with draft plan"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createAndGetDraftPlan(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)

        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
    }

    def "create new order with one time charge"() {
        when:
        Map order = createOrderWithOTCPlan(client)

        then:
        order.totalAmount == 175
    }

    def "create new order with recurring flat fee charge quantity > 1 returns 400"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringFlatFeeCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        orderJson.lineItems[0].quantity = 10

        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
    }

    def "create new order with recurring flat fee charge quantity = 0 creates quantity of 1"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringFlatFeeCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        orderJson.lineItems[0].quantity = 0

        def response = client.post(ORDER_PATH, orderJson)
        def getOrderResponse = client.get("${ORDER_PATH}/${response.locationId}")
        Map createdOrder = getOrderResponse.body

        then:
        response.status == 201
        createdOrder.status == "DRAFT"
        createdOrder.lineItems.size() == 1
        createdOrder.lineItems[0].quantity == 1
    }

    def "create new order with recurring flat fee charge quantity = null creates quantity of 1"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringFlatFeeCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        orderJson.lineItems[0].quantity = null

        def response = client.post(ORDER_PATH, orderJson)
        def getOrderResponse = client.get("${ORDER_PATH}/${response.locationId}")
        Map createdOrder = getOrderResponse.body

        then:
        response.status == 201
        createdOrder.status == "DRAFT"
        createdOrder.lineItems.size() == 1
        createdOrder.lineItems[0].quantity == 1
    }

    def "create new order with different currency plan than account return 400"() {
        when:
        String currency = TenantSettingRestAndGqlSpec.USED_CURRENCY
        Map plan = PlanSpec.createPlanWithRecurringCharge(client, [currency: currency])
        HttpResponse createOrderResponse = createDraftOrder(client, plan)
        HttpResponse deletePlanResponse = PlanSpec.deletePlan(client, plan.id)

        then:
        createOrderResponse.status == 400
        deletePlanResponse.status == 200
    }

    def "create new ramp order with one time charge"() {
        when:
        Map order = createRampOrderWithRecurringPlan(client)
        String subscriptionId = order.subscriptionId
        Map subscriptionMetrics = SubscriptionSpec.getSubscriptionMetrics(client, subscriptionId).body

        then:
        order.subscriptionId != null
        order.lineItems.size() == 3

        order.lineItems[0].subscriptionChargeGroupId == order.lineItems[1].subscriptionChargeGroupId
        order.lineItems[1].subscriptionChargeGroupId == order.lineItems[2].subscriptionChargeGroupId

        order.lineItems[0].chargeId == order.lineItems[1].chargeId
        order.lineItems[1].chargeId == order.lineItems[2].chargeId

        order.lineItems[0].planId == order.lineItems[1].planId
        order.lineItems[1].planId == order.lineItems[2].planId

        order.lineItems[0].subscriptionChargeId != order.lineItems[1].subscriptionChargeId
        order.lineItems[1].subscriptionChargeId != order.lineItems[2].subscriptionChargeId
        order.lineItems[0].subscriptionChargeId != order.lineItems[2].subscriptionChargeId

        order.lineItems[0].isRamp == true
        order.lineItems[1].isRamp == true
        order.lineItems[2].isRamp == true

        order.metrics.averageArr == new BigDecimal("105.00")
        subscriptionMetrics.averageArr == new BigDecimal("105.00")
    }

    def "create ramp order with paid in full billing cycle"() {
        when:
        Map order = createRampOrderWithRecurringPlan(client, Recurrence.PAID_IN_FULL_CYCLE, false)

        then:
        order.lineItems.size() == 3
        order.lineItems.every { it -> it.amount == new BigDecimal("105.00") }
    }

    def "create order with predefined discount"() {
        when:
        Map discount = DiscountSpec.addAndGetDiscount(client, 0.25)
        Map order = createOrderWithTenantDiscounts(client, Recurrence.THREE_YEAR_CYCLE, [discount])
        String subscriptionId = order.subscriptionId
        Map subscription = SubscriptionSpec.getSubscription(client, subscriptionId)

        then:
        order.predefinedDiscounts != null
        order.predefinedDiscounts.size() == 1
        order.predefinedDiscounts[0].id == discount.id
        order.predefinedDiscounts[0].percent == discount.percent

        // 10.5 / year * 10 units * 3 years with 25% discount
        order.totalAmount == new BigDecimal("236.25")

        order.lineItems.every {
            it.predefinedDiscounts != null &&
                    it.predefinedDiscounts.size() == 1 &&
                    it.predefinedDiscounts[0].id == discount.id &&
                    it.predefinedDiscounts[0].percent == discount.percent &&
                    it.discountAmount == 78.75
        }

        subscriptionId != null
        subscription.predefinedDiscounts != null
        subscription.charges.every {
            it.predefinedDiscounts != null &&
                    it.predefinedDiscounts.size() == 1 &&
                    it.predefinedDiscounts[0].id == discount.id &&
                    it.predefinedDiscounts[0].percent == discount.percent
        }
    }

    def "create order with predefined discount ramped"() {
        when:
        def plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map orderJson = generateOrderJson(client, plan, Recurrence.getRecurrenceCycle("MONTH", 18))
        List rampOverrides = [
            [effectiveDate: DEFAULT_START_DATE, quantity: 1, discountPercent: "0.25"],
            [effectiveDate: plusMonths(DEFAULT_START_DATE, 6), quantity: 1, discountPercent: "0.25"],
        ]
        orderJson.lineItems = getRampOrderLineItemJson(plan.id as String, plan.charges[0].id as String, rampOverrides)
        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = getOrder(client, response.locationId)

        then:
        response.status == 201
        order.totalListAmount == new BigDecimal("15.75")

        // note: 15.75 * 0.75 = 11.81. However the amount is composed of 10.5 x 0.75 + 5.25 x 0.75 = 7.88 (rounded) + 3.94 (rounded) = 11.82
        order.totalAmount == new BigDecimal("11.82")
        order.lineItems.every { it -> {
                // line discount amount should match individual discount object's amount
                it.discountAmount == it.discounts.get(0).amount
            }}
    }

    def "create order with deprecated discount returns error"() {
        when:
        Map discount = DiscountSpec.addAndGetDiscount(client, 0.25)
        DiscountSpec.updateDiscountStatus(client, discount.id, "deprecated")
        HttpResponse getDiscountByIdResponse = client.get(DiscountSpec.getDiscountPath(discount.id))
        discount = getDiscountByIdResponse.body
        HttpResponse response = createDraftOrder(client, null, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, null, [discount])

        then:
        response.status == 400
    }

    def "create new order with excessive total"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        orderJson.lineItems[0].quantity = ***********

        def response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.toString().contains("cannot exceed")
    }

    def "create one time flat fee custom pricing order"() {
        when:
        Map plan = PlanSpec.createPlanWithOneTimeFlatFeeCustomCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].listUnitPrice = 101.00
        orderJson.lineItems[0].quantity = null

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        order.lineItems[0].listUnitPrice == 101.00
        order.lineItems[0].sellUnitPrice == 101.00
        order.lineItems[0].amount == 101.00
    }

    def "create one time flat fee custom pricing order with discount"() {
        when:
        DiscountSpec.addAndGetDiscount(client, 0.25)
        Map plan = PlanSpec.createPlanWithOneTimeFlatFeeCustomCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].listUnitPrice = 150.00
        orderJson.lineItems[0].quantity = null
        orderJson.lineItems[0].discounts = [
            [
                name   : "default",
                percent: 0.5
            ]
        ]

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        order.lineItems[0].listUnitPrice == 150.00
        order.lineItems[0].sellUnitPrice == 75.00
        order.lineItems[0].amount == 75.00
        order.lineItems[0].discountAmount == 75.00
    }

    def "create one time per unit custom pricing order"() {
        when:
        Map plan = PlanSpec.createPlanWithOneTimePerUnitCustomCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].listUnitPrice = 101.00
        orderJson.lineItems[0].quantity = 100

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        order.lineItems[0].listUnitPrice == 101.00
        order.lineItems[0].sellUnitPrice == 101.00
        order.lineItems[0].amount == 10100.00
        order.lineItems[0].discountAmount == 0
    }

    def "create recurring per unit custom pricing order"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringPerUnitCustomCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].listUnitPrice = 101.00
        orderJson.lineItems[0].quantity = 100
        orderJson.lineItems[0].effectiveDate = DEFAULT_START_DATE
        orderJson.termLength = Recurrence.ONE_YEAR_CYCLE
        orderJson.billingCycle = Recurrence.ONE_YEAR_CYCLE
        orderJson.startDate = DEFAULT_START_DATE

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        order.lineItems[0].listUnitPrice == 101.00
        order.lineItems[0].sellUnitPrice == 101.00
        order.lineItems[0].amount == 10100.00
        order.lineItems[0].discountAmount == 0
    }

    def "create custom pricing order with negative price"() {
        when:
        Map plan = PlanSpec.createPlanWithOneTimePerUnitCustomCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].listUnitPrice = -120.00

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        order.lineItems[0].listUnitPrice == -120.00
        order.lineItems[0].sellUnitPrice == -120.00
        order.lineItems[0].amount == -1200.00
        order.lineItems[0].discountAmount == 0
    }

    def "create custom pricing order with no custom price"() {
        when:
        Map plan = PlanSpec.createPlanWithOneTimePerUnitCustomCharge(client)
        Map orderJson = generateOrderJson(client, plan)

        HttpResponse response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.toString().contains("must provide custom listUnitPrice")
    }

    def "modification of draft order with invalid line item effective date fails correctly"() {
        when:
        Map plan = PlanSpec.createPlanWithBlockPriceCharge(client)
        HttpResponse response = createDraftOrder(client, plan)

        Map createdOrder = client.get(getOrderUrl(response.locationId)).body
        // push the order line item effective date outside of the range of the order date
        createdOrder.lineItems.get(0).effectiveDate = createdOrder.lineItems.get(0).effectiveDate - 500000
        createdOrder[END_DATE] = null
        HttpResponse updateResponse = client.put(ORDER_PATH, createdOrder)
        then:
        response.status == 201
        updateResponse.status == 400
        updateResponse.error.matches(~".*lineItem effective date.*should be between the order start date.*and order end date.*")
    }

    def "legacy or new deprecated plans cannot be added or updated on orders."() {
        when:
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        HttpResponse response = client.post(ORDER_PATH, orderJson)
        orderJson = getOrder(client, response.locationId)

        // update order with legacy deprecated plan is allowed
        HttpResponse deprecateResponse = PlanSpec.deprecatePlan(client, plan.id as String)
        orderJson[END_DATE] = null
        HttpResponse updateOrderResponse = client.put(ORDER_PATH, orderJson)

        // update order with new deprecated plan is not allowed
        Map plan2 = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        HttpResponse deprecateResponse2 = PlanSpec.deprecatePlan(client, plan2.id as String)
        orderJson = addPlanToOrder(orderJson, plan2)
        orderJson[END_DATE] = null
        HttpResponse updateOrderResponse2 = client.put(ORDER_PATH, orderJson)

        then:
        response.status == 201
        deprecateResponse.status == 200
        updateOrderResponse.status == 400
        updateOrderResponse.error.contains("deprecated and cannot be added to order")
        deprecateResponse2.status == 200
        updateOrderResponse2.status == 400
        updateOrderResponse2.error.contains("deprecated and cannot be added to order")
    }

    def "create new order with line item quantity too little"() {
        when:
        Map plan = PlanSpec.createPlanWithOneTimeCharge(client, [:], [minQuantity: 5L])
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].quantity = 2L

        HttpResponse response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("cannot be less than minimum quantity")
    }

    def "create new order with line item quantity too large"() {
        when:
        Map plan = PlanSpec.createPlanWithOneTimeCharge(client, [:], [maxQuantity: 50L])
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].quantity = 200L

        HttpResponse response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("cannot be greater than maximum quantity")
    }

    def "create new order with disallowed line item action"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson.lineItems[0].action = "UPDATE"

        HttpResponse response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("Invalid action type")
    }

    def "create subscription with multiple instances of a charge"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        Map lineItem = orderJson.lineItems.get(0).clone()
        lineItem.quantity = 25
        orderJson.lineItems.add(lineItem)

        String orderId = client.post(ORDER_PATH, orderJson).locationId
        executeDraftOrder(client, orderId)
        Map order = getOrder(client, orderId)

        then:
        Map firstItem = order.lineItems.get(0)
        Map secondItem = order.lineItems.get(1)
        firstItem.subscriptionChargeGroupId != null
        secondItem.subscriptionChargeGroupId != null
        firstItem.subscriptionChargeGroupId != secondItem.subscriptionChargeGroupId
        firstItem.subscriptionChargeId != null
        secondItem.subscriptionChargeId != null
        firstItem.subscriptionChargeId != secondItem.subscriptionChargeId
        firstItem.quantity == 10
        secondItem.quantity == 25
    }

    def "create order line items with end dates"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        Map lineItem = orderJson.lineItems.get(0).clone()
        lineItem.quantity = 20
        long endDate = plusMonths(lineItem.effectiveDate as long, 6, DateTimeHelper.DEFAULT_TZ)
        lineItem.endDate = endDate
        orderJson.lineItems.add(lineItem)

        String orderId = client.post(ORDER_PATH, orderJson).locationId
        executeDraftOrder(client, orderId)
        Map order = getOrder(client, orderId)

        then:
        Map firstItem = order.lineItems.get(0)
        firstItem.quantity == 10
        Map secondItem = order.lineItems.get(1)
        secondItem.quantity == 20
        secondItem.effectiveDate == firstItem.effectiveDate
        secondItem.endDate == endDate
        secondItem.endDate != order.endDate
    }

    def "create order line items with end dates order dates"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        Map lineItem = orderJson.lineItems.get(0).clone()
        lineItem.quantity = 20
        lineItem.endDate = plusMonths(orderJson.startDate, -6, DateTimeHelper.DEFAULT_TZ)
        orderJson.lineItems.add(lineItem)

        HttpResponse beforeStartResponse = client.post(ORDER_PATH, orderJson)

        lineItem.endDate = plusMonths(orderJson.startDate, 18, DateTimeHelper.DEFAULT_TZ)
        HttpResponse afterEndResponse = client.post(ORDER_PATH, orderJson)

        then:
        beforeStartResponse.status == 400
        beforeStartResponse.error.contains("end date")
        afterEndResponse.status == 400
        afterEndResponse.error.contains("end date")
    }

    def "create order with percent of charge with different derived from source than the tenant"() {
        when:
        // create plan with all charges
        Map targetPlan = PlanSpec.createPlanWithRecurringCharge(client)

        Map sellPercentOfPlan = PlanSpec.createPlanWithPercentOfCharge(client, [:], [percentDerivedFrom: "SELL_AMOUNT"])
        PlanRelationshipSpec.createPercentOfRelationship(client, sellPercentOfPlan, targetPlan)

        Map listPercentOfPlan = PlanSpec.createPlanWithPercentOfCharge(client, [:], [percentDerivedFrom: "LIST_AMOUNT", chargeModel: ProductCatalogTypes.PER_UNIT])
        PlanRelationshipSpec.createPercentOfRelationship(client, listPercentOfPlan, targetPlan)

        Map orderJson = generateOrderJson(client, targetPlan)
        orderJson.lineItems.get(0).discounts = [
            [name: "discount", percent: "0.5"]
        ]
        orderJson.lineItems.add(getOrderLineItemJson(sellPercentOfPlan.id, sellPercentOfPlan.charges.get(0).id, null, 1))
        orderJson.lineItems.add(getOrderLineItemJson(listPercentOfPlan.id, listPercentOfPlan.charges.get(0).id, null, 1))
        String orderId = client.post(ORDER_PATH, orderJson).locationId
        executeDraftOrder(client, orderId)

        Map order = getOrder(client, orderId)

        // Create an amendment for percent of charge against list amount
        Map amendedPercentOfChargeLineItem = order.lineItems.find { it.planId == listPercentOfPlan.id }
        Long targetDate = plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        Map amendmentJson = AmendmentOrderSpec.getAmendmentJson(order, amendedPercentOfChargeLineItem, targetDate, null, 3, null)
        HttpResponse amendmentResponse = client.post(ORDER_PATH, amendmentJson)
        Map amendmentOrder = client.get(getOrderUrl(amendmentResponse.locationId)).body

        then:
        Map recurringLine = order.lineItems.find { it.planId == targetPlan.id }
        Map sellPercentOfLine = order.lineItems.find { it.planId == sellPercentOfPlan.id }
        Map listPercentOfLine = order.lineItems.find { it.planId == listPercentOfPlan.id }

        recurringLine.listAmount == 105.0
        recurringLine.amount == 52.5

        // 10% of the sell amount of the recurring line
        sellPercentOfLine.listAmount == 5.25
        sellPercentOfLine.amount == 5.25

        // 10% of list amount of recurring line
        listPercentOfLine.listAmount == 10.5
        listPercentOfLine.amount == 10.5

        // amendment order checks
        amendmentOrder.lineItems.size() == 1
        amendmentOrder.lineItemsNetEffect.size() == 2
        Map amendmentLineRemoved = amendmentOrder.lineItemsNetEffect.find { it.action == "UPDATE" && it.quantity == -1 }
        amendmentLineRemoved.amount == -10.5
        Map amendmentLineAdded = amendmentOrder.lineItemsNetEffect.find { it.action == "UPDATE" && it.quantity == 3 }
        amendmentLineAdded.amount == 31.5
        amendmentOrder.lineItems[0].amount == 21.00
        amendmentOrder.totalAmount == 21.00
        amendmentOrder.totalListAmount == 21.00
    }

    def "percent of charge with no target does not break other calculations"() {
        when:
        Map recurringPlan = PlanSpec.createPlanWithRecurringCharge(client)
        Map percentOfPlan = PlanSpec.createPlanWithPercentOfCharge(client)
        HttpResponse relationshipResponse = PlanRelationshipSpec.createPercentOfRelationship(client, percentOfPlan, recurringPlan)
        Map percentOfPLanWithNoTargets = PlanSpec.createPlanWithPercentOfCharge(client)

        Map order = createOrderForPlans(client, null, [
            recurringPlan,
            percentOfPlan,
            percentOfPLanWithNoTargets
        ])

        then:
        relationshipResponse.status == 201

        order.lineItems.size() == 3
        order.lineItems[0].planId == recurringPlan.id
        order.lineItems[0].amount == new BigDecimal("10.50")

        order.lineItems[1].planId == percentOfPlan.id
        order.lineItems[1].amount == new BigDecimal("1.05")

        order.lineItems[2].planId == percentOfPLanWithNoTargets.id
        order.lineItems[2].amount == new BigDecimal("0.00")
    }

    def "create order with plan that has multiple charges"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getRecurringPerUnitChargeJson(),
            PlanJsonBuilder.getVolumeChargeJson()
        ])
        String planId = PlanSpec.createPlan(client, planJson).locationId as String
        Map plan = PlanSpec.getPlan(client, planId)

        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        Map removedCharge = orderJson.lineItems.get(1) as Map
        orderJson.lineItems.remove(1)

        HttpResponse notAllChargesPresentResponse = client.post(ORDER_PATH, orderJson)

        orderJson.lineItems.add(removedCharge)
        HttpResponse allChargesPresentInPlanResponse = client.post(ORDER_PATH, orderJson)

        then:
        notAllChargesPresentResponse.status == 400
        notAllChargesPresentResponse.error.matches(".*charges .* missing in order.*")

        allChargesPresentInPlanResponse.status == 201
    }

    def "create order with one time charge with duration less than order end date"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getOneTimeChargeJson([durationInMonths: 6])
        ])
        String planId = PlanSpec.createPlan(client, planJson).locationId as String
        Map plan = PlanSpec.getPlan(client, planId)

        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        HttpResponse defaultEndDateResponse = client.post(ORDER_PATH, orderJson)
        Map defaultEndDateOrder = getOrder(client, defaultEndDateResponse.locationId)

        // set end date as duration date
        orderJson.lineItems.get(0).endDate = plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
        HttpResponse durationEndDateResponse = client.post(ORDER_PATH, orderJson)
        Map durationEndDateOrder = getOrder(client, durationEndDateResponse.locationId)

        // set incorrect end date
        orderJson.lineItems.get(0).endDate = plusMonths(DEFAULT_START_DATE, 8, DateTimeHelper.DEFAULT_TZ)
        HttpResponse invalidEndDateResponse = client.post(ORDER_PATH, orderJson)

        // set end date as order end date truncates to duration end date
        orderJson.lineItems.get(0).endDate = plusMonths(DEFAULT_START_DATE, 12, DateTimeHelper.DEFAULT_TZ)
        HttpResponse orderEndDateResponse = client.post(ORDER_PATH, orderJson)
        Map orderEndDateOrder = getOrder(client, orderEndDateResponse.locationId)

        then:
        defaultEndDateResponse.status == 201
        defaultEndDateOrder.lineItems.get(0).endDate == plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)

        durationEndDateResponse.status == 201
        durationEndDateOrder.lineItems.get(0).endDate == plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)

        invalidEndDateResponse.status == 400
        invalidEndDateResponse.error.contains("end date")

        orderEndDateResponse.status == 201
        orderEndDateOrder.lineItems.get(0).endDate == plusMonths(DEFAULT_START_DATE, 6, DateTimeHelper.DEFAULT_TZ)
    }

    def "create order with one time charge with duration greater than order end date"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getOneTimeChargeJson([durationInMonths: 24]),
            PlanJsonBuilder.getRecurringPerUnitChargeJson()
        ])
        String planId = PlanSpec.createPlan(client, planJson).locationId as String
        Map plan = PlanSpec.getPlan(client, planId)
        String oneTimeChargeId = plan.charges.find{ it.type == 'ONE_TIME' }.id

        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        HttpResponse defaultEndDateResponse = client.post(ORDER_PATH, orderJson)
        Map defaultEndDateOrder = getOrder(client, defaultEndDateResponse.locationId)

        // set end date as duration date
        orderJson.lineItems.find {it.chargeId == oneTimeChargeId }.endDate = plusMonths(DEFAULT_START_DATE, 24, DateTimeHelper.DEFAULT_TZ)
        HttpResponse durationEndDateResponse = client.post(ORDER_PATH, orderJson)

        // set incorrect end date that is neither the end of the order nor the duration of the charge
        orderJson.lineItems.find {it.chargeId == oneTimeChargeId }.endDate = plusMonths(DEFAULT_START_DATE, 10, DateTimeHelper.DEFAULT_TZ)
        HttpResponse invalidEndDateResponse = client.post(ORDER_PATH, orderJson)

        // set end date as the end of the order
        orderJson.lineItems.find {it.chargeId == oneTimeChargeId }.endDate = plusMonths(DEFAULT_START_DATE, 12, DateTimeHelper.DEFAULT_TZ)
        HttpResponse orderEndDateResponse = client.post(ORDER_PATH, orderJson)
        Map orderEndDateOrder = getOrder(client, orderEndDateResponse.locationId)

        then:
        defaultEndDateResponse.status == 201
        defaultEndDateOrder.lineItems.find {it.chargeId == oneTimeChargeId }.endDate == defaultEndDateOrder.endDate

        durationEndDateResponse.status == 400
        durationEndDateResponse.error.contains("end date")

        invalidEndDateResponse.status == 400
        invalidEndDateResponse.error.contains("end date")

        orderEndDateResponse.status == 201
        orderEndDateOrder.lineItems.find {it.chargeId == oneTimeChargeId }.endDate == orderEndDateOrder.endDate
    }

    def "create order with price override"() {
        when:
        Map oneTimePlan = PlanSpec.createPlanWithOneTimeCharge(client, [:], [amount: 20, isListPriceEditable: true])
        Map recurringPlan = PlanSpec.createPlanWithRecurringCharge(client, [:], [amount: 100, isListPriceEditable: true])
        Map recurringPlan2 = PlanSpec.createPlanWithRecurringCharge(client, [:], [amount: 13500, isListPriceEditable: true])
        Map orderJson = generateOrderJson(client, oneTimePlan)
        orderJson.lineItems[0].quantity = 1
        orderJson.lineItems[0].listPriceOverrideRatio = 1.5
        addPlanToOrder(orderJson, recurringPlan)
        orderJson.lineItems[1].quantity = 100
        orderJson.lineItems[1].listPriceOverrideRatio = 2
        orderJson.lineItems[1].discounts = [
            [
                name          : "default",
                percent       : 0.2,
            ]
        ]
        addPlanToOrder(orderJson, recurringPlan2)
        orderJson.lineItems[2].quantity = 10
        orderJson.lineItems[2].listPriceOverrideRatio = 1.1111111111

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = client.get(getOrderUrl(response.locationId)).body

        then:
        response.status == 201
        order.lineItems[0].listUnitPrice == 30.0
        order.lineItems[0].sellUnitPrice == 30.0
        order.lineItems[0].amount == 30.0
        order.lineItems[1].listUnitPrice == 200.0
        order.lineItems[1].sellUnitPrice == 160.0
        order.lineItems[1].amount == 16000
        order.lineItems[2].listUnitPrice == 15000.0
        order.lineItems[2].sellUnitPrice == 15000.0
        order.lineItems[2].amount == 150000.0
    }

    def "price override cannot be applied if charge does not allow it"() {
        when:
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].listPriceOverrideRatio = 2.0
        HttpResponse response = client.post(ORDER_PATH, orderJson)

        then:
        response.status == 400
        response.error.contains("cannot override list price")
    }

    def "create order with past expiry date and submit"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson[EXPIRES_ON] = plusMonths(Instant.now().epochSecond, -1, DateTimeHelper.DEFAULT_TZ)
        HttpResponse postResponse = client.post(ORDER_PATH, orderJson)
        HttpResponse getOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")
        HttpResponse submitExpiredOrderResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/submitted", null)
        HttpResponse getExpiredOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")

        then:
        postResponse.status == 201
        getOrderResponse.status == 200
        getOrderResponse.body.status == "DRAFT"

        // Submitting an expired draft order should fail and change the order status to EXPIRED
        submitExpiredOrderResponse.status == 409
        submitExpiredOrderResponse.error.contains("expired")
        getExpiredOrderResponse.body.status == "EXPIRED"
    }

    def "create order without contacts with past expiry date and let it expire"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson.billingContactId = null
        orderJson.shippingContactId = null
        orderJson[EXPIRES_ON] = plusMonths(Instant.now().epochSecond, -1, DateTimeHelper.DEFAULT_TZ)

        HttpResponse postResponse = client.post(ORDER_PATH, orderJson)
        HttpResponse getOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")
        HttpResponse submitExpiredOrderResponse = client.put("${ORDER_PATH}/${getOrderResponse.body.id}/status/submitted", null)
        HttpResponse getExpiredOrderResponse = client.get("${ORDER_PATH}/${postResponse.locationId}")

        then:
        postResponse.status == 201
        getOrderResponse.status == 200
        getOrderResponse.body.status == "DRAFT"

        // Submitting an expired draft order should fail and change the order status to EXPIRED
        submitExpiredOrderResponse.status == 409
        submitExpiredOrderResponse.error.contains("expired")
        getExpiredOrderResponse.body.status == "EXPIRED"
    }

    def "updated expired order can be submitted"() {
        when:
        Map orderJson = generateOrderJson(client)
        orderJson[EXPIRES_ON] = plusMonths(Instant.now().epochSecond, -1, DateTimeHelper.DEFAULT_TZ)
        HttpResponse postExpiredOrderResponse = client.post(ORDER_PATH, orderJson)
        String orderId = postExpiredOrderResponse.locationId
        HttpResponse getExpiredOrderResponse = client.get("${ORDER_PATH}/${orderId}")
        HttpResponse submitExpiredOrderResponse = client.put("${ORDER_PATH}/${getExpiredOrderResponse.body.id}/status/submitted", null)
        HttpResponse getSubmittedExpiredOrderResponse = client.get("${ORDER_PATH}/${orderId}")

        // Update expiresOn date
        getExpiredOrderResponse.body[EXPIRES_ON] = plusMonths(Instant.now().epochSecond, 1, DateTimeHelper.DEFAULT_TZ)
        getExpiredOrderResponse.body[END_DATE] = null
        HttpResponse putUpdatedOrderResponse = client.put("${ORDER_PATH}", getExpiredOrderResponse.body)
        HttpResponse getUpdatedOrderResponse = client.get("${ORDER_PATH}/${orderId}")
        HttpResponse submitUpdatedOrderResponse = client.put("${ORDER_PATH}/${getUpdatedOrderResponse.body.id}/status/submitted", null)
        HttpResponse getSubmittedUpdatedOrderResponse = client.get("${ORDER_PATH}/${orderId}")

        then:
        postExpiredOrderResponse.status == 201
        getExpiredOrderResponse.status == 200
        getExpiredOrderResponse.body.status == "DRAFT"
        submitExpiredOrderResponse.status == 409
        getSubmittedExpiredOrderResponse.body.status == "EXPIRED"

        // Order is put back in draft state after updating expiresOn date
        putUpdatedOrderResponse.status == 200
        getUpdatedOrderResponse.status == 200
        getUpdatedOrderResponse.body.status == "DRAFT"
        submitUpdatedOrderResponse.status == 200
        getSubmittedUpdatedOrderResponse.body.status == "APPROVED"
    }

    def "dry run order with no line items does not fail"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringFlatFeeCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan)
        orderJson.lineItems = null

        def dryRunResponse = client.post("${ORDER_PATH}?isDryRun=true", orderJson)
        def response = client.post("${ORDER_PATH}", orderJson)

        then:
        dryRunResponse.status == 204
        response.status == 400
    }

    def "Create order with empty cycle or term length"() {
        when:
        Map orderJson = generateOrderJson(client, null, [cycle: Recurrence.YEAR])
        orderJson.endDate = null
        def postResponse = client.post(ORDER_PATH, orderJson)

        then:
        postResponse.status == 400
        postResponse.error.contains("termLength cycle and step are required")

        when:
        orderJson = generateOrderJson(client, null, [step: 1])
        orderJson.endDate = null
        postResponse = client.post(ORDER_PATH, orderJson)

        then:
        postResponse.status == 400
        postResponse.error.contains("termLength cycle and step are required")
    }

    def "create order with termLength step 0"() {
        when:
        Map orderJson = generateOrderJson(client, null, [cycle: Recurrence.YEAR, step: 0])
        orderJson.endDate = null
        def postResponse = client.post(ORDER_PATH, orderJson)

        then:
        postResponse.status == 400
        postResponse.error.contains("termLength step must be greater than 0")
    }

    static Map getOrder(HttpClient client, String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null
        }
        HttpResponse response = client.get(getOrderUrl(orderId))

        if (response.error) {
            logger.severe("Received error response: " + response.error)
        }

        return response.body
    }

    static Map createAndGetOrder(HttpClient client, String accountId = null, boolean automaticPayment = false, String ruleId = "", String paymentTerm = DEFAULT_PAYMENT_TERM) {
        def createOrderResponse = createDraftOrder(client, null, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, null, null, accountId, automaticPayment, ruleId, null, paymentTerm)
        Map order = getOrder(client, createOrderResponse.locationId)

        executeDraftOrder(client, order.id)
        return getOrder(client, order.id)
    }

    static Map createAndGetOrder(HttpClient client, Map orderJson) {
        String orderId = client.post(ORDER_PATH, orderJson).locationId

        executeDraftOrder(client, orderId)

        return getOrder(client, orderId)
    }

    static Map createOrderWithPlan(HttpClient client, Map plan, Long startDate = DEFAULT_START_DATE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Map termLength = Recurrence.ONE_YEAR_CYCLE) {
        HttpResponse response = createDraftOrder(client, plan, termLength, billingCycle, startDate)
        Map order = client.get(getOrderUrl(response.locationId)).body

        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createOrderWithBlockPricePlan(HttpClient client) {
        Map plan = PlanSpec.createPlanWithBlockPriceCharge(client)
        createOrderWithPlan(client, plan)
    }

    static Map createOrderWithOTCPlan(HttpClient client, String recognitionRuleId = null) {
        Map chargeOverrides = [:]
        if (recognitionRuleId) {
            chargeOverrides = [recognitionRuleId: recognitionRuleId]
        }
        Map plan = PlanSpec.createPlanWithOneTimeCharge(client, [:], chargeOverrides)
        createOrderWithPlan(client, plan)
    }

    static Map createOrderWithPrepaidPlan(HttpClient client, Map chargeOverrides = [:]) {
        Map plan = PlanSpec.createPlanWithPrepaidCharge(client, chargeOverrides)
        createOrderWithPlan(client, plan)
    }

    static Map createOrderWithRecurringCustomCharge(HttpClient client) {
        Map plan = PlanSpec.createPlanWithRecurringPerUnitCustomCharge(client)
        Map orderJson = generateOrderJson(client, plan)
        orderJson.lineItems[0].listUnitPrice = 10.5
        orderJson.lineItems[0].quantity = 10
        orderJson.lineItems[0].effectiveDate = DEFAULT_START_DATE
        orderJson.termLength = Recurrence.ONE_YEAR_CYCLE
        orderJson.billingCycle = Recurrence.ONE_YEAR_CYCLE
        orderJson.startDate = DEFAULT_START_DATE

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map order = client.get(getOrderUrl(response.locationId)).body
        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createOrderWithMonthlyRecurringPlan(HttpClient client, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Long startDate = DEFAULT_START_DATE) {
        Map plan = PlanSpec.createPlanWithMonthlyRecurringCharge(client)
        HttpResponse response = createDraftOrder(client, plan, termLength, billingCycle, startDate)

        Map order = client.get(getOrderUrl(response.locationId)).body
        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createOrderWithMonthlyRecurringPlanOfZeroAmount(HttpClient client, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Long startDate = DEFAULT_START_DATE) {
        Map plan = PlanSpec.createPlanWithMonthlyRecurringChargeOfZeroAmount(client)
        HttpResponse response = createDraftOrder(client, plan, termLength, billingCycle, startDate)

        Map order = client.get(getOrderUrl(response.locationId)).body
        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createOrderWithTenantDiscounts(HttpClient client, Map termLength = Recurrence.THREE_YEAR_CYCLE, List<Map> tenantDiscounts = null, String accountId = null) {
        if (tenantDiscounts == null) {
            Map discount = DiscountSpec.addAndGetDiscount(client, 0.25)
            tenantDiscounts = [discount]
        }
        return createOrderWithRecurringPlan(client, termLength, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, tenantDiscounts, '', '', accountId)
    }

    static Map createOrderWithDocumentTemplate(HttpClient client, String templateId = null) {
        return createOrderWithRecurringPlan(client, Recurrence.THREE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, DEFAULT_START_DATE, null, '', templateId)
    }

    static Map createDraftOrderWithRecurringPlan(HttpClient client, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Long startDate = DEFAULT_START_DATE, List<Map> tenantDiscounts = null, String ruleId = '', String templateId = null, String accountId = null) {
        Map plan = PlanSpec.createPlanWithRecurringCharge(client, [:], [recognitionRuleId: ruleId])
        HttpResponse response = createDraftOrder(client, plan, termLength, billingCycle, startDate, null, tenantDiscounts, accountId, false, '', templateId)

        if (response.error) {
            logger.severe("create draft order got error response: " + response.error)
        }

        HttpResponse getResponse = client.get(getOrderUrl(response.locationId))

        if (getResponse.error) {
            logger.severe("get order received error response: " + getResponse.error)
        }

        return getResponse.body
    }

    static Map createOrderWithRecurringPlan(HttpClient client, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Long startDate = DEFAULT_START_DATE, List<Map> tenantDiscounts = null, String ruleId = '', String templateId = null, String accountId = null) {
        Map order = createDraftOrderWithRecurringPlan(client, termLength, billingCycle, startDate, tenantDiscounts, ruleId, templateId, accountId)
        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createOrderWithUsagePlan(HttpClient client, Long startDate = DEFAULT_START_DATE) {
        Map plan = PlanSpec.createPlanWithUsageCharge(client)
        createOrderWithPlan(client, plan, startDate)
    }

    static Map createOrderWithUsagePlanWithBillingCycle(HttpClient client, Map plan, Long startDate = DEFAULT_START_DATE, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_MONTH_CYCLE) {
        createOrderWithPlan(client, plan, startDate, billingCycle, termLength)
    }

    static Map createOrderWithPricedUsagePlan(HttpClient client, Map plan, Long startDate = DEFAULT_START_DATE) {
        createOrderWithPlan(client, plan, startDate)
    }

    static Map createOrderWithRecurringAndUsagePlan(HttpClient client, Long startDate = DEFAULT_START_DATE) {
        Map plan = PlanSpec.createPlanWithRecurringAndUsageCharge(client)
        createOrderWithPlan(client, plan, startDate)
    }

    static HttpResponse createDraftOrder(HttpClient client, Map plan = null, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Long startDate = DEFAULT_START_DATE, String externalId = null, List<Map> tenantDiscounts = null, String accountId = null, boolean automaticPayment = false, String ruleId = '', String templateId = null, String paymentTerm = DEFAULT_PAYMENT_TERM) {
        Map orderJson = generateOrderJson(client, plan, termLength, billingCycle, startDate, externalId, tenantDiscounts, accountId, automaticPayment, ruleId, templateId, paymentTerm)

        return client.post(ORDER_PATH, orderJson)
    }

    static HttpResponse changeOrderStatus(HttpClient client, String orderId, String newStatus) {
        return client.put("${ORDER_PATH}/${orderId}/status/${newStatus}", null)
    }

    static HttpResponse createDraftOrderInDryRun(HttpClient client, Map plan = null, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Long startDate = DEFAULT_START_DATE, String externalId = null, List<Map> tenantDiscounts = null, String accountId = null, boolean automaticPayment = false, String ruleId = '', String templateId = null) {
        Map orderJson = generateOrderJson(client, plan, termLength, billingCycle, startDate, externalId, tenantDiscounts, accountId, automaticPayment, ruleId, templateId)
        String dryRunOrderPath = "${ORDER_PATH}/?isDryRun=true"

        return client.post(dryRunOrderPath, orderJson)
    }

    static Map generateOrderJson(HttpClient client, Map plan = null, Map termLength = Recurrence.ONE_YEAR_CYCLE, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, Long startDate = DEFAULT_START_DATE, String externalId = null, List<Map> tenantDiscounts = null, String accountId = null, boolean automaticPayment = false, String ruleId = '', String templateId = null, String paymentTerm = "NET30") {
        Map account = (accountId == null) ? AccountSpec.createAndGetCustomerAccount(client).body : AccountSpec.getCustomerAccount(client, accountId).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        if (templateId == null) {
            templateId = PredefinedTermsSpec.createOrderTemplate(client)
            PredefinedTermsSpec.updatePredefinedTermsStatus(client, templateId, "ACTIVE")
        }

        if (plan == null) {
            plan = PlanSpec.createAndGetPlanWithTaxRate(client, [recognitionRuleId: ruleId])
        }

        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan, null, termLength, billingCycle, startDate, 10, tenantDiscounts, null, null, false, paymentTerm)

        if (externalId != null) {
            orderJson[EXTERNAL_ID] = externalId
        }

        orderJson.orderFormTemplateIds = [templateId]

        return orderJson
    }

    static Map createOrderForPlans(HttpClient client, Map order = null, List<Map> plans = [], Long quantity = 1, List<List<Map>> rampOverrides = [], Map accountOverrides = [:]) {
        Map defaultOrder = [
            orderType        : "NEW",
            paymentTerm      : "NET30",
            startDate        : DEFAULT_START_DATE,
            termLength       : Recurrence.ONE_YEAR_CYCLE,
            billingCycle     : Recurrence.ONE_YEAR_CYCLE,
            billingTerm      : "UP_FRONT",
            ownerId          : "USER-ADMIN",
        ]
        if (!order) {
            order = [:]
        }
        order = defaultOrder + order

        if (!order.accountId) {
            Map accountJson = AccountSpec.getAccountJson(false, false, false, accountOverrides)
            Map account = AccountSpec.createAndGetCustomerAccount(client, accountJson).body
            Map contact = ContactSpec.createAndGetContact(client, account.id as String).body
            order.accountId = account.id as String
            order.shippingContactId = contact.id as String
            order.billingContactId = contact.id as String
        }

        plans = plans == null ? [] : plans
        if (plans.size() == 0 && !order.lineItems) {
            var plan = PlanSpec.createAndGetPlanWithTaxRate(client)
            plans = [plan]
        }

        if (!order.lineItems) {
            order.lineItems = []
            for (int i = 0; i < plans.size(); i++) {
                def planLineItems = getPlanLineItems(plans[i], order.startDate as Long, quantity, rampOverrides[i])
                order.lineItems.addAll(planLineItems)
            }
        }

        HttpResponse response = client.post(ORDER_PATH, order)
        assert response.locationId

        order = client.get(getOrderUrl(response.locationId)).body
        assert order?.id

        return order
    }

    static List<Map> getPlanLineItems(Map plan, Long startDate, Long quantity = 1, List<Map> rampOverrides = null) {
        if (rampOverrides) {
            List<Map> planLineItems = []
            for (def charge : plan.charges) {
                def chargeId = charge.id as String
                List<Map> rampLineItems = getRampOrderLineItemJson(plan.id as String, chargeId, rampOverrides)
                planLineItems.addAll(rampLineItems)
            }
            return planLineItems
        }
        return plan.charges.collect {
            getOrderLineItemJson(plan.id as String, it.id as String, startDate, quantity)
        }
    }

    static Map createOrderForPlan(HttpClient client, Map order = null, Map plan = null, Long quantity = 1, List<Map> planRampOverrides = null, Map accountOverrides = [:]) {
        def plans = plan == null ? [] : [plan]
        List<List<Map>> rampOverrides = planRampOverrides == null ? [] : [planRampOverrides]
        return createOrderForPlans(client, order, plans, quantity, rampOverrides, accountOverrides)
    }

    static Map executeOrderForPlan(HttpClient client, Map order = null, Map plan = null, Integer quantity = 1, List<Map> planRampOverrides = null, Map accountOverrides = [:]) {
        order = createOrderForPlan(client, order, plan, quantity, planRampOverrides, accountOverrides)
        assert order.id
        executeDraftOrder(client, order.id as String, order.executedOn as Long)
        return client.get(getOrderUrl(order.id as String)).body
    }

    static def executeDraftOrder(HttpClient client, String orderId, Long statusUpdatedOn = null) {
        // With the introduction of approvalFlows, a submitted order becomes auto approved unless there is one approval flows
        def status = ["submitted", "executed"]

        status.forEach { s ->
            HttpResponse response = updateOrderStatus(client, orderId, s, statusUpdatedOn)

            if (response.status != 200) {
                throw new IllegalStateException(response.error)
            }
        }
    }

    // executes a draft order, generates an invoice on the subscription and posts the invoice
    // returns posted invoice and executed order
    static List<Map> executeOrderAndPostInvoiceOrCreditMemo(HttpClient client, String orderId, Map options = [:]) {
        executeDraftOrder(client, orderId)
        def executedOrder = client.get(getOrderUrl(orderId)).body
        assert executedOrder.subscriptionId
        String subscriptionId = executedOrder.subscriptionId
        client.post(BaseInvoiceSpec.getGenerateInvoicePath(subscriptionId, executedOrder.startDate as Long), [:])
        def invoiceResponse = BaseInvoiceSpec.getInvoiceBySubscriptionId(client, subscriptionId)
        def invoices = invoiceResponse.body?.data
        assert invoices.size() > 0
        def invoice = invoices.get(0)
        assert invoice.invoiceNumber
        Map postedInvoiceOrCreditMemo = InvoiceGqlSpec.postInvoiceOrCreditMemo(client, invoice, options)
        return [
            postedInvoiceOrCreditMemo,
            executedOrder
        ]
    }

    static HttpResponse updateOrderStatus(HttpClient client, String orderId, String orderStatus, Long statusUpdatedOn = null) {
        String path = ORDER_PATH + "/" + orderId + "/status/" + orderStatus
        if (statusUpdatedOn) {
            path = path + "?statusUpdatedOn=" + statusUpdatedOn
        }
        return client.put(path, null)
    }

    static Map createOrderWithPercentDiscount(HttpClient client, long startDate = DEFAULT_START_DATE, Map account = null, String discountPercent = "0.1", Map chargeOverrides = [:]) {
        if (account == null) {
            account = AccountSpec.createAndGetCustomerAccount(client).body
        }

        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createAndGetPlanWithTaxRate(client, chargeOverrides)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan, discountPercent, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, startDate)

        HttpResponse response = client.post(ORDER_PATH, orderJson)

        Map order = client.get(getOrderUrl(response.locationId)).body
        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createOrderWithFixedAmountDiscount(HttpClient client, long startDate = DEFAULT_START_DATE, Map account = null) {
        if (account == null) {
            account = AccountSpec.createAndGetCustomerAccount(client).body
        }

        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createAndGetPlanWithTaxRate(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan, null, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, startDate, 10, null, "5.5")

        HttpResponse response = client.post(ORDER_PATH, orderJson)

        Map order = client.get(getOrderUrl(response.locationId)).body
        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createRecurringOrderWithDiscount(HttpClient client, Long startDate = DEFAULT_START_DATE) {
        Map order = createDraftRecurringOrderWithDiscount(client, "0.3", startDate)
        executeDraftOrder(client, order.id)
        return client.get(getOrderUrl(order.id)).body
    }

    static Map createDraftRecurringOrderWithDiscount(HttpClient client, String discount, Long startDate = DEFAULT_START_DATE, String ownerUserId = "USER-ADMIN") {
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan, discount, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, startDate,
                10, null, null, null, false, DEFAULT_PAYMENT_TERM, ownerUserId)
        HttpResponse response = client.post(ORDER_PATH, orderJson)

        return client.get(getOrderUrl(response.locationId)).body
    }

    static Map createOrderJson(String accountId,
            String contactId,
            Map plan,
            String discountPercent = null,
            Map termLength = Recurrence.ONE_YEAR_CYCLE,
            Map billingCycle = Recurrence.ONE_YEAR_CYCLE,
            Long startDate = DEFAULT_START_DATE,
            Long quantity = 10,
            List<Map> tenantDiscounts = null,
            String discountAmount = null,
            String purchaseOrderNumber = null,
            boolean purchaseOrderRequiredForInvoicing = false,
            String paymentTerm = DEFAULT_PAYMENT_TERM,
            String ownerUserId = "USER-ADMIN") {
        Map orderJson = getOrderRequestJson(accountId, contactId, "NEW", termLength, billingCycle, UUID.randomUUID().toString(), startDate, tenantDiscounts, purchaseOrderNumber, purchaseOrderRequiredForInvoicing, paymentTerm, ownerUserId)

        List<String> tenantDiscountIds = null
        if (tenantDiscounts != null) {
            tenantDiscountIds = tenantDiscounts.id
        }

        for (def charge : plan.charges) {
            def orderLineItem = getOrderLineItemJson(plan.id as String, charge.id as String, startDate, quantity)
            if (charge.isDiscount) {
                continue
            }
            if (discountPercent || discountAmount) {
                orderLineItem.discounts = [
                    [
                        name          : "default",
                        percent       : discountPercent,
                        discountAmount: discountAmount
                    ]
                ]
            }

            orderLineItem.predefinedDiscounts = tenantDiscountIds
            orderJson.lineItems.add(orderLineItem)
        }
        return orderJson
    }

    static Map addPlanToOrder(Map orderJson, Map plan, Long effectiveDate = null, Map lineItemOverrides = [:]) {
        for (def charge : plan.charges) {
            def orderLineItem = getOrderLineItemJson(plan.id as String, charge.id as String, effectiveDate, lineItemOverrides.quantity ?: 10, lineItemOverrides)
            orderJson.lineItems.add(orderLineItem)
        }
        return orderJson
    }

    static Map addRampPlanToOrder(Map orderJson, int lineItemIndex = 0, List<Long> effectiveDates = null, Map lineItemOverrides = [:]) {
        if (effectiveDates == null) {
            effectiveDates = List.of(orderJson.startDate, DateTimeHelper.plusYears(orderJson.startDate, 1, DateTimeHelper.DEFAULT_TZ))
        }
        Map nonRampedItem = orderJson.lineItems.remove(lineItemIndex)
        Integer quantity = 10
        for (long effectiveDate : effectiveDates) {
            def orderLineItem = getOrderLineItemJson(nonRampedItem.planId, nonRampedItem.chargeId, effectiveDate, quantity)
            orderLineItem.isRamp = true
            orderJson.lineItems.add(orderLineItem)
            quantity += 10
        }
        return orderJson
    }

    static Map createRampOrderWithRecurringPlan(HttpClient client, Map billingCycle = Recurrence.ONE_YEAR_CYCLE, boolean createSubscription = true, boolean rampQuantity = false, String discountPercent = null) {
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderRequest = createOrderRampJson(account.id, contact.id, plan.id, plan.charges[0].id, discountPercent, rampQuantity)
        orderRequest.billingCycle = billingCycle
        HttpResponse response = client.post(getORDER_PATH(), orderRequest)

        Map order = client.get(getOrderUrl(response.locationId)).body
        if (createSubscription) {
            executeDraftOrder(client, order.id)
        }
        Map toReturn = client.get(getOrderUrl(order.id)).body
        addMetricsToOrderMap(order.id, toReturn, client)
        return toReturn
    }

    static Map createOrderJsonWithNoLineItems(HttpClient client, Map recurrence = Recurrence.THREE_YEAR_CYCLE) {
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        return getOrderRequestJson(account.id, contact.id, "NEW", recurrence)
    }

    static Map createOrderRampJson(String accountId, String contactId, String planId, String chargeId, String discountPercent, boolean rampQuantities) {
        Map orderJson = getOrderRequestJson(accountId, contactId, "NEW", Recurrence.THREE_YEAR_CYCLE)
        Long effectiveDate = DEFAULT_START_DATE
        Integer startingQuantity = 10
        for (int year = 0; year < 3; year++) {
            def orderLineItem = getOrderLineItemJson(planId, chargeId, effectiveDate, startingQuantity)
            if (discountPercent) {
                orderLineItem.discounts = [
                    [
                        name   : "default",
                        percent: discountPercent
                    ]
                ]
            }
            if (rampQuantities) {
                startingQuantity += 10
            }
            orderLineItem.isRamp = true
            orderJson.lineItems.add(orderLineItem)
            effectiveDate = DateTimeHelper.plusYears(effectiveDate, 1, DateTimeHelper.DEFAULT_TZ)
        }
        return orderJson
    }

    static Map createOrderWithPurchaseOrder(HttpClient client, String purchaseOrderNumber = null, boolean purchaseOrderRequiredForInvoicing = false, def startDate = DEFAULT_START_DATE) {
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id as String).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map orderJson = createOrderJson(account.id as String, contact.id as String, plan, null,
                Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE, startDate, 10,
                null, null, purchaseOrderNumber, purchaseOrderRequiredForInvoicing)

        HttpResponse response = client.post(ORDER_PATH, orderJson)
        Map draftOrder = getOrder(client, response.locationId)
        String orderId = draftOrder.id as String
        executeDraftOrder(client, orderId)
        return getOrder(client, orderId)
    }

    static HttpResponse createOrderWithExistingPlan(HttpClient client, String planId) {
        Map planJson = client.get(PlanSpec.getPlanUrl(planId)).body
        Map orderJson = generateOrderJson(client, planJson)
        return client.post(ORDER_PATH, orderJson)
    }

    static Map getOrderLineItemJson(String planId, String chargeId, Long effectiveDate = null, Long quantity = 10, Map lineItemOverrides = [:]) {
        return [
            planId       : planId,
            chargeId     : chargeId,
            quantity     : quantity,
            effectiveDate: effectiveDate == null ? DEFAULT_START_DATE : effectiveDate
        ] + lineItemOverrides
    }

    static List<Map> getRampOrderLineItemJson(String planId, String chargeId, List<Map> rampOverrides) {
        List<Map> lineItems = []

        for (Map ramp : rampOverrides) {
            def discountPercent = ramp.discountPercent
            ramp.discountPercent = null
            Map lineItem = getOrderLineItemJson(planId, chargeId, ramp.effectiveDate as Long, ramp.quantity as Long, ramp + [isRamp: true])
            if (discountPercent) {
                lineItem.discounts = [
                    [
                        name          : "default",
                        percent       : discountPercent,
                    ]
                ]
            }
            lineItems.add(lineItem)
        }
        return lineItems
    }

    static Map deleteOrder(HttpClient client, String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null
        }
        HttpResponse response = client.delete(getOrderUrl(orderId))

        if (response.error) {
            logger.severe("Received error response: " + response.error)
        }

        return response.body
    }

    static Map getCustomBillingScheduleForOrder(HttpClient client, String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null
        }
        HttpResponse response = client.get(getOrderUrl(orderId) + "/billing/custom")

        if (response.error) {
            logger.severe("Received error response: " + response.error)
        }

        return response.body
    }
}
