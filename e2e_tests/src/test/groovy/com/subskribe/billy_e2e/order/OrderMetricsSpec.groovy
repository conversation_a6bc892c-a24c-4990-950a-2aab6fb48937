package com.subskribe.billy_e2e.order

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.order.amendment.AmendmentOrderSpec
import com.subskribe.billy_e2e.order.renewal.RenewOrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.subscription.SubscriptionSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.Recurrence

class OrderMetricsSpec extends Authenticated {

    def "get order metrics for expiration cancel"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("-105.00")
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String subscriptionId = order.subscriptionId
        Long endDate = order.endDate as Long
        HttpResponse cancelResponse = CancelOrderSpec.cancelSubscription(client, subscriptionId, order.shippingContactId as String, order.billingContactId as String, endDate)
        String cancelOrderId = cancelResponse.locationId
        HttpResponse metricsResponse = client.get(getOrderMetricsPath(cancelOrderId, endDate))

        then:
        metricsResponse.status == 200
        metricsResponse.body.tcv == BigDecimal.ZERO
        metricsResponse.body.recurringTotal == BigDecimal.ZERO
        metricsResponse.body.entryArr == expectedAmount
        metricsResponse.body.deltaArr == expectedAmount
        metricsResponse.body.arr == expectedAmount
        metricsResponse.body.averageArr == expectedAmount
    }

    def "get order metrics for expiration cancel in the future"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("-105.00")
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String subscriptionId = order.subscriptionId
        Long endDate = order.endDate as Long
        HttpResponse cancelResponse = CancelOrderSpec.cancelSubscription(client, subscriptionId, order.shippingContactId as String, order.billingContactId as String, endDate)
        String cancelOrderId = cancelResponse.locationId
        HttpResponse metricsResponse = client.get(getOrderMetricsPath(cancelOrderId, endDate - 1))

        then:
        metricsResponse.status == 200
        metricsResponse.body.tcv == BigDecimal.ZERO
        metricsResponse.body.recurringTotal == BigDecimal.ZERO
        metricsResponse.body.entryArr == expectedAmount
        metricsResponse.body.deltaArr == expectedAmount
        metricsResponse.body.arr == BigDecimal.ZERO
    }

    def "get order metrics for expiration cancel of ramp subscription"() {
        when:
        BigDecimal expectedArr = new BigDecimal("-315.00")
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client, Recurrence.ONE_YEAR_CYCLE, true, true)
        String subscriptionId = order.subscriptionId
        Long endDate = order.endDate as Long
        HttpResponse cancelResponse = CancelOrderSpec.cancelSubscription(client, subscriptionId, order.shippingContactId as String, order.billingContactId as String, endDate)
        String cancelOrderId = cancelResponse.locationId
        HttpResponse response = client.get(getOrderMetricsPath(cancelOrderId, endDate))

        then:
        response.status == 200
        response.body.tcv == BigDecimal.ZERO
        response.body.entryArr == expectedArr
        response.body.deltaArr == expectedArr
    }

    def "get order metrics for one year subscription"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("105.00")
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String orderId = order.id
        Long startDate = order.startDate as Long
        Long endDate = order.endDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate))

        then:
        response.status == 200
        response.body.tcv == expectedAmount
        response.body.recurringTotal == expectedAmount
        response.body.nonRecurringTotal == BigDecimal.ZERO
        response.body.entryArr == expectedAmount
        response.body.exitArr == expectedAmount
        response.body.arr == expectedAmount
        response.body.averageArr == expectedAmount
        response.body.deltaArr == expectedAmount
        response.body.deltaTcv == expectedAmount
        response.body.arrTrend.size() == 2
        response.body.arrTrend.get(0).instant == startDate
        response.body.arrTrend.get(0).amount == expectedAmount
        response.body.arrTrend.get(1).instant == endDate
        response.body.arrTrend.get(1).amount == BigDecimal.ZERO
    }

    def "get order line metrics for one year subscription"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("105.00")
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String orderLineId = order.lineItems.get(0).id
        String orderId = order.id
        HttpResponse response = client.get(getOrderLineMetricsPath(orderId))

        then:
        response.status == 200
        response.body.size() == 1
        response.body.get(orderLineId).tcv == expectedAmount
        response.body.get(orderLineId).entryArr == expectedAmount
    }

    def "get order metrics for 3 year subscription"() {
        when:
        BigDecimal arrAmount = new BigDecimal("105.00")
        Map order = OrderSpec.createOrderWithRecurringPlan(client, Recurrence.THREE_YEAR_CYCLE)
        String orderId = order.id
        Long startDate = order.startDate as Long
        Long endDate = order.endDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate))

        then:
        response.status == 200
        response.body.tcv == new BigDecimal("315.00")
        response.body.entryArr == arrAmount
        response.body.exitArr == arrAmount
        response.body.arr == arrAmount
        response.body.averageArr == arrAmount
        response.body.deltaArr == arrAmount
        response.body.arrTrend.size() == 2
        response.body.arrTrend.get(0).instant == startDate
        response.body.arrTrend.get(0).amount == arrAmount
        response.body.arrTrend.get(1).instant == endDate
        response.body.arrTrend.get(1).amount == BigDecimal.ZERO
    }

    def "get order metrics for custom recurring charge order"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("105.00")
        Map order = OrderSpec.createOrderWithRecurringCustomCharge(client)
        String orderId = order.id
        Long startDate = order.startDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate))

        then:
        response.status == 200
        response.body.tcv == expectedAmount
        response.body.recurringTotal == expectedAmount
        response.body.nonRecurringTotal == BigDecimal.ZERO
        response.body.entryArr == expectedAmount
        response.body.exitArr == expectedAmount
        response.body.arr == expectedAmount
        response.body.averageArr == expectedAmount
        response.body.deltaArr == expectedAmount
        response.body.deltaTcv == expectedAmount
        response.body.arrTrend.size() == 2
    }

    def "get order line metrics for custom recurring charge order"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("105.00")
        Map order = OrderSpec.createOrderWithRecurringCustomCharge(client)
        String orderLineId = order.lineItems.get(0).id
        String orderId = order.id
        HttpResponse response = client.get(getOrderLineMetricsPath(orderId))

        then:
        response.status == 200
        response.body.size() == 1
        response.body.get(orderLineId).tcv == expectedAmount
        response.body.get(orderLineId).entryArr == expectedAmount
    }

    def "get order metrics for one time charge order"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("175.00")
        Map order = OrderSpec.createOrderWithOTCPlan(client)
        String orderId = order.id
        Long startDate = order.startDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate))

        then:
        response.status == 200
        response.body.tcv == expectedAmount
        response.body.recurringTotal == BigDecimal.ZERO
        response.body.nonRecurringTotal == expectedAmount
        response.body.entryArr == BigDecimal.ZERO
        response.body.exitArr == BigDecimal.ZERO
        response.body.arr == BigDecimal.ZERO
        response.body.averageArr == BigDecimal.ZERO
        response.body.deltaArr == BigDecimal.ZERO
        response.body.deltaTcv == expectedAmount
        response.body.arrTrend.size() == 0
    }

    def "get order metrics for prepaid charge without recurrence"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("22.50")
        Map order = OrderSpec.createOrderWithPrepaidPlan(client, [shouldTrackArr: false])
        String orderId = order.id
        Long startDate = order.startDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate))

        then:
        response.status == 200
        response.body.tcv == expectedAmount
        response.body.recurringTotal == BigDecimal.ZERO
        response.body.nonRecurringTotal == expectedAmount
        response.body.entryArr == BigDecimal.ZERO
        response.body.exitArr == BigDecimal.ZERO
        response.body.arr == BigDecimal.ZERO
        response.body.averageArr == BigDecimal.ZERO
        response.body.deltaArr == BigDecimal.ZERO
        response.body.deltaTcv == expectedAmount
        response.body.arrTrend.size() == 0
    }

    def "get order metrics for prepaid charge with yearly recurrence"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("22.50")
        Map order = OrderSpec.createOrderWithPrepaidPlan(client, [recurrence: Recurrence.ONE_YEAR_CYCLE])
        String orderId = order.id
        Long startDate = order.startDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate))

        then:
        response.status == 200
        response.body.tcv == expectedAmount
        response.body.recurringTotal == expectedAmount
        response.body.nonRecurringTotal == BigDecimal.ZERO
        response.body.entryArr == expectedAmount
        response.body.exitArr == expectedAmount
        response.body.arr == expectedAmount
        response.body.averageArr == expectedAmount
        response.body.deltaArr == expectedAmount
        response.body.deltaTcv == expectedAmount
        response.body.arrTrend.size() == 2
    }

    def "get order metrics for prepaid charge with yearly recurrence in half year subscription"() {
        when:
        BigDecimal expectedAmount = new BigDecimal("22.50")
        Map plan = PlanSpec.createPlanWithPrepaidCharge(client,  [recurrence: Recurrence.ONE_YEAR_CYCLE])
        HttpResponse createOrderResponse = OrderSpec.createDraftOrder(client, plan, [cycle: "MONTH", step: 6])
        Map order = client.get(OrderSpec.getOrderUrl(createOrderResponse.locationId)).body
        OrderSpec.executeDraftOrder(client, order.id as String)
        String orderId = order.id
        Long startDate = order.startDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate))

        then:
        response.status == 200
        response.body.tcv == expectedAmount
        response.body.recurringTotal == expectedAmount
        response.body.nonRecurringTotal == BigDecimal.ZERO
        response.body.entryArr == expectedAmount * 2
        response.body.exitArr == expectedAmount * 2
        response.body.arr == expectedAmount * 2
        response.body.averageArr == expectedAmount * 2
        response.body.deltaArr == expectedAmount * 2
        response.body.deltaTcv == expectedAmount
        response.body.arrTrend.size() == 2
    }

    def "get order metrics for updated subscription"() {
        when:
        BigDecimal arrAmount = new BigDecimal("52.50")
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String orderId = order.id
        Long startDate = order.startDate as Long
        Long endDate = order.endDate as Long
        Long targetDate = DateTimeHelper.plusMonths(startDate, 6, DateTimeHelper.DEFAULT_TZ)
        Map newOrder = AmendmentOrderSpec.postAddQuantityOrder(client, orderId, targetDate)
        HttpResponse response = client.get(getOrderMetricsPath(newOrder.id as String, targetDate))

        then:
        response.status == 200
        response.body.tcv == new BigDecimal("26.25")
        response.body.entryArr == arrAmount
        response.body.exitArr == arrAmount
        response.body.arr == arrAmount
        response.body.averageArr == arrAmount
        response.body.deltaArr == arrAmount
        response.body.arrTrend.size() == 2
        response.body.arrTrend.get(0).instant == targetDate
        response.body.arrTrend.get(0).amount == arrAmount
        response.body.arrTrend.get(1).instant == endDate
        response.body.arrTrend.get(1).amount == BigDecimal.ZERO
    }

    def "get order metrics for updated ramp subscription"() {
        when:
        BigDecimal arrAmount = new BigDecimal("52.50")
        Map order = OrderSpec.createRampOrderWithRecurringPlan(client)
        Long startDate = order.startDate as Long
        Long targetDate = DateTimeHelper.plusMonths(startDate, 18, DateTimeHelper.DEFAULT_TZ) // update second interval
        Map newOrder = AmendmentOrderSpec.postAddQuantityOrder(client, order.id as String, order.lineItems.get(1).id as String, targetDate)
        HttpResponse response = client.get(getOrderMetricsPath(newOrder.id as String, targetDate))

        then:
        response.status == 200
        response.body.tcv == new BigDecimal("26.25")
        response.body.entryArr == arrAmount
        response.body.arr == arrAmount
        response.body.averageArr == arrAmount / 3 // 6 months of 2nd segment amount over 18 month order term
        response.body.deltaArr == arrAmount
    }

    def "get order metrics for future subscription"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String orderId = order.id
        Long startDate = order.startDate as Long
        HttpResponse response = client.get(getOrderMetricsPath(orderId, startDate - 1))

        then:
        response.status == 200
        response.body.tcv == new BigDecimal("105.00")
        response.body.arr == BigDecimal.ZERO
        response.body.averageArr == new BigDecimal("105.00")
    }

    def "get order metrics for renewed subscription for the renewed line"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String subscriptionId = order.subscriptionId
        Map subscription = SubscriptionSpec.getSubscription(client, subscriptionId)
        Map renewalOrder = RenewOrderSpec.getRenewOrderJson(subscriptionId)
        renewalOrder.startDate = subscription.endDate as long
        HttpResponse response = client.post(BaseOrderSpec.ORDER_PATH, renewalOrder)
        HttpResponse saveRenewalOrderResponse = client.get("${BaseOrderSpec.ORDER_PATH}/${response.locationId}")
        Map savedRenewalOrder = saveRenewalOrderResponse.body
        savedRenewalOrder.lineItems.get(0).quantity = 15 // update quantity from 10 to 15
        savedRenewalOrder.endDate = null
        saveRenewalOrderResponse = client.put(BaseOrderSpec.ORDER_PATH, savedRenewalOrder)
        HttpResponse orderMetricsResponse = client.get(getOrderMetricsPath(savedRenewalOrder.id as String, savedRenewalOrder.startDate as Long - 1))

        then:
        saveRenewalOrderResponse.status == 200
        orderMetricsResponse.body.entryArr == new BigDecimal("157.50")
        orderMetricsResponse.body.deltaArr == new BigDecimal("52.50") // 157.50 (new order entry ARR) - 105.00 (previous sub exit ARR)
    }

    // todo: create a resource to fetch orderline metrics for an order
    def "get order metrics for renewed subscription for a new line"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client)
        String subscriptionId = order.subscriptionId
        Map subscription = SubscriptionSpec.getSubscription(client, subscriptionId)
        Map renewalOrder = RenewOrderSpec.getRenewOrderJson(subscriptionId)
        renewalOrder.startDate = subscription.endDate as Long
        HttpResponse response = client.post(BaseOrderSpec.ORDER_PATH, renewalOrder)
        HttpResponse saveRenewalOrderResponse = client.get("${BaseOrderSpec.ORDER_PATH}/${response.locationId}")
        Map savedRenewalOrder = saveRenewalOrderResponse.body
        savedRenewalOrder.lineItems.get(0).quantity = 15 // update quantity from 10 to 15
        savedRenewalOrder.endDate = null
        savedRenewalOrder.lineItems.get(0).action = "ADD"
        savedRenewalOrder.lineItems.get(0).baseExternalSubscriptionChargeId = null
        saveRenewalOrderResponse = client.put(BaseOrderSpec.ORDER_PATH, savedRenewalOrder)
        HttpResponse orderMetricsResponse = client.get(getOrderMetricsPath(savedRenewalOrder.id as String, savedRenewalOrder.startDate as Long + 1))

        then:
        saveRenewalOrderResponse.status == 200
        orderMetricsResponse.body.entryArr == new BigDecimal("157.50")
        orderMetricsResponse.body.deltaArr == new BigDecimal("52.50") // 157.50 (new order entry ARR) - 105.00 (previous sub exit ARR)
    }

    static Map getOrderMetrics(HttpClient client, String orderId, Long targetDate) {
        HttpResponse response = client.get(getOrderMetricsPath(orderId, targetDate))
        assert response.status == 200
        return response.body
    }

    static String getOrderMetricsPath(String orderId, Long targetDate) {
        String targetDateString = targetDate == null ? "" : "?targetDate=${targetDate}"
        return "/orders/${orderId}/metrics${targetDateString}"
    }

    static String getOrderLineMetricsPath(String orderId) {
        return "/orders/${orderId}/lineItems/metrics"
    }
}
