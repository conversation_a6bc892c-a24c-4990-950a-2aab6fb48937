package com.subskribe.billy_e2e.order

import static com.subskribe.billy_e2e.DateTimeHelper.plusMonths
import static com.subskribe.billy_e2e.DateTimeHelper.plusYears

import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.invoice.BaseInvoiceSpec
import com.subskribe.billy_e2e.productcatalog.PlanJsonBuilder
import com.subskribe.billy_e2e.productcatalog.PlanRelationshipSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.productcatalog.ProductSpec
import com.subskribe.billy_e2e.utils.Recurrence
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class PercentOfRampOrderSpec extends BaseOrderSpec {

    @Shared
    Map targetPlan

    @Shared
    Map perUnitPercentPlan

    @Shared
    Map perUnitPercentCharge


    def "create charges and make sure to link them as percent of relationship"() {
        when:
        // create plan with all charges
        HttpResponse response = ProductSpec.createProduct(client)
        def productId = response.locationId
        Map planJson = PlanJsonBuilder.getPlanWithAllChargesJson(productId)
        String planId = PlanSpec.createPlan(client, planJson).locationId
        targetPlan = PlanSpec.getPlanWithClient(client, planId)

        Map perUnitPercentJson = PlanJsonBuilder.getPlanJsonWithPercentOfCharge(productId, "PER_UNIT", 15.00)
        String perUnitPercentPlanId = PlanSpec.createPlan(client, perUnitPercentJson).locationId
        perUnitPercentPlan = PlanSpec.getPlanWithClient(client, perUnitPercentPlanId)
        perUnitPercentCharge = perUnitPercentPlan.charges.get(0)

        // now add the relationship between these product and plans
        HttpResponse relationShipTwoResponse = PlanRelationshipSpec.createPercentOfRelationship(client, perUnitPercentPlan, targetPlan)

        then:
        response.status == 201
        planId != null
        relationShipTwoResponse.status == 201
        perUnitPercentPlan.charges.size() == 1
    }

    def "create ramp order with target plan and percent of plans and make sure percent of is ramped"() {
        when:
        Map orderJson = OrderSpec.generateOrderJson(client, targetPlan, Recurrence.THREE_YEAR_CYCLE, Recurrence.ONE_QUARTER_CYCLE)
        List rampOverrides = [
            [effectiveDate: DEFAULT_START_DATE, quantity: 100],
            [effectiveDate: plusYears(DEFAULT_START_DATE, 1), quantity: 200],
            [effectiveDate: plusYears(DEFAULT_START_DATE, 2), quantity: 200],
        ]
        orderJson.lineItems.addAll(OrderSpec.getRampOrderLineItemJson(perUnitPercentPlan.id as String, perUnitPercentCharge.id as String, rampOverrides))
        HttpResponse dryRunResponse = client.post(ORDER_PATH, orderJson)

        then:
        dryRunResponse.status == 201
        dryRunResponse.body != null
        def perUnitPercentLines = dryRunResponse.body.lineItems.findAll {
            it.chargeId == perUnitPercentCharge.id
        }
        perUnitPercentLines.size() == 3
        perUnitPercentLines.every { it ->
            it.listUnitPrice > 0.00
            it.sellUnitPrice > 0.00
            it.amount > 0.00
            it.listAmount > 0.00
        }
    }


    def "create order with 2000 per year recurring charge and 10 percent ramped for 6 months"() {
        when:
        // Create product and plan with $2000 per year recurring charge
        HttpResponse response = ProductSpec.createProduct(client)
        def productId = response.locationId
        Map recurringChargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson([amount: 2000.00])
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [recurringChargeJson])
        String planId = PlanSpec.createPlan(client, planJson).locationId
        Map targetPlan = PlanSpec.getPlanWithClient(client, planId)

        // Create percent of charge plan with 10% charge
        Map percentOfChargeJson = PlanJsonBuilder.getPercentOfChargeJson([chargeModel: "PER_UNIT", percent: 10.00])
        Map percentOfPlanJson = PlanJsonBuilder.getPlanJson(productId, [percentOfChargeJson])
        String percentOfPlanId = PlanSpec.createPlan(client, percentOfPlanJson).locationId
        Map percentOfPlan = PlanSpec.getPlanWithClient(client, percentOfPlanId)
        Map percentOfCharge = percentOfPlan.charges.get(0)

        // Add relationship between plans
        HttpResponse relationshipResponse = PlanRelationshipSpec.createPercentOfRelationship(client, percentOfPlan, targetPlan)

        // Create order with ramped percent of charge for 6 months, and target quantity of 10
        Map orderJson = OrderSpec.generateOrderJson(client, targetPlan, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_YEAR_CYCLE)
        List rampOverrides = [
            [effectiveDate: DEFAULT_START_DATE, quantity: 1],
            [effectiveDate: plusMonths(DEFAULT_START_DATE, 6), quantity: 1],
        ]
        orderJson.lineItems.addAll(OrderSpec.getRampOrderLineItemJson(percentOfPlan.id as String, percentOfCharge.id as String, rampOverrides))
        HttpResponse dryRunResponse = client.post(ORDER_PATH, orderJson)

        then:
        dryRunResponse.status == 201
        dryRunResponse.body != null
        def percentOfLines = dryRunResponse.body.lineItems.findAll {
            it.chargeId == percentOfCharge.id
        }
        percentOfLines.size() == 2
        percentOfLines.every { it ->
            it.listUnitPrice == 1000.00
            it.sellUnitPrice == 1000.00
            it.amount == 1000.00
            it.listAmount == 1000.00
            it.quantity == 1
            it.isRamp == true
        }
    }

    def "validate invoice generation amounts for percent of ramp order"() {
        when:
        // Create product and plan with $2400 per year recurring charge (monthly billing)
        HttpResponse response = ProductSpec.createProduct(client)
        def productId = response.locationId
        Map recurringChargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson([amount: 2400.00])
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [recurringChargeJson])
        String planId = PlanSpec.createPlan(client, planJson).locationId
        Map targetPlan = PlanSpec.getPlanWithClient(client, planId)

        // Create percent of charge plan with 15% charge
        Map percentOfChargeJson = PlanJsonBuilder.getPercentOfChargeJson([chargeModel: "PER_UNIT", percent: 15.00])
        Map percentOfPlanJson = PlanJsonBuilder.getPlanJson(productId, [percentOfChargeJson])
        String percentOfPlanId = PlanSpec.createPlan(client, percentOfPlanJson).locationId
        Map percentOfPlan = PlanSpec.getPlanWithClient(client, percentOfPlanId)
        Map percentOfCharge = percentOfPlan.charges.get(0)

        // Add relationship between plans
        PlanRelationshipSpec.createPercentOfRelationship(client, percentOfPlan, targetPlan)

        // Create order with ramped quantities: 5 units for first 3 months, then 10 units
        Map orderJson = OrderSpec.generateOrderJson(client, targetPlan, Recurrence.ONE_YEAR_CYCLE, Recurrence.ONE_MONTH_CYCLE)
        List rampOverrides = [
            [effectiveDate: DEFAULT_START_DATE, quantity: 5],
            [effectiveDate: plusMonths(DEFAULT_START_DATE, 3), quantity: 10],
        ]
        orderJson.lineItems.addAll(OrderSpec.getRampOrderLineItemJson(percentOfPlan.id as String, percentOfCharge.id as String, rampOverrides))

        // Execute the order to create subscription
        Map order = OrderSpec.createAndGetOrder(client, orderJson)

        then:
        order.id != null
        order.subscriptionId != null

        when:
        // Generate invoice for first billing period (month 1)
        Map firstInvoice = BaseInvoiceSpec.generateAndPostInvoice(client, order.subscriptionId, order.startDate as Long)

        // Generate invoice for fourth month (after ramp change)
        Long fourthMonthDate = plusMonths(order.startDate as Long, 3)
        Map fourthMonthInvoice = BaseInvoiceSpec.generateAndPostInvoice(client, order.subscriptionId, fourthMonthDate)

        Map lastInvoice = BaseInvoiceSpec.generateAndPostInvoice(client, order.subscriptionId, order.endDate)

        then:
        // First invoice should have items for both target plan and percent-of plan at initial quantities
        firstInvoice.invoiceItems.size() == 2
        def firstTargetItem = firstInvoice.invoiceItems.find { it.chargeId == targetPlan.charges[0].id }
        def firstPercentOfItem = firstInvoice.invoiceItems.find { it.chargeId == percentOfCharge.id }

        firstTargetItem.amount == 2000
        firstPercentOfItem.amount == 1500
        firstInvoice.total == 3500

        // Fourth month invoice should reflect the ramped up quantities
        fourthMonthInvoice.invoiceItems.size() == 6

        def fourthPercentOfItems = fourthMonthInvoice.invoiceItems.findAll { it.chargeId == percentOfCharge.id }
        fourthPercentOfItems.size() == 3
        fourthPercentOfItems.each { it.amount == 1500 }

        fourthMonthInvoice.total == 12000

        // Last invoice
        lastInvoice.invoiceItems.size() == 16

        def lastPercentOfItems = fourthMonthInvoice.invoiceItems.findAll { it.chargeId == percentOfCharge.id }
        lastPercentOfItems.size() == 3
        lastPercentOfItems.each { it.amount == 1500 }

        lastInvoice.total == 40000

        // validate totals match order
        def invoices = [
            firstInvoice,
            fourthMonthInvoice,
            lastInvoice
        ]
        invoices.sum { it.total } == order.totalAmount
    }
}
