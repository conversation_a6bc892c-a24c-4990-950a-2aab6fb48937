package com.subskribe.billy_e2e.order


import com.subskribe.billy_e2e.EnumType
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.entity.EntityBaseGqlSpec
import com.subskribe.billy_e2e.productcatalog.PlanJsonBuilder
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.productcatalog.ProductSpec
import com.subskribe.billy_e2e.subscription.BaseSubscriptionSpec
import com.subskribe.billy_e2e.utils.Recurrence
import com.subskribe.billy_e2e.utils.Role
import spock.lang.Shared
import spock.lang.Stepwise

// validate order creation using plans with incompatible entity scope
@Stepwise
class OrderPlanEntityScopeSpec extends BaseOrderSpec {

    @Shared
    String entity1Id

    @Shared
    String entity2Id

    @Shared
    Map entity1Plan

    @Shared
    Map entity2Plan

    @Shared
    Map entities1And2Plan

    @Shared
    String orderId

    @Shared
    String subscriptionId

    def "create entities"() {
        when:
        def entity1 = EntityBaseGqlSpec.createEntity(client, [
            displayId    : "ENT-" + StringUtils.getRandomString(4),
            name         : "entity " +StringUtils.getRandomString(8),
            invoiceConfig: [
                prefix: StringUtils.getRandomString(5) + "-",
                scheme: new EnumType("SEQUENCE"),
                length: 6
            ],
        ])
        entity1Id = entity1.id

        def entity2 = EntityBaseGqlSpec.createEntity(client, [
            displayId    : "ENT-" + StringUtils.getRandomString(4),
            name         : "entity " +StringUtils.getRandomString(8),
            invoiceConfig: [
                prefix: StringUtils.getRandomString(5) + "-",
                scheme: new EnumType("SEQUENCE"),
                length: 6
            ],
        ])
        entity2Id = entity2.id

        then:
        entity1Id
        entity2Id
    }

    def "create plans scoped to entities"() {
        when:
        HttpResponse createProductResponse = ProductSpec.createProduct(client)
        String productId = createProductResponse.locationId

        Map plan1Json = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getRecurringPerUnitChargeJson([recurrence: Recurrence.ONE_YEAR_CYCLE])
        ])
        plan1Json.entityIds = [entity1Id]
        HttpResponse createPlan1Response = client.post(PlanSpec.PLAN_PATH, plan1Json)
        String plan1Id = createPlan1Response.locationId
        entity1Plan = client.get(PlanSpec.getPlanUrl(plan1Id)).body

        Map plan2Json = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getRecurringPerUnitChargeJson([recurrence: Recurrence.ONE_YEAR_CYCLE])
        ])
        plan2Json.entityIds = [entity2Id]
        HttpResponse createPlan2Response = client.post(PlanSpec.PLAN_PATH, plan2Json)
        String plan2Id = createPlan2Response.locationId
        entity2Plan = client.get(PlanSpec.getPlanUrl(plan2Id)).body

        Map plan3Json = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getRecurringPerUnitChargeJson([recurrence: Recurrence.ONE_YEAR_CYCLE])
        ])
        plan3Json.entityIds = [entity1Id, entity2Id]
        HttpResponse createPlan3Response = client.post(PlanSpec.PLAN_PATH, plan3Json)
        String plan3Id = createPlan3Response.locationId
        entities1And2Plan = client.get(PlanSpec.getPlanUrl(plan3Id)).body

        then:
        createProductResponse.status == 201
        createPlan1Response.status == 201
        plan1Json.entityIds.size() == 1
        plan1Json.entityIds.get(0) == entity1Id

        createPlan2Response.status == 201
        plan2Json.entityIds.size() == 1
        plan2Json.entityIds.get(0) == entity2Id

        createPlan3Response.status == 201
        plan3Json.entityIds.size() == 2
    }

    def "create order scoped to entity 2 using plan from entity 1 fails"() {
        when:
        // use context of entity 2
        EntityBaseGqlSpec.switchClientEntity(client, entity2Id)
        Map orderJson = OrderSpec.generateOrderJson(client, entity1Plan)
        orderJson.entityId = entity2Id
        HttpResponse addOrderResponse = client.post(ORDER_PATH, orderJson)

        then:
        orderJson.lineItems.size() == 1
        orderJson.lineItems.get(0).chargeId == entity1Plan.charges.get(0).id
        addOrderResponse.status == 400
        planNotFoundErrorMessage(addOrderResponse.error)
    }

    def "create order scoped to entity 2 using plan from entity 2"() {
        when:
        Map orderJson = OrderSpec.generateOrderJson(client, entity2Plan)
        orderJson.entityId = entity2Id
        HttpResponse addOrderResponse = client.post(ORDER_PATH, orderJson)
        orderId = addOrderResponse.locationId

        then:
        orderJson.lineItems.size() == 1
        orderJson.lineItems.get(0).chargeId == entity2Plan.charges.get(0).id
        addOrderResponse.status == 201
    }

    def "create order scoped to entity 2 using plan scoped to entities 1 and 2"() {
        when:
        Map orderJson = OrderSpec.generateOrderJson(client, entities1And2Plan)
        orderJson.entityId = entity2Id
        HttpResponse addOrderResponse = client.post(ORDER_PATH, orderJson)
        orderId = addOrderResponse.locationId

        then:
        orderJson.lineItems.size() == 1
        orderJson.lineItems.get(0).chargeId == entities1And2Plan.charges.get(0).id
        addOrderResponse.status == 201
    }

    def "update order to use plan from entity 1 fails"() {
        when:
        // use "all entities" context
        client.addHeader(API_KEY_HEADER, getApiKeyForUserWithRole(Role.ADMIN))
        Map orderJson = OrderSpec.getOrder(client, orderId)
        Map lineItem = orderJson.lineItems.get(0)
        lineItem.chargeId = entity1Plan.charges.get(0).id
        lineItem.planId = entity1Plan.id

        HttpResponse updateOrderResponse = updateOrder(client, orderJson)

        then:
        updateOrderResponse.status == 400
        containsEntityScopeErrorMessage(updateOrderResponse.error)
    }

    def "execute order and create subscription"() {
        when:
        OrderSpec.executeDraftOrder(client, orderId)
        Map order = OrderSpec.getOrder(client, orderId)
        subscriptionId = order.subscriptionId

        then:
        order.status == "EXECUTED"
        subscriptionId
    }

    def "amend subscription with new line item from entity 1 fails"() {
        when:
        HttpResponse draftOrderResponse = client.get(BaseSubscriptionSpec.getGenerateDraftAmendmentPath(subscriptionId))
        Map draftAmendmentOrder = draftOrderResponse.body
        Map amendmentOrderJson = OrderSpec.addPlanToOrder(draftAmendmentOrder, entity1Plan, draftAmendmentOrder.startDate, [action: "ADD"])
        HttpResponse addAmendmentResponse = client.post(ORDER_PATH, amendmentOrderJson)

        then:
        addAmendmentResponse.status == 400
        containsEntityScopeErrorMessage(addAmendmentResponse.error)
    }

    private static boolean planNotFoundErrorMessage(String errorMessage) {
        errorMessage.contains("invalid plan ids provided")
    }

    private static boolean containsEntityScopeErrorMessage(String errorMessage) {
        errorMessage.contains("not scoped to order's entity")
    }
}
