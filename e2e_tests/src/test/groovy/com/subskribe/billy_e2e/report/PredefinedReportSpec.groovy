package com.subskribe.billy_e2e.report

import com.subskribe.billy_e2e.HttpResponse

class PredefinedReportSpec extends BaseReportSpec {

    def "verify open_invoices report query syntax"() {
        when:
        HttpResponse response = runPredefinedReportId(client, "open_invoices")
        then:
        assertReportResponse(response, "Open Invoices Report")
    }

    def "verify open_credit_memos report query syntax"() {
        when:
        HttpResponse response = runPredefinedReportId(client, "open_credit_memos")
        then:
        assertReportResponse(response, "Open Credit Memos Report")
    }

    def "verify credit_memo_details report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "credit_memo_details", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Credit Memo Details Report")
    }

    def "verify refunds report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "refunds", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Refunds Report")
    }

    def "verify invoice_details report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "invoice_details", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Invoice Details Report")
    }

    def "verify bookings report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "bookings", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Bookings Report")
    }

    def "verify booking_details report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "booking_details", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Booking Details Report")
    }

    def "verify order_line_item_details report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "order_line_details", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Order Line Details Report")
    }

    def "verify active_subscriptions report query syntax"() {
        when:
        HttpResponse response = runPredefinedReportId(client, "active_subscriptions")
        then:
        assertReportResponse(response, "Active Subscriptions Report")
    }

    def "verify payments report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "payments_v3", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Successful Payments Report")
    }

    def "verify payments_bank_account_v3 report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "payments_bank_account_v3", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Successful Payments Report")
    }

    def "verify failed_payments report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "failed_payments_v3", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Failed Payments Report")
    }

    def "verify upcoming renewals report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "upcoming_renewals", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Upcoming Renewals Report")
    }

    def "verify invoice preview report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "invoice_preview", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Invoice Preview Report")
    }

    def "verify invoice preview by accounting period  query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "invoice_preview_by_accounting_period", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Invoice Preview By Accounting Period Report")
    }

    def "verify ar aging with date by account report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "ar_aging_with_report_date_by_account", [report_date: **********])
        then:
        assertReportResponse(response, "AR Aging Report - By Account")
    }

    def "verify ar aging with date by document report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "ar_aging_with_report_date_by_document", [report_date: **********])
        then:
        assertReportResponse(response, "AR Aging Report - By Document")
    }

    def "verify invoice and dunning emails report query syntax"() {
        when:
        HttpResponse response = runPredefinedReport(client, "invoice_and_dunning_emails_report_v2", [duration:[start:**********, end:**********]])
        then:
        assertReportResponse(response, "Invoice And Dunning Emails Report")
    }
}
