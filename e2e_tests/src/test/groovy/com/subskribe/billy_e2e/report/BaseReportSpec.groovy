package com.subskribe.billy_e2e.report

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.utils.Authenticated

abstract class BaseReportSpec extends Authenticated {

    private static final Map REPORT_HEADERS = [Accept: "application/octet-stream"]

    static final String REPORT_ROUTE = "/reports"

    static final String USAGE_STAT_REPORT_ROUTE = "/v2/usage/stats/csv"

    static HttpResponse runPredefinedReportId(HttpClient client, String reportId) {
        return client.postWithHeaders("${REPORT_ROUTE}/run", [reportId: reportId], REPORT_HEADERS)
    }

    static HttpResponse runPredefinedReport(HttpClient client, String reportId, Map params) {
        return client.postWithHeaders("${REPORT_ROUTE}/run", [reportId: reportId, params: params],
        REPORT_HEADERS)
    }

    static HttpResponse runPrepaidDrawDownReport(HttpClient client, long from, long to) {
        return client.getWithHeaders("${USAGE_STAT_REPORT_ROUTE}?from=${from}&to=${to}", REPORT_HEADERS)
    }

    static boolean assertReportResponse(HttpResponse response, String disposition) {
        assert response.status == 200
        Map<String, List<String>> responseHeaders = response.conn.getHeaderFields()
        assert responseHeaders["Content-Type"].get(0) == "text/csv"
        assert responseHeaders["Content-Disposition"].get(0).toLowerCase().contains("attachment; filename=\"" + disposition.toLowerCase())
        return true
    }
}
