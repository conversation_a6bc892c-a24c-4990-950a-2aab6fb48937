package com.subskribe.billy_e2e.customization

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.auth.BillyAdminApiSpec
import com.subskribe.billy_e2e.utils.Authenticated

class BaseCustomizationSpec extends  Authenticated {

    protected static final String CUSTOMIZATION_ADMIN_PATH = "/admin/customization"

    protected static final String CUSTOMIZATION_PATH = "/customization"

    String generateBillyApiKey() {
        HttpResponse apiKeyGenerationResponse = BillyAdminApiSpec.generateBillyApiKey(client)
        return apiKeyGenerationResponse.body.secretValue
    }

    HttpResponse testZeppaScriptAdminPath(String zeppaScript, String testOrderId, String billyEngineerApiKey) {
        def tenant = getCurrentTenant(client)
        HttpClient engineerClient = new HttpClient(BASE_URL)
        engineerClient.addHeader(API_KEY_HEADER, billyEngineer<PERSON>piKey)
        return engineerClient.putRawText(adminTestOrderCustomizationPath(tenant.tenantId as String, testOrderId), zeppaScript)
    }

    HttpResponse addZeppaScriptAdminPath(String zeppaScript, String testOrderId, String billyEngineerApiKey) {
        def tenant = getCurrentTenant(client)
        HttpClient engineerClient = new HttpClient(BASE_URL)
        engineerClient.addHeader(API_KEY_HEADER, billyEngineerApiKey)
        return engineerClient.putRawText(adminAddOrderCreationCustomization(tenant.tenantId as String, testOrderId, 0), zeppaScript)
    }

    HttpResponse getCurrentZeppaScriptAdminPath(String billyEngineerApiKey) {
        def tenant = getCurrentTenant(client)
        HttpClient engineerClient = new HttpClient(BASE_URL)
        engineerClient.addHeader(API_KEY_HEADER, billyEngineerApiKey)
        return engineerClient.get(adminGetOrderCreationCustomization(tenant.tenantId as String))
    }

    static String adminTestOrderCustomizationPath(String tenantId, String orderId) {
        return "${CUSTOMIZATION_ADMIN_PATH}/orderCreationCustomization/test?tenantId=${tenantId}&orderId=${orderId}"
    }

    static String adminAddOrderCreationCustomization(String tenantId, String orderId, int expectedVersion) {
        return "${CUSTOMIZATION_ADMIN_PATH}/orderCreationCustomization?tenantId=${tenantId}&orderId=${orderId}&expectedVersion=${expectedVersion}"
    }

    static String adminGetOrderCreationCustomization(String tenantId) {
        return "${CUSTOMIZATION_ADMIN_PATH}/orderCreationCustomization?tenantId=${tenantId}"
    }

    static String getOrderCreationCustomizationPath() {
        return "${CUSTOMIZATION_PATH}/orderCreationCustomization"
    }

    static String putOrderCreationCustomizationPath(String testOrderId, Integer expectedVersion) {
        return "${CUSTOMIZATION_PATH}/orderCreationCustomization?testOrderId=${testOrderId}&expectedVersion=${expectedVersion}"
    }
}
