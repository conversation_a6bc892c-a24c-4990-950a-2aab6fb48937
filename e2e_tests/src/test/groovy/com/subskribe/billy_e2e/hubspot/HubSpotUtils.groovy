package com.subskribe.billy_e2e.hubspot

import com.subskribe.billy_e2e.GqlUtils
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.QueryParamBuilder
import com.subskribe.billy_e2e.graphql.GqlSpec
import com.subskribe.billy_e2e.utils.TenantJobWait
import groovy.json.JsonBuilder
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class HubSpotUtils {

    static final String GQL_PATH = "/graphql"
    static final String COMPANY_NAME = "Example, Inc."
    static final String ALTERNATE_COMPANY_NAME_SOURCE_FIELD = "alternate_company_name"
    static final String UPDATE_HUBSPOT_FOR_ORDER_PATH = "/hubspot/test/updateHubSpotForOrder/%s"
    static final String DEAL_QUALIFIED_NAME = "deals"
    static final String COMPANY_QUALIFIED_NAME = "companies"
    static final String CONTACT_QUALIFIED_NAME = "contacts"
    static final String SUBSCRIPTION_QUALIFIED_NAME = "p45753606_subskribe_subscription"
    static final String SUBSCRIPTION_LINE_QUALIFIED_NAME = "p45753606_subskribe_line_item"
    static final String ORDER_QUALIFIED_NAME = "p45753606_subskribe_orders"
    static final String ORDER_LINE_QUALIFIED_NAME = "p45753606_subskribe_order_lines"
    static final String RATE_CARD_QUALIFIED_NAME = "p45753606_subskribe_rate_cards"
    static final String ORDER_ANNUAL_AMOUNT_QUALIFIED_QUALIFIED_NAME = "p45753606_subskribe_order_annual_amount"
    static final DELETE_HUBSPOT_SUBSCRIPTION_PATH = "/hubspot/sync/subscription/%s"

    public static final String HUBSPOT_ACCOUNT_SYNC_JOB = "HUBSPOT_ACCOUNT_SYNC"

    static Map createCompany(String accessToken, Map overrides = [:]) {
        def path = "/crm/v3/objects/companies"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        Map data = [
            properties: [
                name: COMPANY_NAME,
                domain: "example.com",
                address: "123 Maple Ave",
                city: "Cambridge",
                zip: "12345",
                country: "United States",
                phone: "(*************",
                state: "Massachusetts",
            ] + overrides
        ]
        def response = hubSpotClient.postJsonAndParse(path, data)
        assert response.id
        return response as Map
    }

    static Map syncCompanyToSubskribe(HttpClient client, String companyId) {
        return GqlSpec.runGqlMutation(client, "importAccountByCrmId", [ accountCrmId: companyId ], ['id', 'name', 'legalName'])
    }

    static Map getCompany(String accessToken, String companyId) {
        def path = "/crm/v3/objects/companies/${companyId}"
        def queryParam = QueryParamBuilder.buildQueryParam([
            properties: "name,subskribe_account,city,crm_city_string,crm_city_picklist,crm_city_multipicklist,crm_picklist_invalid"
        ])
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubSpotClient.get(path + queryParam)
        def parsedResponse = hubSpotClient.parseResponse(response)
        return parsedResponse
    }

    static deleteCompany(String accessToken, String companyId) {
        def path = "/crm/v3/objects/companies/${companyId}"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        hubSpotClient.delete(path)
    }

    static Map createDeal(String accessToken, String companyId, propertyOverrides = [:]) {
        def closeDate = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT)
        def defaultProperties = [
            amount: "1500.00",
            closedate: closeDate,
            dealname: "Test Deal",
            dealstage: "appointmentscheduled",
            pipeline: "default",
            dealtype: "newbusiness",
        ]
        def properties = defaultProperties + propertyOverrides
        def path = "/crm/v3/objects/deals"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        Map data = [
            properties: properties,
            "associations": [
                [
                    to: [ id: companyId ],
                    types: [
                        [
                            associationCategory: "HUBSPOT_DEFINED",
                            "associationTypeId": 5
                        ]
                    ]
                ]
            ]
        ]
        def response = hubSpotClient.postJsonAndParse(path, data)
        assert response.id
        return response as Map
    }

    static Map getOrderMapForDeal(Map deal, String accountId, String contactId, long startDate) {
        Map order = [
            startDate: startDate,
            accountId: accountId,
            shippingContactId: contactId,
            billingContactId: contactId,
            sfdcOpportunityId: deal.id as String,
            sfdcOpportunityName: deal.properties.dealname as String,
            sfdcOpportunityStage: deal.properties.dealstage as String,
            sfdcOpportunityType: deal.properties.dealtype as String,
        ]
        return order
    }

    static Map getDeal(String accessToken, String dealId) {
        def path = "/crm/v3/objects/deals/${dealId}"
        def queryParam = QueryParamBuilder.buildQueryParam([
            properties: "dealtype,dealstage,dealname,subskribe_primary_order_link,order_total,average_arr,delta_arr,entry_arr,exit_arr,subskribe_order_status,region,subskribe_order_discount_total,subskribe_order_discount_percent"
        ])
        HttpClient hubspotClient = new HttpClient("https://api.hubapi.com")
        hubspotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubspotClient.get(path + queryParam)
        def parsedResponse = hubspotClient.parseResponse(response)
        return parsedResponse
    }

    static updateHubSpotForOrder(client, String orderId) {
        def updatePath = String.format(UPDATE_HUBSPOT_FOR_ORDER_PATH, orderId)
        def updateResponse = client.post(updatePath, [:])
        assert updateResponse.status == 200
    }

    static Map updatePrimaryOrderForOpportunity(HttpClient client, String orderId) {
        var queryString = GqlUtils.mutation(
                "updatePrimaryOrderIdForOpportunity",
                [ orderId: orderId ],
                ['id', 'name']
                )
        HttpResponse response =  client.post(GQL_PATH, [query: queryString ])
        return response.body?.data?.updatePrimaryOrderIdForOpportunity
    }

    static Map getHubSpotSubscription(String accessToken, String subscriptionId) {
        def path = "/crm/v3/objects/${SUBSCRIPTION_QUALIFIED_NAME}/search"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        Map data = [
            filterGroups: [
                [
                    filters: [
                        [
                            propertyName: "subscription_id",
                            operator: "EQ",
                            value: subscriptionId
                        ]
                    ]
                ]
            ],
            properties: [
                'entry_arr',
                'exit_arr',
                'tcv',
                'subscription_id',
                'billing_cycle_start_date',
                'recurring_total',
                'non_recurring_total'
            ],
        ]
        def response = hubSpotClient.postJsonAndParse(path, data)
        if (response?.results?.size()) {
            return response.results[0] as Map
        }
    }

    static List<String> getHubSpotSubscriptionLineItemIds(String accessToken, String hubspotSubscriptionId) {
        def path = "/crm/v4/objects/${SUBSCRIPTION_QUALIFIED_NAME}/${hubspotSubscriptionId}/associations/${SUBSCRIPTION_LINE_QUALIFIED_NAME}"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubSpotClient.get(path)
        def parsedResponse = hubSpotClient.parseResponse(response)
        assert parsedResponse.results
        def results = parsedResponse.results as List<Map>
        return results.collect { it.toObjectId as String }
    }

    static Map getHubSpotSubscriptionLineItem(String accessToken, String lineItemId) {
        def path = "/crm/v3/objects/${SUBSCRIPTION_LINE_QUALIFIED_NAME}/${lineItemId}"
        def queryParam = QueryParamBuilder.buildQueryParam([
            properties: "name,quantity,charge_id,charge_name,plan_id,plan_name,discount,list_unit_price,sell_unit_price,line_start_date,line_end_date"
        ])
        HttpClient hubspotClient = new HttpClient("https://api.hubapi.com")
        hubspotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubspotClient.get(path + queryParam)
        def parsedResponse = hubspotClient.parseResponse(response)
        return parsedResponse
    }

    static Map getHubSpotOrder(String accessToken, String orderId) {
        def path = "/crm/v3/objects/${ORDER_QUALIFIED_NAME}/search"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        Map data = [
            filterGroups: [
                [
                    filters: [
                        [
                            propertyName: "primary_order_id",
                            operator: "EQ",
                            value: orderId
                        ]
                    ]
                ]
            ],
            properties: [
                'primary_order_id',
                'entry_arr',
                'exit_arr',
                'order_total',
                'subskribe_order_discount_total',
                'subskribe_order_discount_percent',
                'crm_region'
            ],
        ]
        def response = hubSpotClient.postJsonAndParse(path, data)
        if (response?.results?.size()) {
            return response.results[0] as Map
        }
    }

    static Map fetchOrderObject(String accessToken, String orderId) {
        Map hubspotOrder
        // wait for search indexes to update on hubspot
        for (int i = 0; i < 10; i++) {
            sleep(4000)
            hubspotOrder = getHubSpotOrder(accessToken, orderId)
            if (hubspotOrder?.id) break
        }
        assert hubspotOrder?.id
        return hubspotOrder
    }

    static List<String> getHubSpotOrderIdsForDeal(String accessToken, String dealId) {
        def path = "/crm/v4/objects/deals/${dealId}/associations/${ORDER_QUALIFIED_NAME}"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubSpotClient.get(path)
        def parsedResponse = hubSpotClient.parseResponse(response)
        def results = parsedResponse.results as List<Map>
        return results.collect { it.toObjectId as String }
    }

    static Map getHubSpotOrderById(String accessToken, String id) {
        def path = "/crm/v3/objects/${ORDER_QUALIFIED_NAME}/${id}"
        def queryParam = QueryParamBuilder.buildQueryParam([
            properties: "name,primary_order_id,entry_arr,exit_arr,order_total,subscription_link,crm_region"
        ])
        HttpClient hubspotClient = new HttpClient("https://api.hubapi.com")
        hubspotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubspotClient.get(path + queryParam)
        def parsedResponse = hubspotClient.parseResponse(response)
        return parsedResponse
    }

    static boolean verifyOrderObject(Map hubspotOrder, String orderId, String totalAmount, String arr) {
        assert hubspotOrder?.id
        assert hubspotOrder.properties.primary_order_id == orderId
        assert new BigDecimal(hubspotOrder.properties.order_total as String).round(2).toString() == totalAmount
        assert new BigDecimal(hubspotOrder.properties.entry_arr as String).round(2).toString() == arr
        assert new BigDecimal(hubspotOrder.properties.exit_arr as String).round(2).toString() == arr
        assert hubspotOrder.properties.subskribe_order_discount_total != null
        assert hubspotOrder.properties.subskribe_order_discount_percent != null
        return true
    }

    static boolean verifyDealObject(String accessToken, String dealId, Map order, String arr) {
        Map deal = getDeal(accessToken, dealId)
        String orderId = order.id as String
        assert deal.id
        assert deal.properties.order_total == order.totalAmount.round(2).toString()
        assert deal.properties.entry_arr == arr
        assert deal.properties.exit_arr == arr
        assert deal.properties.delta_arr == arr
        assert deal.properties.subskribe_order_status.toLowerCase() == order.status.toLowerCase()
        assert deal.properties.subskribe_primary_order_link ==~ /^http.*\/orders\/${orderId}$/
        assert deal.properties.subskribe_order_discount_total != null
        assert deal.properties.subskribe_order_discount_percent != null
        return true
    }

    static boolean verifyBlankDealObject(String accessToken, String dealId) {
        Map deal = getDeal(accessToken, dealId)
        assert deal.id
        assert deal.properties.order_total == ""
        assert deal.properties.entry_arr == ""
        assert deal.properties.exit_arr == ""
        assert deal.properties.delta_arr == ""
        assert deal.properties.subskribe_order_status == ""
        assert deal.properties.subskribe_primary_order_link == ""
        return true
    }

    static deleteRecords(String accessToken, String objectQualifiedName, List<String> objectIds) {
        def path = "/crm/v3/objects/${objectQualifiedName}/batch/archive"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        hubSpotClient.addHeader("Content-Type", "application/json")

        def requestBody = new JsonBuilder([
            inputs: objectIds.collect {
                [id: it]}
        ]).toString()
        hubSpotClient.post(path, requestBody)
    }

    static deleteDeal(String accessToken, String dealId) {
        def path = "/crm/v3/objects/deals/${dealId}"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        hubSpotClient.delete(path)
    }

    static deleteSubscription(client, String subscriptionId) {
        if (!subscriptionId) {
            return
        }
        def deletePath = String.format(DELETE_HUBSPOT_SUBSCRIPTION_PATH, subscriptionId)
        def deleteResponse = client.delete(deletePath)
        assert deleteResponse.status == 200
    }

    static def waitForTenantJob(HttpClient client, String jobType, String objectId) {
        TenantJobWait.waitUntilNoActiveJobs(client, jobType, objectId)
    }

    static List<String> getAssociatedOrderAnnualAmountIds(String accessToken, String dealId) {
        def path = "/crm/v4/objects/deal/${dealId}/associations/${ORDER_ANNUAL_AMOUNT_QUALIFIED_QUALIFIED_NAME}"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubSpotClient.get(path)
        def parsedResponse = hubSpotClient.parseResponse(response)
        assert parsedResponse.results
        def results = parsedResponse.results as List<Map>
        return results.collect { it.toObjectId as String }
    }

    static List<String> getAssociatedOrderLineIds(String accessToken, String orderObjectId) {
        def path = "/crm/v4/objects/${ORDER_QUALIFIED_NAME}/${orderObjectId}/associations/${ORDER_LINE_QUALIFIED_NAME}"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubSpotClient.get(path)
        def parsedResponse = hubSpotClient.parseResponse(response)
        assert parsedResponse.results
        def results = parsedResponse.results as List<Map>
        return results.collect { it.toObjectId as String }
    }

    static List<String> getAssociatedRateCardIds(String accessToken, String objectQualifiedName, String orderLineObjectId) {
        def path = "/crm/v4/objects/${objectQualifiedName}/${orderLineObjectId}/associations/${RATE_CARD_QUALIFIED_NAME}"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubSpotClient.get(path)
        def parsedResponse = hubSpotClient.parseResponse(response)
        assert parsedResponse.results
        def results = parsedResponse.results as List<Map>
        return results.collect { it.toObjectId as String }
    }

    static Map getOrderAnnualAmount(String accessToken, String orderAnnualAmountId) {
        def path = "/crm/v3/objects/${ORDER_ANNUAL_AMOUNT_QUALIFIED_QUALIFIED_NAME}/${orderAnnualAmountId}"
        def queryParam = QueryParamBuilder.buildQueryParam([
            properties: "year,amount"
        ])
        HttpClient hubspotClient = new HttpClient("https://api.hubapi.com")
        hubspotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubspotClient.get(path + queryParam)
        def parsedResponse = hubspotClient.parseResponse(response)
        return parsedResponse
    }

    static Map getOrderLine(String accessToken, String orderLineId) {
        def path = "/crm/v3/objects/${ORDER_LINE_QUALIFIED_NAME}/${orderLineId}"
        def queryParam = QueryParamBuilder.buildQueryParam([
            properties: "name,charge_name,quantity,tcv,product_category,product_name,plan_name,charge_id,currency,subscription_id,list_unit_price,sell_unit_price,discount,arr,acv,line_start_date,line_end_date,uuid,product_external_id,plan_external_id,charge_external_id,order_id,crm_region,subskribe_line_item_fx_rate"
        ])
        HttpClient hubspotClient = new HttpClient("https://api.hubapi.com")
        hubspotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubspotClient.get(path + queryParam)
        def parsedResponse = hubspotClient.parseResponse(response)
        return parsedResponse
    }

    static Map getRateCard(String accessToken, String rateCardId) {
        def path = "/crm/v3/objects/${RATE_CARD_QUALIFIED_NAME}/${rateCardId}"
        def queryParam = QueryParamBuilder.buildQueryParam([
            properties: "price_attribute_name,price_attribute_value,price_attribute_id,rate_card_id,order_id,order_line_id,subscription_id,subscription_charge_id"
        ])
        HttpClient hubspotClient = new HttpClient("https://api.hubapi.com")
        hubspotClient.addHeader("Authorization", "Bearer " + accessToken)
        def response = hubspotClient.get(path + queryParam)
        def parsedResponse = hubspotClient.parseResponse(response)
        return parsedResponse
    }

    static Map mergeDeal(String accessToken, String parentId, String childId) {
        def path = "/crm/v3/objects/deals/merge"
        HttpClient hubSpotClient = new HttpClient("https://api.hubapi.com")
        hubSpotClient.addHeader("Authorization", "Bearer " + accessToken)
        Map data = [
            objectIdToMerge: parentId,
            primaryObjectId: childId
        ]
        def response = hubSpotClient.postJsonAndParse(path, data)
        assert response.id
        return response as Map
    }
}
