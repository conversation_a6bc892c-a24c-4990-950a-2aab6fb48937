package com.subskribe.billy_e2e.hubspot

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.EnumField
import com.subskribe.billy_e2e.EnumType
import com.subskribe.billy_e2e.GqlUtils
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.account.ContactSpec
import com.subskribe.billy_e2e.auth.BillyAdminApiSpec
import com.subskribe.billy_e2e.category.HubSpotTest
import com.subskribe.billy_e2e.foreignexchange.CurrencyConversionRateGqlSpec
import com.subskribe.billy_e2e.foreignexchange.CurrencyTypeSettingGqlSpec
import com.subskribe.billy_e2e.graphql.AccountGqlSpec
import com.subskribe.billy_e2e.graphql.GqlSpec
import com.subskribe.billy_e2e.graphql.queries.OrderQueries
import com.subskribe.billy_e2e.opportunity.OpportunitySpec
import com.subskribe.billy_e2e.order.BaseOrderSpec
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.settings.SupportedCurrenciesSpec
import com.subskribe.billy_e2e.settings.TenantSettingRestAndGqlSpec
import com.subskribe.billy_e2e.utils.FeatureFlag
import java.time.Instant
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
@HubSpotTest
class HubSpotCompleteSpec extends GqlSpec {

    private static final String HUBSPOT_CONFIGURATION_PATH_FORMAT = "/admin/hubspot/configuration?tenantId=%s"
    private static final String GET_CONTACTS_BY_COMPANY_ID_PATH_FORMAT = "/hubspot/test/contactsByAccountCrmId/%s"
    private static final String GET_OBJECT_RECORDS_PATH_FORMAT = "/hubspot/%s?objectIds=%s"
    private static final String GET_HUBSPOT_ORDERS_BY_DEAL_ID_PATH_FORMAT = "/hubspot/test/hubSpotOrdersByDealId/%s"
    private static final String SYNC_HUBSPOT_SUBSCRIPTION_STATUS_PATH_FORMAT = "/hubspot/sync/subscription/%s/status"
    private static final String HUBSPOT_COMPANY_ID = "***********" // Random company with some contacts and legal name
    private static final String HUBSPOT_MERGED_COMPANY_ID_1 = "***********" // Company merged to ***********
    private static final String HUBSPOT_MERGED_COMPANY_ID_2 = "***********" // Company merged to ***********
    private static final List<String> HUBSPOT_COMPANIES = [
        "***********",
        "***********",
        "***********",
        "***********",
        "***********"
    ]
    private static final List<String> HUBSPOT_DEALS = [
        "***********",
        "***********",
        "***********",
        "35115776064",
        "35115764037"
    ]
    private static final List<String> HUBSPOT_CONTACTS = [
        "105460453482",
        "105471826061",
        "105460453553",
        "105471826177",
        "105460453660"
    ]

    private static final String SUBSKRIBE_COMPANY_NAME = "Subskribe"
    private static final String SUBSKRIBE_ALTERNATE_COMPANY_NAME = "Subskribe, Inc."
    private static final String CRM_ORDER_DELETION_SYNC_JOB = "CRM_ORDER_DELETION_SYNC"
    private static final String HUBSPOT_ORDER_SYNC_JOB = "HUBSPOT_ORDER_SYNC"
    private static final Long START_DATE = DateTimeHelper.parseToInstant("2023-06-15T00:00:00")
    private static final String FEATURE_NAME = "FOREIGN_EXCHANGE"
    private static final String TX_FEATURE_NAME = "TRANSACTIONAL_FOREIGN_EXCHANGE"
    private static final String ACCOUNT_PATH = "/accounts"

    @Shared
    String billyAdminApiKey

    @Shared
    String accessToken

    @Shared
    String accountId

    @Shared
    String contactId

    @Shared
    List<Map> plans = []

    @Shared
    List<Map> orders = []

    @Shared
    List<String> dealIds = []

    @Shared
    List<String> companyIds = []

    @Shared
    Map company

    @Shared
    Map deal

    @Shared
    String companyId

    @Shared
    Map account

    @Shared
    String dealId

    @Shared
    String parentDealId

    @Shared
    String childDealId

    @Shared
    String amendmentDealId

    @Shared
    String subscriptionId

    @Shared
    String hubspotSubscriptionId

    @Shared
    List<String> hubSpotSubscriptionLineItemIds

    @Shared
    List<String> orderAnnualAmountIds

    @Shared
    List<String> orderObjectIds

    @Shared
    List<String> orderLineIds

    @Shared
    String updateDealId

    @Shared
    String currencyConversionRateId

    @Shared
    String orderId

    @Shared
    private Map recurringPlan

    def setupSpec() {
        HttpResponse apiKeyGenerationResponse = BillyAdminApiSpec.generateBillyApiKey(client, "BILLY_ADMIN")
        billyAdminApiKey = apiKeyGenerationResponse.body.secretValue

        HubSpotIntegrationSpec.createTestIntegration(client)
        accessToken = HubSpotIntegrationSpec.setupAccessToken()
    }


    def "HubSpotSearchSpec: Get merged company 1"() {
        when:
        HttpResponse getCompaniesResponse = getObjectRecords(client, HubSpotUtils.COMPANY_QUALIFIED_NAME, List.of(HUBSPOT_MERGED_COMPANY_ID_1))

        then:
        getCompaniesResponse.status == 200
        getCompaniesResponse.body.size() == 1
        getCompaniesResponse.body[0].properties.hs_object_id == HUBSPOT_COMPANY_ID
    }


    def "HubSpotSearchSpec: Get merged company 2"() {
        when:
        HttpResponse getCompaniesResponse = getObjectRecords(client, HubSpotUtils.COMPANY_QUALIFIED_NAME, List.of(HUBSPOT_MERGED_COMPANY_ID_2))

        then:
        getCompaniesResponse.status == 200
        getCompaniesResponse.body.size() == 1
        getCompaniesResponse.body[0].properties.hs_object_id == HUBSPOT_COMPANY_ID
    }

    def "HubSpotOrderSpec: create and sync company"() {
        when:
        def company = HubSpotUtils.createCompany(accessToken, [name: "Reading Club, LLC."])
        companyId = company.id as String

        account = HubSpotUtils.syncCompanyToSubskribe(client, company.id as String)

        HubSpotUtils.waitForTenantJob(client, HubSpotUtils.HUBSPOT_ACCOUNT_SYNC_JOB, account.id as String)
        Map updatedCompany = HubSpotUtils.getCompany(accessToken, companyId)

        // Sleep to reduce rate-limiting
        sleep(15000)

        then:
        company.id != null
        account.id != null

        account.name == "Reading Club, LLC."
        updatedCompany?.properties?.subskribe_account ==~ /^http.*\/accounts\/${account.id as String}$/
    }

    def "HubSpotOrderSpec: create contact"() {
        when:
        Map contact = ContactSpec.createAndGetContact(client, account.id as String).body
        contactId = contact?.id

        then:
        contactId
    }

    def "HubSpotOrderSpec: create plans"() {
        when:
        Map plan1 = PlanSpec.createPlanWithRecurringCharge(client)
        Map plan2 = PlanSpec.createPlanWithOneTimeCharge(client)
        plans.addAll(plan1, plan2)

        then:
        plan1.id
        plan2.id
        plans.size() == 2
    }

    def "HubSpotOrderSpec: create parent deal"() {
        when:
        deal = HubSpotUtils.createDeal(accessToken, companyId)
        parentDealId = deal?.id as String
        sleep(15000)

        then:
        parentDealId
    }

    def "HubSpotOrderSpec: create child deal"() {
        when:
        deal = HubSpotUtils.createDeal(accessToken, companyId)
        childDealId = deal?.id as String
        sleep(15000)

        then:
        childDealId
    }

    def "HubSpotOrderSpec: merge deals"() {
        when:
        Map mergedDeal = HubSpotUtils.mergeDeal(accessToken, parentDealId, childDealId)
        dealId = mergedDeal?.id as String

        then:
        sleep(15000)
    }

    def "HubSpotOrderSpec: create and sync new order - candidate 1"() {
        when:
        Map order = HubSpotUtils.getOrderMapForDeal(deal, account.id, contactId, START_DATE)
        Map createdOrder = OrderSpec.createOrderForPlans(client, order, plans, 10)
        assert createdOrder.id
        def orderId = createdOrder.id as String
        orders.add(createdOrder)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, dealId, createdOrder, "105.00")
    }

    def "HubSpotOrderSpec: create and sync new order - candidate 2 - does not update"() {
        when:
        Map order = HubSpotUtils.getOrderMapForDeal(deal, account.id, contactId, START_DATE)
        Map createdOrder = OrderSpec.createOrderForPlans(client, order, plans, 20)
        assert createdOrder.id
        def orderId = createdOrder.id as String
        orders.add(createdOrder)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, dealId, orders[0], "105.00")
    }

    def "HubSpotOrderSpec: make order 2 as primary - updates the deal"() {
        when:
        def orderId = orders[1].id as String
        HubSpotUtils.updatePrimaryOrderForOpportunity(client, orderId)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, dealId, orders[1], "210.00")
    }

    def "HubSpotOrderSpec: create and sync new order - candidate 3 - does not update"() {
        when:
        Map order = HubSpotUtils.getOrderMapForDeal(deal, account.id, contactId, START_DATE)
        Map createdOrder = OrderSpec.createOrderForPlans(client, order, plans, 30)
        assert createdOrder.id
        def orderId = createdOrder.id as String
        orders.add(createdOrder)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, dealId, orders[1], "210.00")
    }

    def "HubSpotOrderSpec: execute order - candidate 1"() {
        when:
        def orderId = orders[0].id as String
        OrderSpec.executeDraftOrder(client, orderId)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        def executedOrder = OrderSpec.getOrder(client, orderId)
        assert executedOrder.id
        orders[0] = executedOrder
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, dealId, orders[0], "105.00")
    }

    def "HubSpotOrderSpec: verify that order object is populated"() {
        when:
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            orderObjectIds = HubSpotUtils.getHubSpotOrderIdsForDeal(accessToken, dealId)
            if (orderObjectIds.size() >= 1) {
                break
            }
        }

        assert orderObjectIds.size() >= 1

        Map order = HubSpotUtils.getHubSpotOrderById(accessToken, orderObjectIds[0])

        then:
        order.properties.name != null
        order.properties.primary_order_id != null
    }

    def "HubSpotOrderSpec: verify that order object using merged deal id"() {
        when:
        HttpResponse getHubSpotOrdersResponse = getHubSpotOrdersByDealId(client, childDealId)

        then:
        getHubSpotOrdersResponse.status == 200
        getHubSpotOrdersResponse.body.size() >= 1
    }

    def "HubSpotOrderSpec: verify that order object using parent deal id"() {
        when:
        HttpResponse getHubSpotOrdersResponse = getHubSpotOrdersByDealId(client, dealId)

        then:
        getHubSpotOrdersResponse.status == 200
        getHubSpotOrdersResponse.body.size() >= 1
    }


    def "HubSpotOrderSpec: verify order lines"() {
        when:
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            orderLineIds = HubSpotUtils.getAssociatedOrderLineIds(accessToken, orderObjectIds[0])
            if (orderLineIds?.size() >= 1) break
        }

        List<Map> orderLines = orderLineIds.collect {HubSpotUtils.getOrderLine(accessToken, it) }

        then:
        orderLines[0].properties.name != null
        orderLines[0].properties.charge_name != null
        orderLines[0].properties.currency == "USD"
        orderLines[0].properties.order_id != null
    }

    def "HubSpotOrderSpec: verify subscription"() {
        when:
        def orderId = orders[0].id as String
        def executedOrder = OrderSpec.getOrder(client, orderId)
        subscriptionId = executedOrder.subscriptionId as String
        Map hubspotSubscription
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            hubspotSubscription = HubSpotUtils.getHubSpotSubscription(accessToken, subscriptionId)
            if (hubspotSubscription?.id) break
        }
        assert hubspotSubscription?.id
        hubspotSubscriptionId = hubspotSubscription.id as String

        then:
        hubspotSubscription.properties.subscription_id == subscriptionId
        hubspotSubscription.properties.entry_arr == "105.00"
        hubspotSubscription.properties.exit_arr == "105.00"
        hubspotSubscription.properties.tcv == "280.00"
        hubspotSubscription.properties.recurring_total == "105.00"
        hubspotSubscription.properties.non_recurring_total == "175.00"
        assert hubspotSubscription.properties.billing_cycle_start_date
    }

    def "HubSpotOrderSpec: verify subscription line items"() {
        when:
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            hubSpotSubscriptionLineItemIds = HubSpotUtils.getHubSpotSubscriptionLineItemIds(accessToken, hubspotSubscriptionId)
            if (hubSpotSubscriptionLineItemIds?.size() == 2) break
        }
        assert hubSpotSubscriptionLineItemIds.size() == 2

        List<Map> lineItems = hubSpotSubscriptionLineItemIds
                .collect {HubSpotUtils.getHubSpotSubscriptionLineItem(accessToken, it) }
                .sort { a, b -> a.properties.charge_name.compareToIgnoreCase(b.properties.charge_name) }

        then:
        lineItems[0].properties.quantity == "10"
        lineItems[0].properties.list_unit_price == "17.50"
        lineItems[1].properties.quantity == "10"
        lineItems[1].properties.list_unit_price == "10.50"
    }

    def "HubSpotOrderSpec: verify order annual amounts are synced after order is executed"() {
        when:
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            orderAnnualAmountIds = HubSpotUtils.getAssociatedOrderAnnualAmountIds(accessToken, dealId)
            if (orderAnnualAmountIds?.size() == 1) break
        }

        List<Map> orderAnnualAmounts = orderAnnualAmountIds.collect {HubSpotUtils.getOrderAnnualAmount(accessToken, it) }

        then:
        assert orderAnnualAmountIds.size() == 1
        orderAnnualAmounts[0].properties.year == "2023"
        orderAnnualAmounts[0].properties.amount == "280.00"
    }

    def "HubSpotOrderSpec: create update deal"() {
        when:
        deal = HubSpotUtils.createDeal(accessToken, companyId)
        updateDealId = deal?.id as String
        sleep(15000)

        then:
        updateDealId
    }

    def "HubSpotOrderSpec: Update Opportunity ID for executed order"() {
        when:
        def orderId = orders[0].id as String
        HttpResponse updateResponse = BaseOrderSpec.updateOrderAttributes(client, orderId, [crmOpportunityId: updateDealId, crmOpportunityName: "Test Opportunity 2"])
        HubSpotUtils.waitForTenantJob(client, HUBSPOT_ORDER_SYNC_JOB, orderId)
        Map updatedOrder = OrderSpec.getOrder(client, orderId)

        then:
        updateResponse.status == 200
        updatedOrder.status == "EXECUTED"
        updatedOrder.sfdcOpportunityId == updateDealId
    }

    def "HubSpotOrderSpec: Opportunity is added for updated order"() {
        when:
        HttpResponse getOpportunityResponse = client.get("${OpportunitySpec.OPPORTUNITY_PATH}/crm/${updateDealId}")

        then:
        getOpportunityResponse.status == 200
        getOpportunityResponse.body.name == "Test Opportunity 2"
        getOpportunityResponse.body.opportunityId != null
        getOpportunityResponse.body.crmId == updateDealId
        getOpportunityResponse.body.primaryOrderId == orders[0].id as String
    }



    def "HubSpotOrderSpec: create amendment deal"() {
        when:
        deal = HubSpotUtils.createDeal(accessToken, companyId)
        amendmentDealId = deal?.id as String
        sleep(15000)

        then:
        amendmentDealId
        dealIds.add(amendmentDealId)
    }

    def "HubSpotOrderSpec: create amendment order"() {
        when:
        Map query = OrderQueries.generateAmendmentQuery(subscriptionId)
        HttpResponse generateAmendmentResponse = client.post(GQL_PATH, query)
        Map orderDetail = generateAmendmentResponse.body.data.generateAmendment

        Long effectiveDate = DateTimeHelper.plusMonths(orderDetail.startDate as long, 12, DateTimeHelper.DEFAULT_TZ)

        Map noneAmendmentMutation = createAmendmentWithOpptyMutation(
                orderDetail.subscriptionId,
                account.contact,
                orderDetail.lineItems[0].plan.id,
                orderDetail.lineItems[0].charge.id,
                orderDetail.lineItems[0].subscriptionChargeId,
                effectiveDate,
                "NONE",
                orderDetail.lineItems[0].quantity,
                amendmentDealId)

        HttpResponse noneAmendmentResponse = client.post(GQL_PATH, noneAmendmentMutation)
        def noneAmendmentOrder = noneAmendmentResponse.body.data.upsertAmendment
        HubSpotUtils.updateHubSpotForOrder(client, noneAmendmentOrder.id as String)
        HubSpotUtils.waitForTenantJob(client, HUBSPOT_ORDER_SYNC_JOB, noneAmendmentOrder.id as String)
        sleep(15000)

        then:
        generateAmendmentResponse.status == 200
        noneAmendmentResponse.status == 200
    }

    def "HubSpotOrderSpec: verify that amendment order object is populated"() {
        when:
        List<String> orderObjectIds
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            orderObjectIds = HubSpotUtils.getHubSpotOrderIdsForDeal(accessToken, amendmentDealId)
            if (orderObjectIds.size() == 1) {
                break
            }
        }

        assert orderObjectIds.size() == 1

        Map amendmentOrder = HubSpotUtils.getHubSpotOrderById(accessToken, orderObjectIds[0])

        then:
        amendmentOrder.properties.subscription_link != null
        plans.clear()
        orders.clear()
    }

    def "Sync subscription should not throw if account CRM ID is invalid"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map order = OrderSpec.executeOrderForPlan(client, null, plan)
        String subscriptionId = order.subscriptionId
        HttpResponse syncSubscriptionStatusResponse = syncHubspotSubscriptionStatus(client, subscriptionId)

        then:
        syncSubscriptionStatusResponse.status == 200
    }

    def "Sync subscription should not throw if account CRM ID is null"() {
        when:
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map order = OrderSpec.executeOrderForPlan(client, null, plan, 1, null, [
            crmId: null
        ])
        String subscriptionId = order.subscriptionId
        HttpResponse syncSubscriptionStatusResponse = syncHubspotSubscriptionStatus(client, subscriptionId)

        then:
        syncSubscriptionStatusResponse.status == 200
    }

    def "HubSpotSearchSpec: Get companies"() {
        when:
        HttpResponse getCompaniesResponse = getObjectRecords(client, HubSpotUtils.COMPANY_QUALIFIED_NAME, HUBSPOT_COMPANIES)

        then:
        getCompaniesResponse.status == 200
        getCompaniesResponse.body.size() == 5
    }

    def "HubSpotSearchSpec: Get deals"() {
        when:
        HttpResponse getDealsResponse = getObjectRecords(client, HubSpotUtils.DEAL_QUALIFIED_NAME, HUBSPOT_DEALS)

        then:
        getDealsResponse.status == 200
        getDealsResponse.body.size() == 5
    }

    def "HubSpotSearchSpec: Get contacts"() {
        when:
        HttpResponse getContactsResponse = getObjectRecords(client, HubSpotUtils.CONTACT_QUALIFIED_NAME, HUBSPOT_CONTACTS)

        then:
        getContactsResponse.status == 200
        getContactsResponse.body.size() == 5
    }

    def "HubSpotSearchSpec: Get non-existent records"() {
        when:
        List<String> randomIds = [
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString()
        ]

        HttpResponse getObjectsResponse = getObjectRecords(client, HubSpotUtils.COMPANY_QUALIFIED_NAME, randomIds)

        then:
        getObjectsResponse.status == 200
        getObjectsResponse.body.size() == 0
    }

    def "HubSpotSearchSpec: Invalid object type"() {
        when:
        List<String> randomIds = [
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString()
        ]

        HttpResponse getObjectsResponse = getObjectRecords(client, UUID.randomUUID().toString(), randomIds)

        then:
        getObjectsResponse.status == 404
    }

    def "Account name should be populated with the company name if alternate company name is not present"() {
        when:
        def account = HubSpotUtils.syncCompanyToSubskribe(client, HUBSPOT_COMPANY_ID)
        HubSpotUtils.waitForTenantJob(client, HubSpotUtils.HUBSPOT_ACCOUNT_SYNC_JOB, accountId)
        accountId = account.id as String

        then:
        accountId
        account.name == SUBSKRIBE_COMPANY_NAME
    }

    def "Update HubSpot configuration"() {
        when:
        HttpResponse getTenantResponse = client.get("/tenants")
        String tenantId = getTenantResponse.body.tenantId
        client.addHeader(API_KEY_HEADER, billyAdminApiKey)
        HttpResponse updateConfigurationResponse = updateHubSpotConfiguration(client, tenantId, HubSpotUtils.ALTERNATE_COMPANY_NAME_SOURCE_FIELD)

        then:
        updateConfigurationResponse.status == 200
    }

    def "Account name should be populated with the alternate company name if exists"() {
        when:
        def account = HubSpotUtils.syncCompanyToSubskribe(client, HUBSPOT_COMPANY_ID)
        HubSpotUtils.waitForTenantJob(client, HubSpotUtils.HUBSPOT_ACCOUNT_SYNC_JOB, accountId)
        accountId = account.id as String

        then:
        accountId
        account.name == SUBSKRIBE_ALTERNATE_COMPANY_NAME
    }

    def "verify opportunities by company crm id"() {
        when:
        // create company on hubspot
        def company = HubSpotUtils.createCompany(accessToken, [name: "Sports Club, LLC."])
        assert company.id
        def companyId = company.id as String
        companyIds.add(companyId)
        sleep(15000)

        // create deal 1
        def deal_1 = HubSpotUtils.createDeal(accessToken, companyId)
        assert deal_1.id
        dealIds.add(deal_1.id as String)
        sleep(15000)

        // create deal 2
        def deal_2 = HubSpotUtils.createDeal(accessToken, companyId)
        assert deal_2.id
        dealIds.add(deal_2.id as String)
        sleep(15000)

        // create deal 3 - closedwon
        def deal_3 = HubSpotUtils.createDeal(accessToken, companyId, [ dealstage: "closedwon" ])
        assert deal_3.id
        dealIds.add(deal_3.id as String)
        sleep(15000)

        // create deal 4 - closedlost
        def deal_4 = HubSpotUtils.createDeal(accessToken, companyId, [ dealstage: "closedlost" ])
        assert deal_4.id
        dealIds.add(deal_4.id as String)
        sleep(15000)

        // sync account from hubspot to subskribe
        Map account = HubSpotUtils.syncCompanyToSubskribe(client, company.id as String)
        HubSpotUtils.waitForTenantJob(client, HubSpotUtils.HUBSPOT_ACCOUNT_SYNC_JOB, accountId)
        assert account.id
        sleep(15000)

        // call opportunities by account crm id
        List<Map> deals
        for (int i = 0; i < 5; i++) {
            deals = getOpportunitiesByHubSpotCompanyId(client, companyId)
            sleep(15000)
            if (deals.size() == 3) break
        }

        then:
        // this will not include the closedlost deal
        deals.size() == 3
        deals.each {
            assert it.name == "Test Deal"
            assert it.type == "newbusiness"
            assert it.stage == "appointmentscheduled" || it.stage == "closedwon"
            assert it.accountId == account.id
            assert it.type == "newbusiness"
            assert it.opportunityCrmType == "HUBSPOT"
            assert dealIds.contains(it.crmId)
        }
    }

    def "Get contacts from HubSpot"() {
        when:
        HttpResponse getContactsResponse = getContactsByCompanyId(client, HUBSPOT_COMPANY_ID)
        sleep(15000)

        then:
        getContactsResponse.status == 200
        getContactsResponse.body.size() == 11
        getContactsResponse.body.any {
            it.firstname == "Brian"
            it.lastname == "Halligan"
            it.email == "<EMAIL>"
        }
    }

    def "Get contacts from HubSpot using merged company ID should get the same contacts"() {
        when:
        HttpResponse getContactsResponse = getContactsByCompanyId(client, HUBSPOT_MERGED_COMPANY_ID_1)
        sleep(15000)

        then:
        getContactsResponse.status == 200
        getContactsResponse.body.size() == 11
        getContactsResponse.body.any {
            it.firstname == "Brian"
            it.lastname == "Halligan"
            it.email == "<EMAIL>"
        }
    }

    def "create and sync company"() {
        when:
        company = HubSpotUtils.createCompany(accessToken)
        companyIds.add(company.id as String)

        def account = HubSpotUtils.syncCompanyToSubskribe(client, company.id as String)
        accountId = account.id as String

        HubSpotUtils.waitForTenantJob(client, HubSpotUtils.HUBSPOT_ACCOUNT_SYNC_JOB, accountId)
        Map updatedCompany = HubSpotUtils.getCompany(accessToken, company.id as String)

        // Sleep to reduce rate-limiting
        sleep(15000)

        then:
        company.id != null
        account.id != null

        account.name == HubSpotUtils.COMPANY_NAME
        updatedCompany?.properties?.subskribe_account ==~ /^http.*\/accounts\/${accountId}$/
    }

    def "create contact"() {
        when:
        Map contact = ContactSpec.createAndGetContact(client, accountId as String).body
        contactId = contact?.id

        then:
        contactId
    }

    def "create plans"() {
        when:
        Map plan1 = PlanSpec.createPlanWithRecurringCharge(client)
        Map plan2 = PlanSpec.createPlanWithOneTimeCharge(client)
        plans.addAll(plan1, plan2)

        then:
        plan1.id
        plan2.id
        plans.size() == 2
    }

    def "create deal"() {
        when:
        deal = HubSpotUtils.createDeal(accessToken, company.id as String)
        sleep(15000)

        then:
        deal != null
        dealIds.add(deal.id as String)
    }

    def "create and sync new order - candidate 1"() {
        when:
        Map order = HubSpotUtils.getOrderMapForDeal(deal, accountId, contactId, START_DATE)
        Map createdOrder = OrderSpec.createOrderForPlans(client, order, plans, 10)
        assert createdOrder.id
        def orderId = createdOrder.id as String
        orders.add(createdOrder)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        HubSpotUtils.waitForTenantJob(client, HUBSPOT_ORDER_SYNC_JOB, orderId)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, deal.id as String, orders[0], "105.00")
    }

    def "create and sync new order - candidate 2 - does not update"() {
        when:
        Map order = HubSpotUtils.getOrderMapForDeal(deal, accountId, contactId, START_DATE)
        Map createdOrder = OrderSpec.createOrderForPlans(client, order, plans, 20)
        assert createdOrder.id
        def orderId = createdOrder.id as String
        orders.add(createdOrder)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        HubSpotUtils.waitForTenantJob(client, HUBSPOT_ORDER_SYNC_JOB, orderId)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, deal.id as String, orders[0], "105.00")
    }

    def "create and sync new order - candidate 3 - does not update"() {
        when:
        Map order = HubSpotUtils.getOrderMapForDeal(deal, accountId, contactId, START_DATE)
        Map createdOrder = OrderSpec.createOrderForPlans(client, order, plans, 30)
        assert createdOrder.id
        def orderId = createdOrder.id as String
        orders.add(createdOrder)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        HubSpotUtils.waitForTenantJob(client, HUBSPOT_ORDER_SYNC_JOB, orderId)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, deal.id as String, orders[0], "105.00")
    }

    def "delete order 1 - makes 3 as primary"() {
        when:
        def order1_id = orders[0].id as String
        def order3_id = orders[2].id as String
        OrderSpec.deleteOrder(client, order1_id)
        HubSpotUtils.waitForTenantJob(client, CRM_ORDER_DELETION_SYNC_JOB, deal.id as String)
        HubSpotUtils.updateHubSpotForOrder(client, order3_id)
        HubSpotUtils.waitForTenantJob(client, HUBSPOT_ORDER_SYNC_JOB, order3_id)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, deal.id as String, orders[2], "315.00")
    }

    def "delete order 3 - makes 2 as primary"() {
        when:
        def order2_id = orders[1].id as String
        def order3_id = orders[2].id as String
        OrderSpec.deleteOrder(client, order3_id)
        HubSpotUtils.waitForTenantJob(client, CRM_ORDER_DELETION_SYNC_JOB, deal.id as String)
        HubSpotUtils.updateHubSpotForOrder(client, order2_id)
        HubSpotUtils.waitForTenantJob(client, HUBSPOT_ORDER_SYNC_JOB, order2_id)
        sleep(15000)

        then:
        HubSpotUtils.verifyDealObject(accessToken, deal.id as String, orders[1], "210.00")
    }

    def "fetch and verify order object"() {
        when:
        def order2_id = orders[1].id as String
        Map hubspotOrder = HubSpotUtils.fetchOrderObject(accessToken, order2_id)
        sleep(15000)

        then:
        HubSpotUtils.verifyOrderObject(hubspotOrder, order2_id, "560.00", "210.00")
    }

    def "delete order 2 - deal properties should blank out"() {
        when:
        def order2_id = orders[1].id as String
        OrderSpec.deleteOrder(client, order2_id)
        HubSpotUtils.waitForTenantJob(client, CRM_ORDER_DELETION_SYNC_JOB, deal.id as String)
        sleep(15000)

        then:
        HubSpotUtils.verifyBlankDealObject(accessToken, deal.id as String)
    }

    def "verify that order object is deleted"() {
        when:
        def order2_id = orders[1].id as String
        OrderSpec.deleteOrder(client, order2_id)
        HubSpotUtils.waitForTenantJob(client, CRM_ORDER_DELETION_SYNC_JOB, deal.id as String)

        List<String> orderObjectIds
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            orderObjectIds = HubSpotUtils.getHubSpotOrderIdsForDeal(accessToken, deal.id as String)
            if (orderObjectIds.size() == 0) {
                break
            }
        }

        then:
        orderObjectIds.size() == 0
    }

    def "FX: Enable FX feature flag"() {
        when:
        def flagEnabled = FeatureFlag.updateFlag(client, FEATURE_NAME, true)
        def txFlagDisabled = FeatureFlag.updateFlag(client, TX_FEATURE_NAME, false)
        TenantSettingRestAndGqlSpec.updateTenantSettingSeal(client, "OFF")
        HttpResponse supportedCurrenciesResponse = SupportedCurrenciesSpec.addAllSupportedCurrencies(client)
        TenantSettingRestAndGqlSpec.updateTenantSettingSeal(client, "ON")

        then:
        flagEnabled
        txFlagDisabled
        supportedCurrenciesResponse.status == 200
    }

    def "FX: Set up currency type setting to leverage currency conversion"() {
        when:
        Map currencyTypeSettingQuery = CurrencyTypeSettingGqlSpec.getCurrencyTypeSettingQuery()
        HttpResponse getOrCreateCurrencyTypeSettingResponse = client.post(GQL_PATH, currencyTypeSettingQuery)
        Map currencyTypeSetting = getOrCreateCurrencyTypeSettingResponse.body.data.currencyTypeSetting
        currencyTypeSetting.planCurrencySettingType = new EnumField(CurrencyTypeSettingGqlSpec.CURRENCY_TYPE_SETTING_LEVERAGE_CURRENCY_CONVERSION)
        currencyTypeSetting.catalogCurrency = "USD"
        String currencyTypeSettingMutation = GqlUtils.mutation(
                "upsertCurrencyTypeSetting",
                [currencyTypeSetting: currencyTypeSetting],
                CurrencyTypeSettingGqlSpec.getCurrencyTypeSettingFields()
                )
        HttpResponse updateCurrencyTypeSettingResponse = client.post(GQL_PATH, [query: currencyTypeSettingMutation])
        Map currencyTypeSettingUpdated = updateCurrencyTypeSettingResponse.body.data.upsertCurrencyTypeSetting

        then:
        getOrCreateCurrencyTypeSettingResponse.status == 200
        currencyTypeSetting != null
        updateCurrencyTypeSettingResponse.status == 200
        currencyTypeSettingUpdated != null
    }

    def "FX: Create account with AUD currency"() {
        when:
        Map upsertAccountRequest = AccountGqlSpec.upsertAccountGqlMutation(null, null, null, false, null, false, false, null, "AUD")
        HttpResponse upsertAccountResponse = client.post(GQL_PATH, upsertAccountRequest)
        account = upsertAccountResponse.body.data.upsertAccount
        accountId = account.id
        def accountCurrency = upsertAccountResponse.body.data.upsertAccount.currency


        then:
        upsertAccountResponse.status == 200
        accountId
        accountCurrency == "AUD"
    }

    def "FX: Create contact"() {
        when:
        Map contact = ContactSpec.createAndGetContact(client, account.id as String).body
        contactId = contact?.id

        then:
        contactId
    }

    def "FX: Create company and update Subskribe account"() {
        when:
        def company = HubSpotUtils.createCompany(accessToken, [name: "FX Company"])
        sleep(15000)
        companyId = company.id as String
        companyIds.add(companyId)

        HttpResponse updateResponse = updateAccount(client, account + [crmId: companyId])
        Map updatedAccount = getCustomerAccount(client, account.id as String).body

        then:
        updateResponse.status == 200
        updatedAccount.crmId == companyId

    }

    def "FX: Create deal"() {
        when:
        deal = HubSpotUtils.createDeal(accessToken, company.id as String)
        sleep(15000)
        dealId = deal.id as String
        dealIds.add(dealId)

        then:
        dealId != null
    }

    def "FX: Create plan"() {
        when:
        recurringPlan = PlanSpec.createPlanWithRecurringCharge(client, [:], [amount: 19444.4375])
        def planWithOTCharge = PlanSpec.createPlanWithOneTimeCharge(client, [:], [amount: 15555.55])
        def planWithPrepaidCharge = PlanSpec.createPlanWithPrepaidCharge(client, [amount: 13333.55])
        def planWithUsageCharge = PlanSpec.createPlanWithUsageCharge(client, new BigDecimal("9999.75"), [:])

        then:
        recurringPlan
        recurringPlan.currency == "USD"
        planWithOTCharge
        planWithOTCharge.currency == "USD"
        planWithPrepaidCharge
        planWithPrepaidCharge.currency == "USD"
        planWithUsageCharge
        planWithUsageCharge.currency == "USD"
    }

    def "FX: create a currency conversion rate for currency pair USD->AUD"() {
        when:
        Map currencyConversionRateRequestPayload = CurrencyConversionRateGqlSpec.currencyConversionRate("USD", "AUD", 1.51, Instant.now().getEpochSecond(), "UP", BigDecimal.valueOf(1000))
        String currencyConversionRateMutation = GqlUtils.mutation(
                "upsertCurrencyConversionRate",
                [currencyConversionRate: currencyConversionRateRequestPayload],
                CurrencyConversionRateGqlSpec.getCurrencyConversionRateFields()
                )
        HttpResponse upsertCurrencyConversionRateResponse = client.post(GQL_PATH, [query: currencyConversionRateMutation])
        Map currencyConversionRate = upsertCurrencyConversionRateResponse.body.data.upsertCurrencyConversionRate
        currencyConversionRateId = currencyConversionRate.id

        then:
        currencyConversionRateRequestPayload
        currencyConversionRate
    }

    def "FX: Create and sync order with FX rate"() {
        when:
        Map order = HubSpotUtils.getOrderMapForDeal(deal, account.id as String, contactId, START_DATE)

        Long orderStartDate = Instant.now().epochSecond
        Long orderEndDate = DateTimeHelper.plusMonths(orderStartDate, 12, DateTimeHelper.DEFAULT_TZ)

        Map createdOrder = OrderSpec.createOrderForPlans(client, order + [
            startDate: orderStartDate,
            endDate: orderEndDate
        ], List.of(recurringPlan), 10)
        assert createdOrder.id
        def orderId = createdOrder.id as String
        orders.add(createdOrder)
        HubSpotUtils.updateHubSpotForOrder(client, orderId)
        sleep(15000)

        then:
        true
    }

    def "FX: Verify FX on order lines"() {
        when:

        // Get HubSpot orders
        List<String> orderObjectIds = []
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            orderObjectIds.addAll(HubSpotUtils.getHubSpotOrderIdsForDeal(accessToken, dealId))
            if (orderObjectIds.size() >= 1) {
                break
            }
        }
        assert orderObjectIds.size() >= 1

        // Get HubSpot order lines
        List<String> orderLineObjectIds = []
        for (int i = 0; i < 10; i++) {
            sleep(15000)
            orderLineObjectIds.addAll(HubSpotUtils.getAssociatedOrderLineIds(accessToken, orderObjectIds[0]))
            if (orderLineObjectIds?.size() >= 1) break
        }
        List<Map> orderLines = orderLineObjectIds.collect {HubSpotUtils.getOrderLine(accessToken, it) }

        Map currencyConversionRate = CurrencyConversionRateGqlSpec.getCurrencyConversionRateById(client, currencyConversionRateId)

        then:
        orderLines[0].properties.subskribe_line_item_fx_rate == currencyConversionRate.conversionRate.toString()
    }

    def "Clean up records" () {
        HubSpotUtils.deleteRecords(accessToken, HubSpotUtils.DEAL_QUALIFIED_NAME, dealIds)
        HubSpotUtils.deleteRecords(accessToken, HubSpotUtils.COMPANY_QUALIFIED_NAME, companyIds)
    }

    static HttpResponse getContactsByCompanyId(HttpClient client, String companyId) {
        return client.get(String.format(GET_CONTACTS_BY_COMPANY_ID_PATH_FORMAT, companyId))
    }

    static List<Map> getOpportunitiesByHubSpotCompanyId(HttpClient client, String accountCrmId) {
        String fields = /{name
                id
                type
                stage
                crmId
                accountId
                createdOn
                updatedOn
                opportunityCrmType
                isClosed
                primaryOrderId
                opportunityId
                currency
                customFields {
                    id
                    name
                    label
                    type
                    value
                    source
                    options
                    required
                    selections
                    defaultValue {
                        value
                        selections
                    }
                }}/


        var queryString = GqlUtils.query(
                "opportunitiesFromCrm",
                [ accountCrmId: accountCrmId ],
                fields
                )
        HttpResponse response = client.post(GQL_PATH, [query: queryString ])
        return response.body?.data?.opportunitiesFromCrm
    }

    static def updateHubSpotConfiguration(HttpClient client, String tenantId, String alternateCompanyNameSourceField) {
        return client.put(String.format(HUBSPOT_CONFIGURATION_PATH_FORMAT, tenantId), [
            alternateCompanyNameSourceField: alternateCompanyNameSourceField
        ])
    }

    private static def createAmendmentWithOpptyMutation(String subscriptionId, String contactId, String planId, String chargeId, String subscriptionChargeId, Long effectiveDate, String lineAction, int quantity, String opportunityId, boolean isDryRun = false, String ownerId = "USER-ADMIN", String purchaseOrderNumber = null, boolean purchaseOrderRequiredForInvoicing = false) {
        Map arguments = [
            order   : [
                orderType                        : new EnumType("AMENDMENT"),
                subscriptionId                   : subscriptionId,
                shippingContactId                : contactId,
                billingContactId                 : contactId,
                lineItems                        : [
                    [
                        action              : new EnumType(lineAction),
                        subscriptionChargeId: subscriptionChargeId,
                        planId              : planId,
                        chargeId            : chargeId,
                        quantity            : quantity,
                    ],
                ],
                startDate                        : effectiveDate,
                ownerId                          : ownerId,
                sfdcOpportunityId                : opportunityId,
                sfdcOpportunityName              : "Test Opportunity",
                purchaseOrderNumber              : purchaseOrderNumber,
                purchaseOrderRequiredForInvoicing: purchaseOrderRequiredForInvoicing,
            ],
            isDryRun: isDryRun
        ]
        String fields = /{
            ${OrderQueries.orderDetailFragment()}
        }/
        return [query: GqlUtils.mutation("upsertAmendment", arguments, fields)]
    }

    static HttpResponse getObjectRecords(HttpClient client, String objectType, List<String> objectIds) {
        String objectIdsParam = String.join(",", objectIds)
        return client.get(String.format(GET_OBJECT_RECORDS_PATH_FORMAT, objectType, objectIdsParam))
    }

    static HttpResponse getHubSpotOrdersByDealId(HttpClient client, String dealId) {
        return client.get(String.format(GET_HUBSPOT_ORDERS_BY_DEAL_ID_PATH_FORMAT, dealId))
    }

    static HttpResponse syncHubspotSubscriptionStatus(HttpClient client, String subscriptionId) {
        return client.post(String.format(SYNC_HUBSPOT_SUBSCRIPTION_STATUS_PATH_FORMAT, subscriptionId), [:])
    }

    static HttpResponse updateAccount(HttpClient client, Map accountJson) {
        return client.put("${ACCOUNT_PATH}/${accountJson.id}", accountJson)
    }

    static HttpResponse getCustomerAccount(HttpClient client, String accountId) {
        return client.get("${ACCOUNT_PATH}/${accountId}")
    }
}
