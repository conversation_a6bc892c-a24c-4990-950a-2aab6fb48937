package com.subskribe.billy_e2e.productcatalog

import static com.subskribe.billy_e2e.productcatalog.charge.ChargeSpec.CHARGE_PATH

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.StringUtils
import com.subskribe.billy_e2e.UnitOfMeasureSpec
import com.subskribe.billy_e2e.customfield.BaseCustomFieldDefinitionSpec
import com.subskribe.billy_e2e.customfield.CustomFieldDefinitionSpec
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.productcatalog.charge.ChargeSpec
import com.subskribe.billy_e2e.settings.TenantSettingRestAndGqlSpec
import com.subskribe.billy_e2e.tax.TaxRateSpec
import com.subskribe.billy_e2e.template.PredefinedTermsSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.EventControl
import com.subskribe.billy_e2e.utils.ObjectTypes
import com.subskribe.billy_e2e.utils.Recurrence
import org.apache.commons.lang3.RandomStringUtils
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class PlanSpec extends Authenticated {

    static final String PLAN_PATH = "/plans"

    private static final String PLAN_EXTERNAL_ID = RandomStringUtils.randomAlphanumeric(10).toUpperCase()

    @Shared
    private String productId

    @Shared
    private static String planId

    @Shared
    private static String chargeId

    def setupSpec() {
        updateTenantConfig(client)
        HttpResponse response = ProductSpec.createProduct(client)
        productId = response.locationId
    }

    def "create plan with long description should fail validation"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [])
        planJson.status = "DRAFT"
        planJson.description = StringUtils.getRandomString(1001)
        HttpResponse response = client.post(PLAN_PATH, planJson)

        then:
        response.status == 400
    }

    def "create DRAFT plan with no charges"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [])
        planJson.status = "DRAFT"
        HttpResponse response = client.post(PLAN_PATH, planJson)
        planId = response.locationId

        then:
        response.status == 201
    }

    def "create plan with duplicate external id fails as expected"() {
        when:
        String externalId = StringUtils.getRandomString(10)
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [])
        planJson.externalId = externalId
        planJson.status = "DRAFT"
        HttpResponse response = client.post(PLAN_PATH, planJson)

        Map anotherPlanJson = PlanJsonBuilder.getPlanJson(productId, [])
        anotherPlanJson.name = StringUtils.getRandomString(10)
        anotherPlanJson.externalId = externalId
        anotherPlanJson.status = "DRAFT"
        HttpResponse anotherPlanResponse = client.post(PLAN_PATH, anotherPlanJson)

        Map badPlanInput = PlanJsonBuilder.getPlanJson(productId, [])
        badPlanInput.externalId = "pipe-|-is-not-allowed"
        badPlanInput.status = "DRAFT"
        HttpResponse badPlanInputResponse = client.post(PLAN_PATH, badPlanInput)
        then:
        response.status == 201
        anotherPlanResponse.status == 409
        anotherPlanResponse.error.contains("external Id: ${externalId} already exists")

        badPlanInputResponse.status == 400
        badPlanInputResponse.error.contains("not allowed for this identifier")
    }

    def "add charge to DRAFT plan"() {
        when:
        Map chargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        HttpResponse response = client.post(getChargesPath(planId), chargeJson)
        chargeId = response.locationId

        then:
        response.status == 201
    }

    def "get all active plans shouldn't return draft plans"() {
        when:
        HttpResponse getActivePlansResponse = client.get("${PLAN_PATH}?status=ACTIVE")
        HttpResponse getAllPlansResponse = client.get(PLAN_PATH)

        then:
        getActivePlansResponse.status == 200
        getAllPlansResponse.status == 200
        getAllPlansResponse.body.data.size() > 0
        getActivePlansResponse.body.data.every { plan -> plan.status == "ACTIVE" }
        getActivePlansResponse.body.data.every { plan -> plan.displayName == "plan display name" }
    }

    def "add charge with too many decimals in amount fails"() {
        when:
        Map chargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        chargeJson.amount = new BigDecimal("1.000005")
        HttpResponse response = client.post(getChargesPath(planId), chargeJson)

        then:
        response.status == 400
    }

    def "update charge amount with trailing zeros while in draft"() {
        when:
        String chargePath = getChargePath(planId, chargeId)
        HttpResponse response = client.get(chargePath)
        Map chargeJson = response.body
        BigDecimal updatedAmount = (chargeJson.amount as BigDecimal).add(new BigDecimal("1.0000500"))
        chargeJson.amount = updatedAmount

        HttpResponse updateChargeResponse = client.put(chargePath, chargeJson)
        HttpResponse getChargeResponse = client.get(chargePath)

        then:
        updateChargeResponse.status == 200
        getChargeResponse.status == 200
        getChargeResponse.body.amount == updatedAmount
    }

    def "add and delete charge in DRAFT plan"() {
        when:
        Map chargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        HttpResponse addChargeResponse = client.post(getChargesPath(planId), chargeJson)
        String addedChargeId = addChargeResponse.locationId

        String chargePath = getChargePath(planId, addedChargeId)
        HttpResponse deleteChargeResponse = client.delete(chargePath)

        then:
        addChargeResponse.status == 201
        !StringUtils.isBlank(addedChargeId)
        deleteChargeResponse.status == 200
        deleteChargeResponse.body.id == addedChargeId
    }

    def "activate plan"() {
        when:
        HttpResponse activateResponse = client.put("${getPlanUrl(planId)}/activate", [:])

        then:
        activateResponse.status == 200
    }

    def "create another plan with external id"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId,null, [:])
        planJson.status = "DRAFT"
        planJson.externalId = PLAN_EXTERNAL_ID
        planJson.name = RandomStringUtils.randomAlphanumeric(5).toUpperCase()
        HttpResponse response = client.post(PLAN_PATH, planJson)

        then:
        response.status == 201
    }

    def "update plan metadata with duplicate id"() {
        when:
        String path = getPlanUrl(planId)
        Map planJson = getPlan(planId)
        planJson.externalId = PLAN_EXTERNAL_ID
        planJson.description = "updated description"
        HttpResponse updateResponse = client.put(path, planJson)
        then:
        updateResponse.status == 409
        updateResponse.error.contains("already exists")
    }

    def "get active plan"() {
        when:
        HttpResponse getActivePlansResponse = client.get("${PLAN_PATH}?status=ACTIVE")

        then:
        getActivePlansResponse.status == 200
        getActivePlansResponse.body.data.size() > 0
        getActivePlansResponse.body.data.every { plan -> plan.status == "ACTIVE" }
    }

    def "update charge in active plan should fail"() {
        when:
        String chargePath = getChargePath(planId, chargeId)
        HttpResponse response = client.get(chargePath)
        Map chargeJson = response.body
        BigDecimal updatedAmount = (chargeJson.amount as BigDecimal).add(BigDecimal.ONE)
        chargeJson.amount = updatedAmount

        HttpResponse updateChargeResponse = client.put(chargePath, chargeJson)

        then:
        updateChargeResponse.status == 400
    }

    def "update charge partial data in active plan"() {
        when:
        String chargePath = getChargePath(planId, chargeId)
        HttpResponse response = client.get(chargePath)
        Map chargeJson = response.body

        Map updateJson = PlanJsonBuilder.getChargePartialUpdateJson(chargeJson)
        updateJson.name = "updated name"
        updateJson.minQuantity = 2
        updateJson.defaultQuantity = 5
        updateJson.maxQuantity = 50

        HttpResponse updateChargeResponse = client.put("${chargePath}/partial", updateJson)
        def events = EventControl.getEvents(client, "CHARGE_UPDATED")

        then:
        updateChargeResponse.status == 200
        updateChargeResponse.body.name == updateJson.name
        updateChargeResponse.body.minQuantity == updateJson.minQuantity
        updateChargeResponse.body.defaultQuantity == updateJson.defaultQuantity
        updateChargeResponse.body.maxQuantity == updateJson.maxQuantity
        events.last().payload.id == chargeId
        events.last().payload.name == updateJson.name
    }

    def "delete charge in active plan should fail"() {
        when:
        String chargePath = getChargePath(planId, chargeId)
        HttpResponse response = client.delete(chargePath)

        then:
        response.status == 400
    }

    def "create ACTIVE plan with no charges"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [])
        HttpResponse updateResponse = client.post(PLAN_PATH, planJson)

        planJson.status = "DRAFT"
        String draftPlanId = client.post(PLAN_PATH, planJson).locationId
        HttpResponse activateResponse = activatePlan(client, draftPlanId)

        then:
        updateResponse.status == 400
        activateResponse.status == 400
    }

    def "get created plan"() {
        when:
        HttpResponse response = client.get(getPlanUrl(planId))

        then:
        response.status == 200
        response.body.productId == productId
        response.body.updatedOn != null
    }

    def "add plan with charge with invalid price tier should fail"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getInvalidVolumeChargeModel()
        ])
        HttpResponse addPlanResponse = client.post(PLAN_PATH, planJson)

        then:
        addPlanResponse.status == 400
    }

    def "add plan with mixed cycle charges should succeed"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getRecurringPerUnitChargeJson([recurrence: Recurrence.ONE_YEAR_CYCLE]),
            PlanJsonBuilder.getRecurringPerUnitChargeJson([recurrence: Recurrence.ONE_MONTH_CYCLE])
        ])
        HttpResponse addPlanResponse = client.post(PLAN_PATH, planJson)

        then:
        addPlanResponse.status == 201
    }

    def "add plan with charge with invalid recurrence should fail"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getChargeWithInvalidRecurrence()
        ])
        HttpResponse addPlanResponse = client.post(PLAN_PATH, planJson)

        then:
        addPlanResponse.status == 400
    }

    def "add and retrieve plan with OTC"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJsonWithOTC(productId)
        HttpResponse addPlanResponse = client.post(PLAN_PATH, planJson)
        Map addedPlan = client.get("${PLAN_PATH}/${addPlanResponse.locationId}").body

        then:
        addPlanResponse.status == 201
        addedPlan.charges.get(0).type == "ONE_TIME"
        addedPlan.charges.get(0).chargeModel == "PER_UNIT"
    }

    def "create plan with block price"() {
        when:
        Map plan = createPlanWithBlockPriceCharge(client)

        then:
        plan.charges.size() == 1
        plan.charges.get(0).chargeModel == "BLOCK"
    }

    def "flat fee and usage are incompatible"() {
        when:
        Map charge = PlanJsonBuilder.getFlatFeeChargeJson([type: "USAGE"])
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [charge])
        HttpResponse response = client.post(PLAN_PATH, planJson)

        then:
        response.status == 400
    }

    def "adding yearly usage charge fails as expected"() {
        when:
        Map yearlyUsageCharge = PlanJsonBuilder.getUsagePerUnitChargeJson([recurrence: Recurrence.ONE_YEAR_CYCLE])

        Map planJson = PlanJsonBuilder.getPlanJson(productId, [yearlyUsageCharge])
        HttpResponse addResponse = client.post(PLAN_PATH, planJson)

        then:
        addResponse.status == 400
        addResponse.error.contains("USAGE charge recurrence must be one of [MONTH, QUARTER], but was YEAR")
    }

    def "add drawdown charge without prepaid charge"() {
        when:
        Map drawdownCharge = PlanJsonBuilder.getUsagePerUnitChargeJson()
        drawdownCharge.isDrawdown = true

        Map planJson = PlanJsonBuilder.getPlanJson(productId, [drawdownCharge])
        HttpResponse addResponse = client.post(PLAN_PATH, planJson)

        then:
        addResponse.status == 400
    }

    def "add plan with multiple drawdown charges"() {
        when:
        Map drawdownCharge1 = PlanJsonBuilder.getUsagePerUnitChargeJson()
        drawdownCharge1.isDrawdown = true

        Map drawdownCharge2 = PlanJsonBuilder.getUsagePerUnitChargeJson()
        drawdownCharge2.isDrawdown = true

        Map prepaidCharge = PlanJsonBuilder.getPrepaidPerUnitChargeJson()

        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            drawdownCharge1,
            drawdownCharge2,
            prepaidCharge
        ])
        HttpResponse addResponse = client.post(PLAN_PATH, planJson)

        then:
        addResponse.status == 400
    }

    def "add plan with prepaid and drawdown charges"() {
        when:
        Map drawdownCharge = PlanJsonBuilder.getUsagePerUnitChargeJson()
        drawdownCharge.isDrawdown = true

        Map prepaidCharge = PlanJsonBuilder.getPrepaidPerUnitChargeJson()

        Map planJson = PlanJsonBuilder.getPlanJson(productId, [drawdownCharge, prepaidCharge])
        HttpResponse addResponse = client.post(PLAN_PATH, planJson)
        HttpResponse getResponse = client.get(getPlanUrl(addResponse.locationId))

        then:
        addResponse.status == 201
        getResponse.status == 200
        getResponse.body.charges.any { it.isDrawdown }
    }

    def "recurring charge with drawdown is invalid"() {
        when:
        Map charge = PlanJsonBuilder.getRecurringPerUnitChargeJson([recurrence: Recurrence.ONE_MONTH_CYCLE])
        charge.isDrawdown = true
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [charge])
        HttpResponse response = client.post(PLAN_PATH, planJson)

        then:
        response.status == 400
    }

    def "add plan without product should fail"() {
        when:
        def emptyProductResponse = createPlanWithTaxRateId(client, null)
        def invalidProductResponse = createPlanWithTaxRateId(client, "fake product id")

        then:
        emptyProductResponse.status == 422
        invalidProductResponse.status == 404
        invalidProductResponse.error.contains("product id does not exist")
    }

    def "update active plan metadata"() {
        when:
        String planId = createPlanWithRecurringCharge(client).id
        def planJson = getPlan(client, planId)
        planJson.name = "updated plan name"
        def charge = planJson.charges.get(0)
        charge.name = "updated charge name"
        def updateResponse = updatePlan(planId, planJson)
        def plan = getPlan(client, planId)

        then:
        updateResponse.status == 200
        plan.name == planJson.name
        plan.status == planJson.status && plan.status == 'ACTIVE'
        plan.charges.size() == 1
        plan.charges.get(0).name == charge.name
    }

    def "update unused plan's currency should fail if it is not supported by tenant"() {
        when:
        def planId = createProductAndPlan(client).locationId
        String path = "${PLAN_PATH}/${planId}"
        def planJson = client.get(path).body
        planJson.name = "updated plan name"
        def charge = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        charge.name = "updated charge name"
        planJson.charges = [charge]
        planJson.currency = TenantSettingRestAndGqlSpec.UNSUPPORTED_CURRENCY
        def updateResponse = client.put(path, planJson)
        client.get(path).body

        then:
        updateResponse.status == 400
    }

    def "update unused plan's currency should succeed if it is supported by tenant"() {
        when:
        String productId = ProductSpec.createProduct(client).locationId
        String planId = createPlanWithTaxRateId(client, productId, "DRAFT").locationId
        String path = "${PLAN_PATH}/${planId}"
        def planJson = client.get(path).body
        planJson.name = "updated plan name"
        def charge = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        charge.name = "updated charge name"
        planJson.charges = [charge]
        planJson.currency = TenantSettingRestAndGqlSpec.USED_CURRENCY

        def updateResponse = client.put(path, planJson)
        def plan = client.get(path).body

        then:
        updateResponse.status == 200
        plan.currency == TenantSettingRestAndGqlSpec.USED_CURRENCY
    }

    def "update plan's currency fails if active"() {
        when:
        String productId = ProductSpec.createProduct(client).locationId
        String planId = createPlanWithTaxRateId(client, productId, "ACTIVE").locationId
        String path = "${PLAN_PATH}/${planId}"
        def planJson = client.get(path).body
        planJson.name = "updated plan name"
        def charge = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        charge.name = "updated charge name"
        planJson.charges = [charge]
        planJson.currency = TenantSettingRestAndGqlSpec.USED_CURRENCY

        def updateResponse = client.put(path, planJson)
        def plan = client.get(path).body

        then:
        updateResponse.status == 400
        updateResponse.error.contains("Plan's currency cannot be updated")
    }

    def "delete unused plan"() {
        when:
        def planId = createProductAndPlan(client).locationId
        String path = "${PLAN_PATH}/${planId}"
        def deleteResponse = client.delete(path)
        def getResponse = client.get(path)

        then:
        getResponse.status == 404
        deleteResponse.status == 200
        deleteResponse.body.id == planId
        deleteResponse.body.charges.size() == 3
    }

    def "delete used plan should fail"() {
        when:
        def order = OrderSpec.createAndGetOrder(client)
        String planId = order.lineItems.get(0).planId
        String path = "${PLAN_PATH}/${planId}"
        def getResponse = client.get(path)
        def deleteResponse = client.delete(path)

        then:
        getResponse.status == 200
        deleteResponse.status == 409
    }

    def "add and get new plan with UOM"() {
        when:
        def addUOMResponse = UnitOfMeasureSpec.addUOM(client, UnitOfMeasureSpec.ACTIVE_STATUS)
        def chargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        def unitOfMeasureId = addUOMResponse.locationId
        chargeJson.unitOfMeasureId = unitOfMeasureId

        def planJson = PlanJsonBuilder.getPlanJson(productId, [chargeJson])

        def addPlanResponse = client.post(PLAN_PATH, planJson)
        def planId = addPlanResponse.locationId
        def getPlanResponse = client.get("${PLAN_PATH}/${planId}")

        then:
        addPlanResponse.status == 201
        getPlanResponse.body.charges.get(0).unitOfMeasureId == unitOfMeasureId
    }

    def "add new plan with DRAFT UOM"() {
        when:
        def addUOMResponse = UnitOfMeasureSpec.addUOM(client, UnitOfMeasureSpec.DRAFT_STATUS)
        def chargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson()
        def unitOfMeasureId = addUOMResponse.locationId
        chargeJson.unitOfMeasureId = unitOfMeasureId

        def planJson = PlanJsonBuilder.getPlanJson(productId, [chargeJson])

        def addPlanResponse = client.post(PLAN_PATH, planJson)

        then:
        addPlanResponse.status == 400
        addPlanResponse.error.length() > 0
    }

    def "update used plan and charges"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client, Recurrence.ONE_YEAR_CYCLE)
        Map recurringPlan = createPlanWithRecurringCharge(client)
        planId = order.lineItems.get(0).planId

        String path = getPlanUrl(planId)
        Map planJson = getPlan(planId)
        planJson.name = "updated plan name"
        planJson.description = "updated description"

        def addUOMResponse = UnitOfMeasureSpec.addUOM(client, UnitOfMeasureSpec.ACTIVE_STATUS)

        planJson.charges.get(0).name = "updated charge name"
        planJson.charges.get(0).unitOfMeasureId = addUOMResponse.locationId

        HttpResponse updateResponse = client.put(path, planJson)
        Map updatedPlan = getPlan(planId)
        Map storedRecurringPlan = getPlan(recurringPlan.id as String)

        planJson.currency = TenantSettingRestAndGqlSpec.USED_CURRENCY
        HttpResponse updateCurrencyResponse = client.put(path, planJson)

        then:
        updateResponse.status == 200
        updateCurrencyResponse.status == 400

        updatedPlan.name == planJson.name
        updatedPlan.description == planJson.description
        updatedPlan.charges.get(0).name == planJson.charges.get(0).name
        updatedPlan.charges.get(0).unitOfMeasureId == addUOMResponse.locationId
        storedRecurringPlan.name == recurringPlan.name
    }

    def "update plan: adding a new charge should not mutate existing charge id"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJsonWithOTC(productId, [status: "DRAFT"])
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)

        String savedChargeId = savedPlan.charges[0].id
        Map newCharge = PlanJsonBuilder.getRecurringPerUnitChargeJson([recurrence: Recurrence.ONE_MONTH_CYCLE])
        savedPlan.charges.add(newCharge)
        HttpResponse updateResponse = updatePlan(planId, savedPlan)
        Map updatedPlan = getPlan(planId)

        then:
        updateResponse.status == 200

        updatedPlan.charges.size() == 2
        updatedPlan.charges*.id.contains(savedChargeId)
    }

    def "update plan: updating a charge should not mutate existing charge id"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJsonWithRecurringCharge(productId)
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)
        String savedChargeId = savedPlan.charges[0].id
        savedPlan.charges[0].name = "updated charge name"
        savedPlan.charges[0].amount = 99.99
        HttpResponse updateResponse = updatePlan(planId, savedPlan)
        Map updatedPlan = getPlan(planId)

        then:
        updateResponse.status == 200

        updatedPlan.charges.size() == 1
        updatedPlan.charges*.id.contains(savedChargeId)
    }

    def "update plan: deleting a charge should not mutate existing charge id"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanWithAllChargesJson(productId, null, [status: "DRAFT"])
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)

        savedPlan.charges.remove(0)
        List savedChargeIds = savedPlan.charges*.id
        assert savedChargeIds

        HttpResponse updateResponse = updatePlan(planId, savedPlan)
        Map updatedPlan = getPlan(planId)

        then:
        updateResponse.status == 200

        updatedPlan.charges*.id.size() == 2
        updatedPlan.charges*.id.containsAll(savedChargeIds)
    }

    def "percent of charge is added as expected"() {
        when:
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithPercentOfCharge(productId)
        HttpResponse response = client.post(PLAN_PATH, planJson)
        String planId = response.locationId
        HttpResponse planResponse = client.get(getPlanUrl(planId))
        then:
        response.status == 201
        planResponse.status == 200
        planResponse.body.charges.size() == 1
        planResponse.body.charges[0].percent == 10.00000
        planResponse.body.charges[0].type == "PERCENTAGE_OF"
        planResponse.body.charges[0].priceTiers == []
        planResponse.body.charges[0].amount == null
    }

    def "creating a plan with null isRenewable sets it to false"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJsonWithOTC(productId)
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)

        then:
        savedPlan.charges[0].isRenewable == false
    }

    def "updating the isRenewable on an unused plan is allowed"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJsonWithOTC(productId)
        String planId = createPlan(client, planJson).locationId
        Map savedNewPlan = getPlan(planId)

        savedNewPlan.charges[0].isRenewable = true
        updatePlan(planId, savedNewPlan)
        Map updatedPlan = getPlan(planId)

        then:
        updatedPlan.charges[0].isRenewable == true
    }

    def "creating a plan with isRenewable to true sets it to true"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJsonWithOTC(productId)
        planJson.charges[0].isRenewable = true
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)

        then:
        savedPlan.charges[0].isRenewable == true
    }

    def "updating isRenewable on a used plan is allowed"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client, Recurrence.ONE_YEAR_CYCLE)
        Map planJson = getPlan(order.lineItems[0].planId)
        planJson.name = "updated plan name"
        planJson.description = "updated description"
        planJson.charges[0].isRenewable = true
        HttpResponse updateResponse = updatePlan(order.lineItems[0].planId, planJson)
        Map updatedPlan = getPlan(order.lineItems[0].planId)

        then:
        updateResponse.status == 200
        updatedPlan.name == planJson.name
        updatedPlan.description == planJson.description
        updatedPlan.charges[0].isRenewable == true
    }

    def "custom charge is added as expected"() {
        when:
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithOneTimePerUnitCustomCharge(productId)
        HttpResponse response = client.post(PLAN_PATH, planJson)
        String planId = response.locationId
        HttpResponse planResponse = client.get(getPlanUrl(planId))

        then:
        response.status == 201
        planResponse.status == 200
        planResponse.body.charges.size() == 1
        planResponse.body.charges[0].type == "ONE_TIME"
        planResponse.body.charges[0].priceTiers == []
        planResponse.body.charges[0].amount == null
        planResponse.body.charges[0].isCustom == true
    }

    def "revert plan to draft"() {
        when:
        Map plan = createPlanWithRecurringCharge(client, [status: 'ACTIVE'])
        def revertToDraftSuccess = revertToDraft(client, plan.id as String)
        Map revertedPlan = getPlan(plan.id as String)

        activatePlan(client, plan.id as String)

        OrderSpec.createOrderForPlan(client, null, plan)
        def inUsePlan = getPlan(client, plan.id as String)

        def revertToDraftResponse = revertToDraft(client, plan.id as String)

        then:
        revertToDraftSuccess.status == 200
        revertedPlan.status == 'DRAFT'
        inUsePlan.status == 'ACTIVE'
        revertToDraftResponse.error.contains("in use")
    }

    def "deprecate plan"() {
        when:
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, PlanType.YEARLY_RECURRING_UNIT_PRICE)
        HttpResponse response = client.post(PLAN_PATH, planJson)
        String planId = response.locationId
        HttpResponse deprecateResponse = deprecatePlan(client, planId)

        then:
        response.status == 201
        deprecateResponse.status == 200
        deprecateResponse.body.status == "DEPRECATED"
    }

    def "reactivate deprecated plan"() {
        when:
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, PlanType.YEARLY_RECURRING_UNIT_PRICE)
        HttpResponse response = client.post(PLAN_PATH, planJson)
        String planId = response.locationId
        HttpResponse deprecateResponse = deprecatePlan(client, planId)
        HttpResponse reactivateResponse = reactivatePlan(client, planId)

        then:
        response.status == 201
        deprecateResponse.status == 200
        deprecateResponse.body.status == "DEPRECATED"
        reactivateResponse.status == 200
        reactivateResponse.body.status == "ACTIVE"
    }

    def "creating a plan with null templateIds sets it to empty list"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [])
        planJson.status = "DRAFT"
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)

        then:
        savedPlan.templateIds == []
    }

    def "updating the templateIds on an unused plan is allowed"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [])
        planJson.status = "DRAFT"
        String planId = createPlan(client, planJson).locationId
        Map savedNewPlan = getPlan(planId)

        String orderTemplateId = PredefinedTermsSpec.createOrderTemplate(client)
        PredefinedTermsSpec.updatePredefinedTermsStatus(client, orderTemplateId, "active")
        savedNewPlan.templateIds = [orderTemplateId]
        updatePlan(planId, savedNewPlan)
        Map updatedPlan = getPlan(planId)

        then:
        updatedPlan.templateIds == [orderTemplateId]
    }

    def "creating a plan with templateIds sets it"() {
        when:
        String orderTemplateId = PredefinedTermsSpec.createOrderTemplate(client)
        PredefinedTermsSpec.updatePredefinedTermsStatus(client, orderTemplateId, "active")
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [], [:], [orderTemplateId])
        planJson.status = "DRAFT"
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)

        then:
        savedPlan.templateIds == [orderTemplateId]
    }

    def "updating templateIds on a used plan is allowed"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client, Recurrence.ONE_YEAR_CYCLE)
        Map planJson = getPlan(order.lineItems[0].planId)
        planJson.name = "updated plan name"
        planJson.description = "updated description"
        String orderTemplateId = PredefinedTermsSpec.createOrderTemplate(client)
        PredefinedTermsSpec.updatePredefinedTermsStatus(client, orderTemplateId, "active")
        planJson.templateIds = [orderTemplateId]
        HttpResponse updateResponse = updatePlan(order.lineItems[0].planId, planJson)
        Map updatedPlan = getPlan(order.lineItems[0].planId)

        then:
        updateResponse.status == 200
        updatedPlan.name == planJson.name
        updatedPlan.description == planJson.description
        updatedPlan.templateIds == [orderTemplateId]
    }

    def "update templateIds directly"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client, Recurrence.ONE_YEAR_CYCLE)
        String planId = order.lineItems[0].planId
        String orderTemplateId = PredefinedTermsSpec.createOrderTemplate(client)

        HttpResponse updateResponse = updatePlanTerms(planId, [orderTemplateId])
        Map plan = getPlan(planId)

        then:
        updateResponse.status == 200
        plan.templateIds == [orderTemplateId]
    }

    def "updating a plan should not mutate existing entity ids"() {
        when:
        Map planJson = PlanJsonBuilder.getPlanJsonWithOTC(productId)
        planJson.entityIds = Set.of(entityId)
        String planId = createPlan(client, planJson).locationId
        Map savedPlan = getPlan(planId)

        Map updatePlanJson = new HashMap(savedPlan)
        updatePlanJson.name = "updated " + savedPlan.name
        HttpResponse updateResponse = updatePlan(planId, updatePlanJson)
        Map updatedPlan = getPlan(planId)

        then:
        updateResponse.status == 200
        updatedPlan.entityIds == savedPlan.entityIds
    }

    def "can edit deprecated plan"() {
        when:
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, PlanType.YEARLY_RECURRING_UNIT_PRICE)
        HttpResponse response = client.post(PLAN_PATH, planJson)
        String planId = response.locationId
        HttpResponse deprecateResponse = deprecatePlan(client, planId)
        planJson.name = "new name"
        HttpResponse updateResponse = updatePlan(client, planId, planJson)
        Map updatedPlan = getPlan(client, planId)

        then:
        response.status == 201
        deprecateResponse.status == 200
        updateResponse.status == 200
        updatedPlan.name == "new name"
        updatedPlan.status == "DEPRECATED"
    }

    static HttpResponse createPlanWithCharge(HttpClient client, Map charge) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [charge])
        return client.post(PLAN_PATH, planJson)
    }

    static HttpResponse createProductAndPlan(HttpClient client, Map chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        return createPlanWithTaxRateId(client, productId, "ACTIVE", chargeOverrides)
    }

    static HttpResponse createPlan(HttpClient client, Map planJson) {
        return client.post(PLAN_PATH, planJson)
    }

    static HttpResponse createPlanWithTaxRateId(HttpClient client, String productId, String status = "ACTIVE", Map chargeOverrides = [:]) {
        def taxRateId = TaxRateSpec.addNewTaxRate(client, TaxRateSpec.DEFAULT_TAX_CODE, null).locationId
        def planJson = PlanJsonBuilder.getPlanWithAllChargesJson(productId, taxRateId, [:], chargeOverrides)
        planJson.status = status
        return client.post(PLAN_PATH, planJson)
    }

    static HttpResponse createPlanWithGivenTaxRateId(HttpClient client, String productId, String taxRateId, String status = "ACTIVE") {
        def planJson = PlanJsonBuilder.getPlanWithAllChargesJson(productId, taxRateId)
        planJson.status = status
        return client.post(PLAN_PATH, planJson)
    }

    static Map createAndGetPlan(HttpClient client, Map planJson) {
        HttpResponse response = client.post(PLAN_PATH, planJson)
        String planId = response.locationId
        return client.get(getPlanUrl(planId)).body
    }

    static Map createAndGetPlanWithTaxRate(HttpClient client, Map chargeOverrides = [:]) {
        HttpResponse res = createProductAndPlan(client, chargeOverrides)
        return client.get(getPlanUrl(res.locationId)).body
    }

    static Map createPlanWithPlanType(HttpClient client, String productId, PlanType planType) {
        Map planJson = PlanJsonBuilder.getPlanJson(productId, planType)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithBlockPriceCharge(HttpClient client, planOverrides = [:], chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithRecurringBlockCharge(productId, planOverrides, chargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithOneTimeCharge(HttpClient client, planOverrides = [:], chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithOTC(productId, planOverrides, chargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithPrepaidCharge(HttpClient client, chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithPrepaidCharge(productId, [:], chargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithMonthlyRecurringCharge(HttpClient client, planOverrides = [:], chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithMonthlyRecurringCharge(productId, planOverrides, chargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithCustomField(HttpClient client, String fieldName, String fieldValue, planOverrides = [:], chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map testPlan = createPlanWithRecurringCharge(client, planOverrides, chargeOverrides)

        Map cfDefinition = [
            fieldName       : fieldName,
            parentObjectType: ObjectTypes.PLAN_TYPE,
            fieldType       : BaseCustomFieldDefinitionSpec.STRING_FIELD_TYPE,
        ]
        BaseCustomFieldDefinitionSpec.createCFDefinition(client, cfDefinition)
        Map customFieldUpdateInput = [
            value: fieldValue
        ]
        def response = client.put("${CustomFieldDefinitionSpec.CUSTOM_FIELD_PATH}/${ObjectTypes.PLAN_TYPE}/${testPlan.id}/${fieldName}", customFieldUpdateInput)

        return client.get(getPlanUrl(testPlan.id as String)).body
    }

    static Map createPlanWithOneTimeEventBasedBillingCharge(HttpClient client) {
        String productId = ProductSpec.createProduct(client).locationId
        Map chargeJson = PlanJsonBuilder.getOneTimeChargeJson([eventBased: true])
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [chargeJson])
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithRecurringEventBasedBillingCharge(HttpClient client) {
        String productId = ProductSpec.createProduct(client).locationId
        Map chargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson([eventBased: true])
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [chargeJson])
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithRecurringEventBasedBillingChargeWithAmount(HttpClient client, BigDecimal amount) {
        String productId = ProductSpec.createProduct(client).locationId
        Map chargeJson = PlanJsonBuilder.getRecurringPerUnitChargeJson([eventBased: true, amount: amount])
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [chargeJson])
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithMonthlyRecurringChargeOfZeroAmount(HttpClient client, planOverrides = [:], chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        def taxRateId = TaxRateSpec.addNewTaxRate(client, TaxRateSpec.DEFAULT_TAX_CODE, null).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithMonthlyRecurringChargeOfZeroAmount(productId, taxRateId, planOverrides, chargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithRecurringCharge(HttpClient client, planOverrides = [:], chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithRecurringCharge(productId, planOverrides, chargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createDraftPlanWithNoCharges(HttpClient client, planOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithNoCharges(productId, [status: "DRAFT"] + planOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithRecurringFlatFeeCharge(HttpClient client) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithRecurringFlatFeeCharge(productId)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithUsageCharge(HttpClient client, BigDecimal chargeAmount = BigDecimal.ZERO, Map chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJson(productId, [
            PlanJsonBuilder.getUsagePerUnitChargeJson([amount: chargeAmount] + chargeOverrides)
        ])
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithRecurringAndUsageCharge(HttpClient client, recurringChargeOverrides = [:], usageChargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithRecurringAndUsageCharge(productId, recurringChargeOverrides, usageChargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithMinimumCommit(HttpClient client, Map recurringChargeOverrides = [:], Map usageChargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithRecurringFlatFeeCharge(productId, [status: "DRAFT"], [recurrence: Recurrence.ONE_MONTH_CYCLE, billingTerm: "IN_ARREARS"] + recurringChargeOverrides)
        Map plan = createAndGetPlan(client, planJson)
        String flatFeeChargeId = plan.charges.find{ it.type == "RECURRING" }.id
        ChargeSpec.addCharge(client, plan.id, PlanJsonBuilder.getUsagePerUnitChargeJson([amount: 2, minimumCommitBaseChargeId: flatFeeChargeId] + usageChargeOverrides))
        activatePlan(client, plan.id)
        return getPlan(client, plan.id)
    }

    static Map createPlanWithOverage(HttpClient client, Map recurringChargeOverrides = [:], Map usageChargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithMonthlyRecurringCharge(productId, [status: "DRAFT"], [recurrence: Recurrence.ONE_MONTH_CYCLE] + recurringChargeOverrides)
        Map plan = createAndGetPlan(client, planJson)
        String recurringChargeId = plan.charges.find{ it.type == "RECURRING" }.id
        ChargeSpec.addCharge(client, plan.id, PlanJsonBuilder.getUsagePerUnitChargeJson([amount: 2, overageBaseChargeId: recurringChargeId] + usageChargeOverrides))
        activatePlan(client, plan.id)
        return getPlan(client, plan.id)
    }

    static Map createPlanWithPercentOfCharge(HttpClient client, planOverrides = [:], chargeOverrides = [:]) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithPercentOfCharge(productId, "FLAT_FEE", 10, planOverrides, chargeOverrides)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithOneTimeFlatFeeCustomCharge(HttpClient client) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithOneTimeFlatFeeCustomCharge(productId)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithOneTimePerUnitCustomCharge(HttpClient client) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithOneTimePerUnitCustomCharge(productId)
        return createAndGetPlan(client, planJson)
    }

    static Map createPlanWithRecurringPerUnitCustomCharge(HttpClient client, boolean isRenewable = false, boolean eventBased = false) {
        String productId = ProductSpec.createProduct(client).locationId
        Map planJson = PlanJsonBuilder.getPlanJsonWithRecurringPerUnitCustomCharge(productId, isRenewable, eventBased)
        return createAndGetPlan(client, planJson)
    }

    static Map createAndGetDraftPlan(HttpClient client) {
        String productId = ProductSpec.createProduct(client).locationId
        HttpResponse res = createPlanWithTaxRateId(client, productId, "DRAFT")
        return client.get(getPlanUrl(res.locationId)).body
    }

    static Map createPrepaidDrawdownPlan(HttpClient client, Map usageOverrides = [:], Map prepaidOverrides = [:]) {
        HttpResponse response = ProductSpec.createProduct(client)
        String productId = response.locationId

        Map drawdownCharge = PlanJsonBuilder.getUsagePerUnitChargeJson(usageOverrides)
        drawdownCharge.isDrawdown = true

        Map prepaidCharge = PlanJsonBuilder.getPrepaidPerUnitChargeJson(prepaidOverrides)

        Map planJson = PlanJsonBuilder.getPlanJson(productId, [drawdownCharge, prepaidCharge])
        HttpResponse addResponse = client.post(PLAN_PATH, planJson)
        HttpResponse getResponse = client.get(getPlanUrl(addResponse.locationId))

        return getResponse.body
    }

    Map getPlan(String planId) {
        return client.get(getPlanUrl(planId)).body
    }

    static Map getPlan(HttpClient client, String planId) {
        return client.get(getPlanUrl(planId)).body
    }

    static Map getPlanWithClient(HttpClient client, planId) {
        return client.get(getPlanUrl(planId)).body
    }

    HttpResponse updatePlan(String planId, Map planJson) {
        return client.put(getPlanUrl(planId), planJson)
    }

    HttpResponse updatePlanTerms(String planId, List<String> templateIds) {
        return client.put("${getPlanUrl(planId)}/terms", templateIds)
    }

    static HttpResponse updatePlan(HttpClient client, String planId, Map planJson) {
        return client.put(getPlanUrl(planId), planJson)
    }

    static String getPlanUrl(String planId) {
        return "${PLAN_PATH}/${planId}"
    }

    static HttpResponse activatePlan(HttpClient client, String planId) {
        return client.putNoBody("${PLAN_PATH}/${planId}/activate")
    }

    static HttpResponse deprecatePlan(HttpClient client, String planId) {
        return client.putNoBody("${PLAN_PATH}/${planId}/deprecate")
    }

    static HttpResponse reactivatePlan(HttpClient client, String planId) {
        return client.putNoBody("${PLAN_PATH}/${planId}/reactivate")
    }

    static HttpResponse revertToDraft(HttpClient client, String planId) {
        return client.putNoBody("${PLAN_PATH}/${planId}/revertToDraft")
    }

    static HttpResponse duplicatePlan(HttpClient client, String planId) {
        return client.post("${PLAN_PATH}/${planId}/duplicate", null)
    }

    static HttpResponse deletePlan(HttpClient client, String planId) {
        String path = "${PLAN_PATH}/${planId}"
        return client.delete(path)
    }

    static String getLedgerAccountsMappingPath(String planId, String chargeId) {
        return "${PLAN_PATH}/${planId}${CHARGE_PATH}/${chargeId}/ledgerAccounts"
    }

    static HttpResponse getLedgerAccountsForCharge(HttpClient client, String planId, String chargeId) {
        String path = getLedgerAccountsMappingPath(planId, chargeId)
        return client.get(path)
    }

    static HttpResponse putLedgerAccountMappings(HttpClient client, String planId, String chargeId, List<String> ledgerAccountIds) {
        String path = getLedgerAccountsMappingPath(planId, chargeId)
        return client.put(path, ledgerAccountIds)
    }

    static String getChargePath(String planId, String chargeId) {
        return "${getChargesPath(planId)}/${chargeId}"
    }

    static String getChargesPath(String planId) {
        return "${PLAN_PATH}/${planId}${CHARGE_PATH}"
    }
}
