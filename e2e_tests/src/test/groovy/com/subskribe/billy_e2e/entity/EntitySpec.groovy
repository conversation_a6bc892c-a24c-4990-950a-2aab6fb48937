package com.subskribe.billy_e2e.entity


import com.subskribe.billy_e2e.EnumType
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.accounting.AccountingPeriodSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.FeatureFlag

class EntitySpec extends Authenticated {

    public static final String ENTITY_PATH = "/entities"

    def setupSpec() {
        FeatureFlag.updateFlag(client, "MULTI_ENTITY_AUTH", true)
    }

    def cleanupSpec() {
        FeatureFlag.updateFlag(client, "MULTI_ENTITY_AUTH", false)
    }

    def "create a new entity"() {
        when:
        HttpResponse response = createEntity(client, "Canada",  "INV-CA-", "CA", "CALENDAR_DAYS", "NORMALIZED", "US/Pacific", "CAD", "Please enter your bank details here.")

        then:
        response.status == 201
        response.body.name == "Canada"
        response.body.prorationScheme == "CALENDAR_DAYS"
        response.body.prorationMode == "NORMALIZED"
        response.body.timezone == "US/Pacific"
        response.body.functionalCurrency == "CAD"
        response.body.wireInstruction == "Please enter your bank details here."
    }

    static HttpResponse createEntity(HttpClient client, String name, String invoicePrefix, String displayId, String prorationScheme = "CALENDAR_DAYS", String prorationMode = "NORMALIZED", String timezone = "US/Pacific", String functionalCurrency = "USD", String wireInstruction = "") {
        Map payload = getEntityJson(name, invoicePrefix, displayId, prorationScheme, prorationMode, timezone, functionalCurrency, wireInstruction)
        return client.post(ENTITY_PATH, payload)
    }

    // create a second entity, initialize accounting periods, and switches back to the original entity
    static boolean initMultiEntityWithAccounting(HttpClient client, String entityId = null) {
        FeatureFlag.updateFlag(client, "MULTI_ENTITY_AUTH", true)
        EntityBaseGqlSpec.createAndSwitchEntity(client, [
            name: "Second Entity",
            displayId: "ENT-2",
            invoiceConfig: [ prefix: "INV-E2-", scheme: new EnumType("SEQUENCE"), length: 6 ],
        ])
        def acctPeriodResponse = AccountingPeriodSpec.specifyAccountingPeriod(client)
        assert acctPeriodResponse.status == 200
        if (entityId) {
            EntityBaseGqlSpec.switchClientEntity(client, entityId)
        }
    }

    private static Map getEntityJson(String name, String invoicePrefix, String displayId, String prorationScheme = "CALENDAR_DAYS", String prorationMode = "NORMALIZED", String timezone = "US/Pacific", String functionalCurrency = "USD", String wireInstruction = "") {
        return [
            displayId: displayId,
            name: name,
            prorationScheme: prorationScheme,
            prorationMode: prorationMode,
            timezone: timezone,
            functionalCurrency: functionalCurrency,
            wireInstruction: wireInstruction,
            invoiceConfig: [
                prefix: invoicePrefix,
                scheme: "SEQUENCE",
                length: 6,
                nextSequenceNumber: 1,
            ]
        ]
    }
}
