package com.subskribe.billy_e2e.graphql

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.account.AccountSpec
import com.subskribe.billy_e2e.account.ContactSpec
import com.subskribe.billy_e2e.attachments.AttachmentsSpec
import com.subskribe.billy_e2e.auth.BillyAdminApiSpec
import com.subskribe.billy_e2e.discount.DiscountSpec
import com.subskribe.billy_e2e.graphql.queries.OrderQueries
import com.subskribe.billy_e2e.graphql.queries.SubscriptionQueries
import com.subskribe.billy_e2e.invoice.BaseInvoiceSpec
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.salesforce.SalesforceCompleteSpec
import com.subskribe.billy_e2e.settings.TenantSettingRestAndGqlSpec
import com.subskribe.billy_e2e.subscription.SubscriptionSpec
import com.subskribe.billy_e2e.template.DocumentTemplateSpec
import com.subskribe.billy_e2e.utils.FeatureFlag
import com.subskribe.billy_e2e.utils.Recurrence
import java.time.Instant
import java.time.LocalDateTime
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class OrderGqlSpec extends GqlSpec {

    @Shared
    String orderId

    @Shared
    String renewOrderId

    @Shared
    String subscriptionId

    @Shared
    String renewedSubscriptionId

    @Shared
    Long subscriptionEndDate

    @Shared
    String planId

    @Shared
    String chargeId

    @Shared
    private TimeZone tenantTimeZone

    @Shared
    private Long orderStartDate = 0

    @Shared
    private static final long DEFAULT_RENEWAL_TERM_LENGTH_IN_YEARS = 1

    @Shared
    HttpClient billyEngineerClient

    def setupSpec() {
        String timeZoneString = getTenantTimeZoneSetting(client)
        tenantTimeZone = TimeZone.getTimeZone(timeZoneString)
        orderStartDate = DateTimeHelper.getStartOfDayEpochSeconds(tenantTimeZone)
        client.post(GQL_PATH, TenantSettingRestAndGqlSpec.updatePaymentTermSettingsGqlMutation([customPaymentTermsAllowed: true]))
        billyEngineerClient = BillyAdminApiSpec.getBillyEngineerClient(client)
    }

    def "create order dryRun gql"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map query = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate, true, null, false)
        HttpResponse response = client.post(GQL_PATH, query)
        Map order = response.body.data.upsertOrder

        then:
        order.id == null
        account != null
        contact != null
        plan != null
        response.status == 200
        order.account.id.startsWith("ACCT-")
        order.shippingContact.id.startsWith("CONT-")
        order.billingContact.id.startsWith("CONT-")
        order.lineItems[0].isDryRunItem == true
        order.lineItems[0].plan.id.startsWith("PLAN-")
        order.lineItems[0].plan.productId.startsWith("PROD-")
        order.lineItems[0].annualizedAmount == order.lineItems[0].amount / 3
        order.lineItems[0].discounts[0].amount == order.lineItems[0].listAmount - order.lineItems[0].amount
        order.lineItemsNetEffect.size() == order.lineItems.size()
        order.lineItemsNetEffect[0].plan.id == order.lineItems[0].plan.id
        order.lineItemsNetEffect[0].plan.productId == order.lineItems[0].plan.productId
        order.lineItemsNetEffect[0].sellUnitPrice == order.lineItems[0].sellUnitPrice
        order.lineItemsNetEffect[0].listUnitPrice == order.lineItems[0].listUnitPrice
        order.lineItemsNetEffect[0].amount == order.lineItems[0].amount
        order.lineItemsNetEffect[0].discounts[0].amount == order.lineItemsNetEffect[0].listAmount - order.lineItemsNetEffect[0].amount
        order.lineItemsNetEffect[0].isDryRunItem == true
        order.billingTerm == "UP_FRONT"
        order.paymentTerm == "NET30"
        order.currency == "USD"
        (order.sfdcOpportunityId as String).contains("TEST_OP_ID")
        order.sfdcOpportunityName == "TEST_OP_NAME"
        order.sfdcOpportunityType == "TEST_OP_TYPE"
        order.sfdcOpportunityStage == "TEST_OP_STAGE"
        order.owner.id == "USER-ADMIN"
        order.totalAmount > 0
        order.totalListAmount > 0
        order.totalDiscount ==  order.totalListAmount - order.totalAmount
        order.totalDiscountPercent == 20
        order.deltaArrPercent == 100
        order.customBillingEligibleOrderLineIds.size() == 1
    }

    def "create order dryRun gql without billing and shipping contact ids"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map query = OrderQueries.createOrderMutation(account.id, null, plan, orderStartDate, orderStartDate, true, null, false)
        HttpResponse response = client.post(GQL_PATH, query)
        Map order = response.body.data.upsertOrder

        then:
        order.id == null
        account != null
        contact != null
        plan != null
        response.status == 200
        order.account.id.startsWith("ACCT-")
        order.shippingContact == null
        order.billingContact == null
        order.lineItems[0].isDryRunItem == true
        order.lineItems[0].plan.id.startsWith("PLAN-")
        order.lineItems[0].plan.productId.startsWith("PROD-")
        order.lineItems[0].discounts[0].amount == order.lineItems[0].listAmount - order.lineItems[0].amount
        order.lineItemsNetEffect.size() == order.lineItems.size()
        order.lineItemsNetEffect[0].plan.id == order.lineItems[0].plan.id
        order.lineItemsNetEffect[0].plan.productId == order.lineItems[0].plan.productId
        order.lineItemsNetEffect[0].sellUnitPrice == order.lineItems[0].sellUnitPrice
        order.lineItemsNetEffect[0].listUnitPrice == order.lineItems[0].listUnitPrice
        order.lineItemsNetEffect[0].amount == order.lineItems[0].amount
        order.lineItemsNetEffect[0].discounts[0].amount == order.lineItemsNetEffect[0].listAmount - order.lineItemsNetEffect[0].amount
        order.lineItemsNetEffect[0].isDryRunItem == true
        order.billingTerm == "UP_FRONT"
        order.paymentTerm == "NET30"
        order.currency == "USD"
        (order.sfdcOpportunityId as String).contains("TEST_OP_ID")
        order.sfdcOpportunityName == "TEST_OP_NAME"
        order.sfdcOpportunityType == "TEST_OP_TYPE"
        order.sfdcOpportunityStage == "TEST_OP_STAGE"
        order.owner.id == "USER-ADMIN"
        order.totalAmount > 0
        order.totalListAmount > 0
    }

    def "create order gql without billing and shipping contact id saved successfully"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map query = OrderQueries.createOrderMutation(account.id, null, plan, orderStartDate, orderStartDate, false, null, false)
        HttpResponse response = client.post(GQL_PATH, query)
        Map order = response.body.data.upsertOrder

        then:
        order.id != null
        account != null
        contact != null
        plan != null
        response.status == 200
        order.account.id.startsWith("ACCT-")
        order.shippingContact == null
        order.billingContact == null
        order.lineItems[0].plan.id.startsWith("PLAN-")
        order.lineItems[0].plan.productId.startsWith("PROD-")
        order.lineItems[0].discounts[0].amount == order.lineItems[0].listAmount - order.lineItems[0].amount
        order.lineItemsNetEffect.size() == order.lineItems.size()
        order.lineItemsNetEffect[0].plan.id == order.lineItems[0].plan.id
        order.lineItemsNetEffect[0].plan.productId == order.lineItems[0].plan.productId
        order.lineItemsNetEffect[0].sellUnitPrice == order.lineItems[0].sellUnitPrice
        order.lineItemsNetEffect[0].listUnitPrice == order.lineItems[0].listUnitPrice
        order.lineItemsNetEffect[0].amount == order.lineItems[0].amount
        order.lineItemsNetEffect[0].discounts[0].amount == order.lineItemsNetEffect[0].listAmount - order.lineItemsNetEffect[0].amount
        order.billingTerm == "UP_FRONT"
        order.paymentTerm == "NET30"
        order.currency == "USD"
        (order.sfdcOpportunityId as String).contains("TEST_OP_ID")
        order.sfdcOpportunityName == "TEST_OP_NAME"
        order.sfdcOpportunityType == "TEST_OP_TYPE"
        order.sfdcOpportunityStage == "TEST_OP_STAGE"
        order.approvalFlows != null
        order.owner.id == "USER-ADMIN"
        order.totalAmount > 0
        order.totalListAmount > 0
    }

    def "create order dryRun with large quantity"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map query = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate, true, null, false, null, '', null, false, ************)
        HttpResponse response = client.post(GQL_PATH, query)

        then:
        response.body.errors.any { it.message.contains("cannot exceed *************") }
    }

    def "create order dryRun gql with predefined discounts"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map discount = DiscountSpec.addAndGetDiscount(client, 0.25)

        Map query = OrderQueries.createOrderMutationWithPredefinedDiscounts(null, account.id, contact.id, plan, orderStartDate, discount, true)
        HttpResponse response = client.post(GQL_PATH, query)
        Map order = response.body.data.upsertOrder

        then:
        order.id == null
        account != null
        contact != null
        plan != null
        response.status == 200
        order.account.id.startsWith("ACCT-")
        order.shippingContact.id.startsWith("CONT-")
        order.billingContact.id.startsWith("CONT-")
        order.predefinedDiscounts[0].id == discount.id
        order.predefinedDiscounts[0].name == discount.name
        order.predefinedDiscounts[0].percent == discount.percent
        order.lineItems[0].plan.id.startsWith("PLAN-")
        order.lineItems[0].plan.productId.startsWith("PROD-")
        order.lineItemsNetEffect.size() == order.lineItems.size()
        order.lineItemsNetEffect[0].plan.id == order.lineItems[0].plan.id
        order.lineItemsNetEffect[0].plan.productId == order.lineItems[0].plan.productId
        order.lineItemsNetEffect[0].sellUnitPrice == order.lineItems[0].sellUnitPrice
        order.lineItemsNetEffect[0].listUnitPrice == order.lineItems[0].listUnitPrice
        order.lineItemsNetEffect[0].amount == order.lineItems[0].amount
        order.lineItemsNetEffect[0].discounts[0].amount == 63
        order.lineItems[0].predefinedDiscounts[0].id == discount.id
        order.lineItems[0].predefinedDiscounts[0].percent == discount.percent
        order.lineItems[0].discounts[0].amount + order.lineItems[0].predefinedDiscounts[0].amount == order.lineItems[0].listAmount - order.lineItems[0].amount
        order.totalAmount > 0
        order.totalListAmount > 0
    }

    def "create order dryRun gql with deprecated discounts returns error"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map discount = DiscountSpec.addAndGetDiscount(client, 0.25)
        DiscountSpec.updateDiscountStatus(client, discount.id, "deprecated")
        HttpResponse getDiscountByIdResponse = client.get(DiscountSpec.getDiscountPath(discount.id))
        discount = getDiscountByIdResponse.body

        Map query = OrderQueries.createOrderMutationWithPredefinedDiscounts(null, account.id, contact.id, plan, orderStartDate, discount, true)
        HttpResponse response = client.post(GQL_PATH, query)

        then:
        response.body.data == null
        response.body.errors != null
    }

    def "create order - updating a discount updates the predefined discount"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        Map discount = DiscountSpec.addAndGetDiscount(client, 0.25)

        Map query = OrderQueries.createOrderMutationWithPredefinedDiscounts(null, account.id, contact.id, plan, orderStartDate, discount, false)
        HttpResponse createOrderResponse = client.post(GQL_PATH, query)
        Map order = createOrderResponse.body.data.upsertOrder
        Map updatedDiscount = DiscountSpec.updateAndGetDiscount(client, discount.id, UUID.randomUUID().toString(), UUID.randomUUID().toString(), UUID.randomUUID().toString(), discount.percent, "ACTIVE")
        query = OrderQueries.getOrderDetailQuery(order.id as String)
        HttpResponse getOrderResponse = client.post(GQL_PATH, query)
        order = getOrderResponse.body.data.orderDetail

        then:
        account != null
        contact != null
        plan != null
        createOrderResponse.status == 200
        getOrderResponse.status == 200
        order.id.startsWith("ORD-")
        order.predefinedDiscounts[0].id == updatedDiscount.id
        order.predefinedDiscounts[0].name == updatedDiscount.name
        order.predefinedDiscounts[0].type == updatedDiscount.type
        order.predefinedDiscounts[0].description == updatedDiscount.description
        order.predefinedDiscounts[0].percent == discount.percent
        order.lineItems[0].predefinedDiscounts[0].id == updatedDiscount.id
        order.lineItems[0].predefinedDiscounts[0].name == updatedDiscount.name
        order.lineItems[0].predefinedDiscounts[0].type == updatedDiscount.type
        order.lineItems[0].predefinedDiscounts[0].description == updatedDiscount.description
        order.lineItems[0].predefinedDiscounts[0].percent == discount.percent
        order.lineItems[0].discounts[0].amount + order.lineItems[0].predefinedDiscounts[0].amount == order.lineItems[0].listAmount - order.lineItems[0].amount
        order.totalAmount > 0
        order.totalListAmount > 0
    }

    def "create order orderLine effectiveDate before order startDate returns error"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map query = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate - 10000, false, null, false)
        HttpResponse response = client.post(GQL_PATH, query)

        then:
        response.body.data == null
        response.body.errors != null
    }

    def "create order orderLine effectiveDate before order startDate with sanitizeLineItemDates set creates order"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map query = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate - 10000, false, null, true)
        HttpResponse response = client.post(GQL_PATH, query)

        then:
        Map order = response.body.data.upsertOrder
        order.id != null
        account != null
        contact != null
        plan != null
        response.status == 200
        order.account.id.startsWith("ACCT-")
        order.shippingContact.id.startsWith("CONT-")
        order.billingContact.id.startsWith("CONT-")
        order.lineItems[0].plan.id.startsWith("PLAN-")
        order.lineItems[0].plan.productId.startsWith("PROD-")
        order.lineItemsNetEffect.size() == order.lineItems.size()
        order.lineItemsNetEffect[0].plan.id == order.lineItems[0].plan.id
        order.lineItemsNetEffect[0].plan.productId == order.lineItems[0].plan.productId
        order.lineItemsNetEffect[0].sellUnitPrice == order.lineItems[0].sellUnitPrice
        order.lineItemsNetEffect[0].listUnitPrice == order.lineItems[0].listUnitPrice
        order.lineItemsNetEffect[0].amount == order.lineItems[0].amount
        order.billingTerm == "UP_FRONT"
        order.paymentTerm == "NET30"
        order.currency == "USD"
        (order.sfdcOpportunityId as String).contains("TEST_OP_ID")
        order.sfdcOpportunityName == "TEST_OP_NAME"
        order.sfdcOpportunityType == "TEST_OP_TYPE"
        order.sfdcOpportunityStage == "TEST_OP_STAGE"
        order.owner.id == "USER-ADMIN"
        order.totalAmount > 0
        order.totalListAmount > 0
    }

    def "create order gql"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        plan.charges[0].isRenewable = true
        PlanSpec.updatePlan(client, plan.id, plan)
        String templateId = DocumentTemplateGqlSpec.createOrderTemplate(client).id

        Map mutation = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate, false, templateId)
        HttpResponse response = client.post(GQL_PATH, mutation)
        Map order = response.body.data.upsertOrder
        orderId = order.id

        then:
        account != null
        contact != null
        plan != null
        response.status == 200
        order.id.startsWith("ORD-")
        order.account.id.startsWith("ACCT-")
        order.shippingContact.id.startsWith("CONT-")
        order.billingContact.id.startsWith("CONT-")
        order.lineItems[0].plan.id.startsWith("PLAN-")
        order.lineItems[0].plan.productId.startsWith("PROD-")
        order.lineItemsNetEffect.size() == order.lineItems.size()
        order.lineItemsNetEffect[0].plan.id == order.lineItems[0].plan.id
        order.lineItemsNetEffect[0].plan.productId == order.lineItems[0].plan.productId
        order.lineItemsNetEffect[0].sellUnitPrice == order.lineItems[0].sellUnitPrice
        order.lineItemsNetEffect[0].listUnitPrice == order.lineItems[0].listUnitPrice
        order.lineItemsNetEffect[0].amount == order.lineItems[0].amount
        order.billingTerm == "UP_FRONT"
        order.paymentTerm == "NET30"
        order.currency == "USD"
        order.orderFormTemplates[0].id == templateId
        (order.sfdcOpportunityId as String).contains("TEST_OP_ID")
        order.sfdcOpportunityName == "TEST_OP_NAME"
        order.sfdcOpportunityType == "TEST_OP_TYPE"
        order.sfdcOpportunityStage == "TEST_OP_STAGE"
        order.approvalFlows != null
        order.approvalFlows.size() == 1
        order.approvalFlows[0].workflowStatus == "PREVIEW"
        order.owner.id == "USER-ADMIN"
        order.totalAmount > 0
        order.totalListAmount > 0
        order.autoRenew == true
        order.totalDiscount ==  order.totalListAmount - order.totalAmount
        order.totalDiscountPercent == 20
        order.deltaArrPercent == 100
    }

    def "create order gql with a custom end date and no term length"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        plan.charges[0].isRenewable = true
        PlanSpec.updatePlan(client, plan.id, plan)
        String templateId = DocumentTemplateGqlSpec.createOrderTemplate(client).id
        Long customEndDate = DateTimeHelper.plusMonths(orderStartDate, 18, DateTimeHelper.DEFAULT_TZ)
        Map mutation = OrderQueries.createOrderMutationWithCustomEndDate(account.id, contact.id, plan, orderStartDate, customEndDate, orderStartDate, false, templateId)
        HttpResponse response = client.post(GQL_PATH, mutation)
        Map order = response.body.data.upsertOrder
        orderId = order.id

        then:
        account != null
        contact != null
        plan != null
        response.status == 200
        order.id.startsWith("ORD-")
        order.account.id.startsWith("ACCT-")
        order.shippingContact.id.startsWith("CONT-")
        order.billingContact.id.startsWith("CONT-")
        order.lineItems[0].plan.id.startsWith("PLAN-")
        order.lineItems[0].plan.productId.startsWith("PROD-")
        order.lineItemsNetEffect.size() == order.lineItems.size()
        order.lineItemsNetEffect[0].plan.id == order.lineItems[0].plan.id
        order.lineItemsNetEffect[0].plan.productId == order.lineItems[0].plan.productId
        order.lineItemsNetEffect[0].sellUnitPrice == order.lineItems[0].sellUnitPrice
        order.lineItemsNetEffect[0].listUnitPrice == order.lineItems[0].listUnitPrice
        order.lineItemsNetEffect[0].amount == order.lineItems[0].amount
        order.billingTerm == "UP_FRONT"
        order.paymentTerm == "NET30"
        order.currency == "USD"
        order.orderFormTemplates[0].id == templateId
        (order.sfdcOpportunityId as String).contains("TEST_OP_ID")
        order.sfdcOpportunityName == "TEST_OP_NAME"
        order.sfdcOpportunityType == "TEST_OP_TYPE"
        order.sfdcOpportunityStage == "TEST_OP_STAGE"
        order.approvalFlows != null
        order.approvalFlows.size() == 1
        order.approvalFlows[0].workflowStatus == "PREVIEW"
        order.owner.id == "USER-ADMIN"
        order.totalAmount > 0
        order.totalListAmount > 0
        order.autoRenew == true
        order.termLength == null
        order.endDate == customEndDate
    }

    def "create order gql without an address and submit"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContactWithoutAddress(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        plan.charges[0].isRenewable = true
        PlanSpec.updatePlan(client, plan.id, plan)
        String templateId = DocumentTemplateGqlSpec.createOrderTemplate(client).id

        Map mutation = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate, false, templateId)
        HttpResponse response = client.post(GQL_PATH, mutation)
        Map order = response.body.data.upsertOrder
        def orderWithoutContactId = order.id

        Map query = OrderQueries.updateOrderStatusMutation(orderWithoutContactId, "submitted")
        HttpResponse draftToSubmittedResponse = client.post(GQL_PATH, query)

        then:
        account != null
        contact != null
        plan != null
        response.status == 200
        draftToSubmittedResponse.body.errors.size() == 0
    }

    def "create order gql with deprecated predefined terms"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        plan.charges[0].isRenewable = true
        PlanSpec.updatePlan(client, plan.id, plan)
        Map template = DocumentTemplateGqlSpec.createOrderTemplate(client)
        DocumentTemplateSpec.updateDocumentTemplateStatus(client, template.id, "active")
        DocumentTemplateSpec.updateDocumentTemplateStatus(client, template.id, "deprecated")

        Map mutation = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate, false, template.id)
        HttpResponse response = client.post(GQL_PATH, mutation)

        then:
        account != null
        contact != null
        plan != null
        response.status == 200
        response.body.errors.size() > 0
    }

    def "create order gql with an attachment of non-PDF filetype"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)
        plan.charges[0].isRenewable = true
        PlanSpec.updatePlan(client, plan.id, plan)
        def fileName = "accountDocument.doc"
        def attachmentId = AttachmentsSpec.uploadAttachment(multiPartClient, account.id as String, fileName, new byte[1])

        Map mutation = OrderQueries.createOrderMutation(account.id, contact.id, plan, orderStartDate, orderStartDate, false, null, false, attachmentId)
        HttpResponse response = client.post(GQL_PATH, mutation)

        then:
        account != null
        contact != null
        plan != null
        response.status == 200
        response.body.errors.size() > 0
    }

    def "update order status to executed"() {
        when:
        Map query = OrderQueries.updateOrderStatusMutation(orderId, "executed")
        HttpResponse draftToExecuteResponse = client.post(GQL_PATH, query)

        query = OrderQueries.updateOrderStatusMutation(orderId, "submitted")
        client.post(GQL_PATH, query)
        query = OrderQueries.getOrderDetailQuery(orderId)
        HttpResponse getOrderResponse = client.post(GQL_PATH, query)
        Map submittedOrder = getOrderResponse.body.data.orderDetail

        query = OrderQueries.updateOrderStatusMutation(orderId, "executed")
        client.post(GQL_PATH, query)

        query = OrderQueries.getOrderDetailQuery(orderId)
        getOrderResponse = client.post(GQL_PATH, query)
        Map executedOrder = getOrderResponse.body.data.orderDetail

        subscriptionId = executedOrder.updatedSubscription.id
        subscriptionEndDate = executedOrder.endDate as Long
        planId = executedOrder.lineItems[0].plan.id
        chargeId = executedOrder.lineItems[0].charge.id

        then:
        draftToExecuteResponse.body.data == null //cannot transition from draft to executed
        submittedOrder.id == orderId
        submittedOrder.status == "APPROVED"
        submittedOrder.approvalFlows.size() > 0
        submittedOrder.approvalFlows[0].id != null
        submittedOrder.approvalFlows[0].approvalStatus == "APPROVED"
        submittedOrder.approvalFlows[0].workflowStatus == "COMPLETED"
        submittedOrder.approvalFlows[0].orderId == orderId
        executedOrder.id == orderId
        executedOrder.status == "EXECUTED"
        executedOrder.lineItemsNetEffect.size() == executedOrder.lineItems.size()
        executedOrder.lineItemsNetEffect[0].plan.id == executedOrder.lineItems[0].plan.id
        executedOrder.lineItemsNetEffect[0].plan.productId == executedOrder.lineItems[0].plan.productId
        executedOrder.lineItemsNetEffect[0].sellUnitPrice == executedOrder.lineItems[0].sellUnitPrice
        executedOrder.lineItemsNetEffect[0].listUnitPrice == executedOrder.lineItems[0].listUnitPrice
        executedOrder.lineItemsNetEffect[0].amount == executedOrder.lineItems[0].amount
        executedOrder.approvalFlows.size() > 0
        executedOrder.approvalFlows[0].id != null
        executedOrder.approvalFlows[0].approvalStatus == "APPROVED"
        executedOrder.approvalFlows[0].workflowStatus == "COMPLETED"
        executedOrder.approvalFlows[0].orderId == orderId
        executedOrder.executedOn == 1689893629
    }

    def "delete and backfill memoized invoice line items for the order, post that on fetching memoized lines backfilled lines should not be returned"() {
        when:
        FeatureFlag.updateFlag(client, "USE_BACKFILLED_MEMOIZATION", false)
        String tenantId = getCurrentTenant(client).tenantId
        HttpResponse deleteMemoizedInvoiceItemsResponse = BaseInvoiceSpec.deleteMemoizedInvoiceLineItems(client, subscriptionId, orderId )
        HttpResponse backfillMemoizedInvoiceItemsResponse = BaseInvoiceSpec.backfillMemoizedInvoiceLineItems(billyEngineerClient, tenantId, orderId)
        HttpResponse memoizedInvoiceItemsResponse = BaseInvoiceSpec.getMemoizedInvoiceLineItems(client, subscriptionId)

        then:
        deleteMemoizedInvoiceItemsResponse.status == 200
        deleteMemoizedInvoiceItemsResponse.body == "2"
        backfillMemoizedInvoiceItemsResponse.status == 200
        backfillMemoizedInvoiceItemsResponse.body.size() == 2
        memoizedInvoiceItemsResponse.status == 200
        // No backfilled lines should be fetched
        memoizedInvoiceItemsResponse.body.size() == 0
    }

    def "delete and backfill memoized invoice line items for the order, post that on fetching memoized lines backfilled lines should be returned if FF is enabled"() {
        when:
        FeatureFlag.updateFlag(client, "USE_BACKFILLED_MEMOIZATION", true)
        String tenantId = getCurrentTenant(client).tenantId
        HttpResponse deleteMemoizedInvoiceItemsResponse = BaseInvoiceSpec.deleteMemoizedInvoiceLineItems(client, subscriptionId, orderId )
        HttpResponse backfillMemoizedInvoiceItemsResponse = BaseInvoiceSpec.backfillMemoizedInvoiceLineItems(billyEngineerClient, tenantId, orderId)
        HttpResponse memoizedInvoiceItemsResponse = BaseInvoiceSpec.getMemoizedInvoiceLineItems(client, subscriptionId)

        then:
        deleteMemoizedInvoiceItemsResponse.status == 200
        deleteMemoizedInvoiceItemsResponse.body == "4"
        backfillMemoizedInvoiceItemsResponse.status == 200
        backfillMemoizedInvoiceItemsResponse.body.size() == 2
        memoizedInvoiceItemsResponse.status == 200
        // Backfilled lines should be fetched
        memoizedInvoiceItemsResponse.body.size() == 2
    }

    def "generate renew subscription"() {
        when:
        Map query = OrderQueries.generateRenewalQuery(subscriptionId)
        HttpResponse renewOrderResponse = client.post(GQL_PATH, query)
        Map renewOrder = renewOrderResponse.body.data.generateRenewalOrder

        then:
        renewOrder.id == null
        renewOrder.lineItems[0].plan.id == planId
        renewOrder.lineItems[0].charge.id == chargeId
        renewOrder.lineItemsNetEffect.size() == renewOrder.lineItems.size()
        renewOrder.lineItemsNetEffect[0].plan.id == planId
        renewOrder.lineItemsNetEffect[0].charge.id == chargeId
        renewOrder.renewalForSubscription.id == subscriptionId
        renewOrder.renewalForSubscription.version == 1
        renewOrder.totalAmount > 0
        renewOrder.totalListAmount > 0
        renewOrder.autoRenew == true
        renewOrder.startDate == subscriptionEndDate
        Instant.ofEpochSecond(renewOrder.endDate).atZone(tenantTimeZone.toZoneId()).getYear() ==
                DEFAULT_RENEWAL_TERM_LENGTH_IN_YEARS + Instant.ofEpochSecond(renewOrder.startDate).atZone(tenantTimeZone.toZoneId()).getYear()
    }

    def "renew subscription dry run"() {
        when:
        Map query = OrderQueries.renewOrderMutation(subscriptionId, subscriptionEndDate, true)
        HttpResponse renewOrderResponse = client.post(GQL_PATH, query)
        Map renewOrder = renewOrderResponse.body.data.upsertRenewalOrder

        then:
        renewOrder.id == null
        renewOrder.lineItems[0].plan.id == planId
        renewOrder.lineItems[0].charge.id == chargeId
        renewOrder.lineItemsNetEffect.size() == renewOrder.lineItems.size()
        renewOrder.lineItemsNetEffect[0].plan.id == planId
        renewOrder.lineItemsNetEffect[0].charge.id == chargeId
        renewOrder.renewalForSubscription.id == subscriptionId
        renewOrder.renewalForSubscription.version == 1
        renewOrder.owner.id == "USER-ADMIN"
        renewOrder.totalAmount > 0
        renewOrder.totalListAmount > 0
        renewOrder.startDate == subscriptionEndDate
    }

    def "renew subscription generates draft order"() {
        when:
        Map query = OrderQueries.renewOrderMutation(subscriptionId, subscriptionEndDate)
        HttpResponse renewOrderResponse = client.post(GQL_PATH, query)
        Map renewOrder = renewOrderResponse.body.data.upsertRenewalOrder
        renewOrderId = renewOrder.id

        then:
        renewOrder.id != null
        renewOrder.lineItems[0].plan.id == planId
        renewOrder.lineItems[0].charge.id == chargeId
        renewOrder.lineItemsNetEffect.size() == renewOrder.lineItems.size()
        renewOrder.lineItemsNetEffect[0].plan.id == planId
        renewOrder.lineItemsNetEffect[0].charge.id == chargeId
        renewOrder.renewalForSubscription.id == subscriptionId
        renewOrder.renewalForSubscription.version == 1
        renewOrder.owner.id == "USER-ADMIN"
        renewOrder.totalAmount > 0
        renewOrder.totalListAmount > 0
    }

    def "duplicate renewed order"() {
        when:
        Map query = OrderQueries.duplicateOrderQuery(renewOrderId)
        HttpResponse duplicateOrderResponse = client.post(GQL_PATH, query)
        Map duplicateOrder = duplicateOrderResponse.body.data.duplicateOrder

        then:
        duplicateOrder.id == null
        duplicateOrder.lineItems[0].plan.id == planId
        duplicateOrder.lineItems[0].charge.id == chargeId
        duplicateOrder.lineItemsNetEffect[0].plan.id == planId
        duplicateOrder.lineItemsNetEffect[0].charge.id == chargeId
        duplicateOrder.renewalForSubscription.id == subscriptionId
        duplicateOrder.renewalForSubscription.version == 1
    }

    def "update renew order status to executed"() {
        when:
        Map query = OrderQueries.updateOrderStatusMutation(renewOrderId, "executed")
        HttpResponse draftToExecuteResponse = client.post(GQL_PATH, query)

        query = OrderQueries.updateOrderStatusMutation(renewOrderId, "submitted")
        client.post(GQL_PATH, query)
        query = OrderQueries.getOrderDetailQuery(renewOrderId)
        HttpResponse orderDetailResponse = client.post(GQL_PATH, query)
        Map submittedOrder = orderDetailResponse.body.data.orderDetail

        query = OrderQueries.updateOrderStatusMutation(renewOrderId, "executed")
        client.post(GQL_PATH, query)
        query = OrderQueries.getOrderDetailQuery(renewOrderId)
        orderDetailResponse = client.post(GQL_PATH, query)
        Map executedOrder = orderDetailResponse.body.data.orderDetail

        renewedSubscriptionId = executedOrder.updatedSubscription.id

        then:
        draftToExecuteResponse.body.data == null //cannot transition from draft to executed
        submittedOrder.id == renewOrderId
        submittedOrder.status == "APPROVED"
        submittedOrder.approvalFlows.size() > 0
        submittedOrder.approvalFlows[0].id != null
        submittedOrder.approvalFlows[0].approvalStatus == "APPROVED"
        submittedOrder.approvalFlows[0].workflowStatus == "COMPLETED"
        submittedOrder.approvalFlows[0].orderId == renewOrderId
        submittedOrder.totalAmount > 0
        submittedOrder.totalListAmount > 0
        executedOrder.id == renewOrderId
        executedOrder.status == "EXECUTED"
        executedOrder.lineItemsNetEffect.size() == executedOrder.lineItems.size()
        executedOrder.lineItemsNetEffect[0].plan.id == executedOrder.lineItems[0].plan.id
        executedOrder.lineItemsNetEffect[0].plan.productId == executedOrder.lineItems[0].plan.productId
        executedOrder.lineItemsNetEffect[0].sellUnitPrice == executedOrder.lineItems[0].sellUnitPrice
        executedOrder.lineItemsNetEffect[0].listUnitPrice == executedOrder.lineItems[0].listUnitPrice
        executedOrder.lineItemsNetEffect[0].amount == executedOrder.lineItems[0].amount
        executedOrder.approvalFlows.size() > 0
        executedOrder.approvalFlows[0].id != null
        executedOrder.approvalFlows[0].approvalStatus == "APPROVED"
        executedOrder.approvalFlows[0].workflowStatus == "COMPLETED"
        executedOrder.approvalFlows[0].orderId == renewOrderId
        executedOrder.totalAmount > 0
        executedOrder.totalListAmount > 0
    }

    def "validate subscription details for renewed order"() {
        when:
        Map renewedOrderQuery = OrderQueries.getOrderDetailQuery(renewOrderId)
        HttpResponse renewedOrderResponse = client.post(GQL_PATH, renewedOrderQuery)
        Map renewedOrder = renewedOrderResponse.body.data.orderDetail

        Map originalSubscriptionQuery = SubscriptionQueries.getSubscriptionDetailQuery(subscriptionId)
        HttpResponse getOriginalSubscriptionResponse = client.post(GQL_PATH, originalSubscriptionQuery)
        Map originalSubscription = getOriginalSubscriptionResponse.body.data.subscriptions[0]

        Map renewedSubscriptionQuery = SubscriptionQueries.getSubscriptionDetailQuery(renewedSubscriptionId)
        HttpResponse getRenewedSubscriptionResponse = client.post(GQL_PATH, renewedSubscriptionQuery)
        Map renewedSubscription = getRenewedSubscriptionResponse.body.data.subscriptions[0]

        then:
        renewedOrder.updatedSubscription.id == renewedSubscription.id
        renewedOrder.renewalForSubscription.id == originalSubscription.id
        originalSubscription.endDate == renewedSubscription.startDate
        originalSubscription.renewedToSubscription.id == renewedSubscription.id
        renewedSubscription.renewedFromSubscription.id == originalSubscription.id
    }

    def "duplicate order"() {
        when:
        Map query = OrderQueries.duplicateOrderQuery(orderId)
        HttpResponse duplicateOrderResponse = client.post(GQL_PATH, query)
        Map duplicateOrder = duplicateOrderResponse.body.data.duplicateOrder

        then:
        duplicateOrder.id == null
        duplicateOrder.name.endsWith(" Copy")
        duplicateOrder.lineItems[0].plan.id == planId
        duplicateOrder.lineItems[0].charge.id == chargeId
        duplicateOrder.lineItemsNetEffect.size() == duplicateOrder.lineItems.size()
        duplicateOrder.lineItemsNetEffect[0].plan.id == planId
        duplicateOrder.lineItemsNetEffect[0].charge.id == chargeId
        duplicateOrder.totalAmount > 0
        duplicateOrder.totalListAmount > 0
    }

    def "duplicate order with a custom predefined template"() {
        when:
        Map order = getOrderById(client, orderId)
        String templateId = order.orderFormTemplates[0].id
        String name = order.orderFormTemplates[0].name + " - updated"
        String description = order.orderFormTemplates[0].description + " - updated"
        String content = order.orderFormTemplates[0].content + " - updated"
        Map upsertCustomPredefinedTemplateOnOrderMutation = OrderQueries.upsertCustomPredefinedTemplateOnOrderMutation(orderId, templateId, name, description, content)
        HttpResponse upsertCustomPredefinedTemplateOnOrderResponse = client.post(GQL_PATH, upsertCustomPredefinedTemplateOnOrderMutation)
        Map upsertCustomPredefinedTemplateOnOrder = upsertCustomPredefinedTemplateOnOrderResponse.body.data.upsertCustomPredefinedTemplateOnOrder

        Map query = OrderQueries.duplicateOrderQuery(orderId)
        HttpResponse duplicateOrderResponse = client.post(GQL_PATH, query)
        Map duplicateOrder = duplicateOrderResponse.body.data.duplicateOrder

        then:
        upsertCustomPredefinedTemplateOnOrder.id == templateId
        upsertCustomPredefinedTemplateOnOrder.orderId == orderId
        upsertCustomPredefinedTemplateOnOrder.name == name
        upsertCustomPredefinedTemplateOnOrder.description == description
        upsertCustomPredefinedTemplateOnOrder.content == content

        duplicateOrder.id == null
        duplicateOrder.name.endsWith(" Copy")
        duplicateOrder.lineItems[0].plan.id == planId
        duplicateOrder.lineItems[0].charge.id == chargeId
        duplicateOrder.lineItemsNetEffect.size() == duplicateOrder.lineItems.size()
        duplicateOrder.lineItemsNetEffect[0].plan.id == planId
        duplicateOrder.lineItemsNetEffect[0].charge.id == chargeId
        duplicateOrder.totalAmount > 0
        duplicateOrder.totalListAmount > 0
        duplicateOrder.orderFormTemplates.size() == 1
        duplicateOrder.orderFormTemplates[0].id == templateId
        duplicateOrder.customPredefinedTemplatesOnOrder.size() == 1
        duplicateOrder.customPredefinedTemplatesOnOrder[0].id == templateId
        duplicateOrder.customPredefinedTemplatesOnOrder[0].orderId == null  // orderId is null in dry run order
        duplicateOrder.customPredefinedTemplatesOnOrder[0].name == name
        duplicateOrder.customPredefinedTemplatesOnOrder[0].description == description
        duplicateOrder.customPredefinedTemplatesOnOrder[0].content == content
    }

    def "create amendment order dryRun"() {
        when:
        Map query = OrderQueries.generateAmendmentQuery(subscriptionId)
        HttpResponse generateAmendmentResponse = client.post(GQL_PATH, query)
        Map orderDetail = generateAmendmentResponse.body.data.generateAmendment

        Long effectiveDate = DateTimeHelper.plusMonths(orderDetail.startDate, 12, DateTimeHelper.DEFAULT_TZ)

        Map amendmentMutation = OrderQueries.createAmendmentMutation(
                orderDetail.subscriptionId,
                orderDetail.billingContact.id,
                orderDetail.lineItems[0].plan.id,
                orderDetail.lineItems[0].charge.id,
                orderDetail.lineItems[0].subscriptionChargeId,
                effectiveDate,
                "UPDATE",
                15,
                true
                )

        HttpResponse amendmentResponse = client.post(GQL_PATH, amendmentMutation)
        Map amendmentOrder = amendmentResponse.body.data.upsertAmendment

        then:
        generateAmendmentResponse.status == 200
        orderDetail.id == null
        orderDetail.currentSubscription.id == subscriptionId
        orderDetail.currentSubscription.version == 1
        orderDetail.subscriptionTargetVersion == 2
        orderDetail.lineItems.size() == 1
        orderDetail.lineItemsNetEffect.size() == 0
        orderDetail.totalAmount == 0
        orderDetail.totalListAmount == 0
        orderDetail.autoRenew == true

        amendmentResponse.status == 200
        amendmentOrder.currentSubscription.id == subscriptionId
        amendmentOrder.currentSubscription.version == 1
        amendmentOrder.subscriptionTargetVersion == 2
        amendmentOrder.lineItems.size() == 1
        amendmentOrder.lineItems[0].quantity == 15
        amendmentOrder.lineItemsNetEffect.size() == 2
        amendmentOrder.lineItemsNetEffect.any { it.quantity == -10 }
        amendmentOrder.lineItemsNetEffect.any { it.quantity == 15 }
        amendmentOrder.owner.id == "USER-ADMIN"
        amendmentOrder.totalAmount > 0
        amendmentOrder.totalListAmount > 0
        amendmentOrder.sfdcOpportunityName.size() > 0
    }

    def "create amendment order with UPDATE action"() {
        when:
        Map query = OrderQueries.generateAmendmentQuery(subscriptionId)
        HttpResponse generateAmendmentResponse = client.post(GQL_PATH, query)
        Map orderDetail = generateAmendmentResponse.body.data.generateAmendment

        Long effectiveDate = DateTimeHelper.plusMonths(orderDetail.startDate, 12, DateTimeHelper.DEFAULT_TZ)

        Map amendmentMutation = AmendmentGqlSpec.getAmendmentMutationFromOrder(orderDetail, effectiveDate, "UPDATE", 15)

        HttpResponse amendmentResponse = client.post(GQL_PATH, amendmentMutation)
        Map amendmentOrder = amendmentResponse.body.data.upsertAmendment

        then:
        generateAmendmentResponse.status == 200
        orderDetail.currentSubscription.id == subscriptionId
        orderDetail.currentSubscription.version == 1
        orderDetail.subscriptionTargetVersion == 2

        amendmentResponse.status == 200
        amendmentOrder.currentSubscription.id == subscriptionId
        amendmentOrder.currentSubscription.version == 1
        amendmentOrder.subscriptionTargetVersion == 2
        amendmentOrder.lineItems.size() == 1
        amendmentOrder.lineItems[0].quantity == 15
        amendmentOrder.lineItemsNetEffect.size() == 2
        amendmentOrder.lineItemsNetEffect.any { it.quantity == -10 }
        amendmentOrder.lineItemsNetEffect.any { it.quantity == 15 }
        amendmentOrder.owner.id == "USER-ADMIN"
        amendmentOrder.totalAmount > 0
        amendmentOrder.totalListAmount > 0
    }

    def "create amendment order with NONE action"() {
        when:
        Map query = OrderQueries.generateAmendmentQuery(subscriptionId)
        HttpResponse generateAmendmentResponse = client.post(GQL_PATH, query)
        Map orderDetail = generateAmendmentResponse.body.data.generateAmendment

        Long effectiveDate = DateTimeHelper.plusMonths(orderDetail.startDate, 12, DateTimeHelper.DEFAULT_TZ)

        Map amendmentMutation = AmendmentGqlSpec.getAmendmentMutationFromOrder(orderDetail, effectiveDate, "NONE", 10)

        HttpResponse amendmentResponse = client.post(GQL_PATH, amendmentMutation)
        Map amendmentOrder = amendmentResponse.body.data.upsertAmendment

        then:
        generateAmendmentResponse.status == 200
        orderDetail.currentSubscription.id == subscriptionId
        orderDetail.currentSubscription.version == 1
        orderDetail.subscriptionTargetVersion == 2

        amendmentResponse.status == 200
        amendmentOrder.currentSubscription.id == subscriptionId
        amendmentOrder.currentSubscription.version == 1
        amendmentOrder.subscriptionTargetVersion == 2
        amendmentOrder.lineItems.size() == 1
        amendmentOrder.lineItems[0].quantity == 10
        amendmentOrder.lineItems[0].amount == 0
        amendmentOrder.totalAmount == 0
        amendmentOrder.lineItemsNetEffect.size() == 0
        amendmentOrder.owner.id == "USER-ADMIN"
        amendmentOrder.totalAmount == 0
        amendmentOrder.totalListAmount == 0
    }

    def "generate cancel order query"() {
        when:
        Map order = OrderSpec.createOrderWithRecurringPlan(client, Recurrence.THREE_YEAR_CYCLE)
        subscriptionId = order.subscriptionId
        Long startDate = order.startDate as Long
        Long cancellationDate = DateTimeHelper.plusMonths(startDate, 15, DateTimeHelper.DEFAULT_TZ)
        Map query = OrderQueries.generateCancelOrderQuery(subscriptionId, cancellationDate)
        HttpResponse generateCancelOrderResponse = client.post(GQL_PATH, query)
        Map cancelOrder = generateCancelOrderResponse.body.data.generateCancelOrder

        then:
        cancelOrder.id == null
        cancelOrder.lineItems.size() == 1
        cancelOrder.lineItems[0].quantity == 0
        cancelOrder.lineItems[0].action == "REMOVE"
        cancelOrder.lineItemsNetEffect.size() == 1
        cancelOrder.lineItemsNetEffect[0].quantity == -10
        cancelOrder.lineItemsNetEffect[0].action == "REMOVE"
        cancelOrder.status == "DRAFT"
        cancelOrder.totalAmount < 0
        cancelOrder.totalListAmount < 0
        cancelOrder.autoRenew == false
    }

    def "generate cancel order query without cancellation date"() {
        when:
        Map query = OrderQueries.generateCancelOrderQuery(subscriptionId, null)
        HttpResponse generateCancelOrderResponse = client.post(GQL_PATH, query)
        Map cancelOrder = generateCancelOrderResponse.body.data.generateCancelOrder

        then:
        cancelOrder.id == null
        cancelOrder.lineItems.size() == 1
        cancelOrder.lineItems[0].quantity == 0
        cancelOrder.lineItems[0].action == "REMOVE"
        cancelOrder.lineItemsNetEffect.size() == 1
        cancelOrder.lineItemsNetEffect[0].quantity == -10
        cancelOrder.lineItemsNetEffect[0].action == "REMOVE"
        cancelOrder.status == "DRAFT"
        cancelOrder.totalAmount < 0
        cancelOrder.totalListAmount < 0
    }

    def "create cancel subscription - dryRun"() {
        when:
        int monthsToCancelAfter = 12
        Map amendmentOrder = createCancelAmendment(monthsToCancelAfter, true)

        then:
        amendmentOrder.id == null
        amendmentOrder.lineItems.size() == 1
        amendmentOrder.lineItems[0].quantity == 0
        amendmentOrder.lineItems[0].action == "REMOVE"
        amendmentOrder.lineItemsNetEffect.size() == 1
        amendmentOrder.lineItemsNetEffect[0].quantity == -10
        amendmentOrder.lineItemsNetEffect[0].action == "REMOVE"
        amendmentOrder.totalAmount < 0
        amendmentOrder.totalListAmount < 0
    }

    def "create cancel subscription - cancel on a later date"() {
        when:
        int monthsToCancelAfter = 12
        Map amendmentOrder = createCancelAmendment(monthsToCancelAfter)
        OrderSpec.executeDraftOrder(client, amendmentOrder.id)
        Map subscription = SubscriptionSpec.getSubscription(client, amendmentOrder.subscriptionId)

        Long startDate = subscription.startDate as Long
        Long cancellationDate = DateTimeHelper.plusMonths(startDate, monthsToCancelAfter, DateTimeHelper.DEFAULT_TZ)

        // try cancellation on a cancelled subscription
        Map cancelMutation = OrderQueries.createCancelMutation(amendmentOrder.subscriptionId, amendmentOrder.billingContactId, cancellationDate)
        HttpResponse cancelResponseOnCancelledSubscription = client.post(GQL_PATH, cancelMutation)

        // try generating amendment on a cancelled order
        Map generateAmendmentQuery = OrderQueries.generateAmendmentQuery(amendmentOrder.subscriptionId)
        HttpResponse generateAmendmentResponseOnCancelledSubscription = client.post(GQL_PATH, generateAmendmentQuery)

        then:
        amendmentOrder.lineItems.size() == 1
        amendmentOrder.lineItems[0].quantity == 0
        amendmentOrder.lineItems[0].action == "REMOVE"
        amendmentOrder.lineItemsNetEffect.size() == 1
        amendmentOrder.lineItemsNetEffect[0].quantity == -10
        amendmentOrder.lineItemsNetEffect[0].action == "REMOVE"
        amendmentOrder.owner.id == "USER-ADMIN"
        subscription.charges.size() > 0
        subscription.canceledDate == cancellationDate
        subscription.charges[0].endDate == cancellationDate

        cancelResponseOnCancelledSubscription.body.data == null
        cancelResponseOnCancelledSubscription.body.errors.size() > 0

        generateAmendmentResponseOnCancelledSubscription.body.data == null
        generateAmendmentResponseOnCancelledSubscription.body.errors.size() > 0
    }

    def "create cancel subscription - cancel with start date"() {
        when:
        Map amendmentOrder = createCancelAmendment(0)
        OrderSpec.executeDraftOrder(client, amendmentOrder.id)
        Map subscription = SubscriptionSpec.getSubscription(client, amendmentOrder.subscriptionId)

        // try cancellation on a cancelled subscription with no charges
        Map cancelMutation = OrderQueries.createCancelMutation(amendmentOrder.subscriptionId, amendmentOrder.billingContactId, 0)
        HttpResponse cancelResponseOnCancelledSubscription = client.post(GQL_PATH, cancelMutation)

        // try generating amendment on a cancelled order
        Map generateAmendmentQuery = OrderQueries.generateAmendmentQuery(amendmentOrder.subscriptionId)
        HttpResponse generateAmendmentResponseOnCancelledSubscription = client.post(GQL_PATH, generateAmendmentQuery)

        then:
        amendmentOrder.lineItems.size() == 1
        amendmentOrder.lineItems[0].quantity == 0
        amendmentOrder.lineItems[0].action == "REMOVE"
        amendmentOrder.lineItemsNetEffect.size() == 1
        amendmentOrder.lineItemsNetEffect[0].quantity == -10
        amendmentOrder.lineItemsNetEffect[0].action == "REMOVE"
        amendmentOrder.owner.id == "USER-ADMIN"
        subscription.charges.size() == 0
        subscription.canceledDate == subscription.startDate

        cancelResponseOnCancelledSubscription.body.data == null
        cancelResponseOnCancelledSubscription.body.errors.size() > 0

        generateAmendmentResponseOnCancelledSubscription.body.data == null
        generateAmendmentResponseOnCancelledSubscription.body.errors.size() > 0
    }

    def "create cancel order on future dated subscription"() {
        when:
        LocalDateTime futureLocalDate = LocalDateTime.ofEpochSecond(orderStartDate, 0, tenantTimeZone.toZoneOffset()).plusMonths(1)
        long futureStartDate = futureLocalDate.toEpochSecond(tenantTimeZone.toZoneOffset())
        def orderId = createOrderWithASpecificDate(futureStartDate)
        OrderSpec.executeDraftOrder(client, orderId)
        Map executedOrder = OrderSpec.getOrder(client, orderId)

        Map query = OrderQueries.generateCancelOrderQuery(executedOrder.subscriptionId, null)
        HttpResponse generateCancelOrderResponse = client.post(GQL_PATH, query)
        Map cancelOrder = generateCancelOrderResponse.body.data.generateCancelOrder

        then:
        orderId != null
        executedOrder.status == "EXECUTED"
        executedOrder.subscriptionId != null
        cancelOrder.startDate == futureStartDate
    }

    def "create cancel order on expired subscription"() {
        when:
        LocalDateTime pastLocalDate = LocalDateTime.ofEpochSecond(orderStartDate, 0, tenantTimeZone.toZoneOffset()).minusYears(4)
        long pastStartDate = pastLocalDate.toEpochSecond(tenantTimeZone.toZoneOffset())
        def orderId = createOrderWithASpecificDate(pastStartDate)
        OrderSpec.executeDraftOrder(client, orderId)
        Map executedOrder = OrderSpec.getOrder(client, orderId)

        Map query = OrderQueries.generateCancelOrderQuery(executedOrder.subscriptionId, null)
        HttpResponse generateCancelOrderResponse = client.post(GQL_PATH, query)
        Map cancelOrder = generateCancelOrderResponse.body.data.generateCancelOrder

        then:
        orderId != null
        executedOrder.status == "EXECUTED"
        executedOrder.subscriptionId != null
        cancelOrder.startDate == pastStartDate
    }

    def "create Amendment on future dated subscription"() {
        when:
        LocalDateTime futureLocalDate = LocalDateTime.ofEpochSecond(orderStartDate, 0, tenantTimeZone.toZoneOffset()).plusMonths(1)
        long futureStartDate = futureLocalDate.toEpochSecond(tenantTimeZone.toZoneOffset())
        def orderId = createOrderWithASpecificDate(futureStartDate)
        OrderSpec.executeDraftOrder(client, orderId)
        Map executedOrder = OrderSpec.getOrder(client, orderId)

        Map query = OrderQueries.generateAmendmentQuery(executedOrder.subscriptionId)
        HttpResponse generateAmendmentResponse = client.post(GQL_PATH, query)
        Map amendment = generateAmendmentResponse.body.data.generateAmendment

        then:
        orderId != null
        executedOrder.status == "EXECUTED"
        executedOrder.subscriptionId != null
        amendment.startDate == futureStartDate
    }

    def "create Amendment on expired subscription"() {
        when:
        LocalDateTime pastLocalDate = LocalDateTime.ofEpochSecond(orderStartDate, 0, tenantTimeZone.toZoneOffset()).minusYears(4)
        long pastStartDate = pastLocalDate.toEpochSecond(tenantTimeZone.toZoneOffset())
        def orderId = createOrderWithASpecificDate(pastStartDate)
        OrderSpec.executeDraftOrder(client, orderId)
        Map executedOrder = OrderSpec.getOrder(client, orderId)

        Map query = OrderQueries.generateAmendmentQuery(executedOrder.subscriptionId)
        HttpResponse generateAmendmentResponse = client.post(GQL_PATH, query)
        Map amendment = generateAmendmentResponse.body.data.generateAmendment

        then:
        orderId != null
        executedOrder.status == "EXECUTED"
        executedOrder.subscriptionId != null
        amendment.startDate == pastStartDate
    }

    def "query order configuration"() {
        when:
        HttpResponse response = client.post(GQL_PATH, TenantSettingRestAndGqlSpec.getPaymentTermSettingsGqlQuery())
        List<String> paymentTerms = response.body.data.paymentTermSettings.defaultPaymentTerms
        boolean customPaymentTermsAllowed = response.body.data.paymentTermSettings.customPaymentTermsAllowed

        then:
        paymentTerms == [
            "NET0",
            "NET30",
            "NET45",
            "NET60",
            "NET90"
        ]
        customPaymentTermsAllowed
    }

    def "query payment term settings"() {
        when:
        HttpResponse response = client.post(GQL_PATH, OrderQueries.getPaymentTermSettings())

        then:
        Map paymentTermSettings = response.body.data.paymentTermSettings
        paymentTermSettings.defaultPaymentTerms == [
            "NET0",
            "NET30",
            "NET45",
            "NET60",
            "NET90"
        ]
        paymentTermSettings.customPaymentTermsAllowed
        paymentTermSettings.defaultPaymentTerm == "NET30"
    }

    def "cannot execute two orders with the same opportunity id"() {
        when:
        def opportunityId = SalesforceCompleteSpec.createTestOpportunity(client)
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        List<Map> plans = new ArrayList<Map>()
        Map createOrderPayload = SalesforceCompleteSpec.getCreateOrderWithOpportunityPayload(opportunityId, client, account.id, contact.id, plans)
        HttpResponse createOrderResponse = client.post(GQL_PATH, createOrderPayload)
        Map order = createOrderResponse.body.data.upsertOrder
        def firstOrderId = order.id

        createOrderResponse = client.post(GQL_PATH, createOrderPayload)
        order = createOrderResponse.body.data.upsertOrder
        def secondOrderId = order.id

        def query = OrderQueries.updateOrderStatusMutation(firstOrderId, "submitted")
        client.post(GQL_PATH, query)
        query = OrderQueries.updateOrderStatusMutation(firstOrderId, "executed")
        client.post(GQL_PATH, query)

        query = OrderQueries.updateOrderStatusMutation(secondOrderId, "submitted")
        client.post(GQL_PATH, query)
        query = OrderQueries.updateOrderStatusMutation(secondOrderId, "executed")
        def tryToExecuteSecondOrderResponse = client.post(GQL_PATH, query)

        SalesforceCompleteSpec.deleteOpportunity(client, opportunityId)

        then:
        tryToExecuteSecondOrderResponse.body.errors.any {
            it.message.contains("already closed")
        }
    }

    def createOrderWithASpecificDate(long startDate) {
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        Map contact = ContactSpec.createAndGetContact(client, account.id).body
        Map plan = PlanSpec.createPlanWithRecurringCharge(client)

        Map query = OrderQueries.createOrderMutation(account.id, contact.id, plan, startDate, startDate, false, null, false)
        HttpResponse createOrderResponse = client.post(GQL_PATH, query)
        Map order = createOrderResponse.body.data.upsertOrder
        return order.id
    }

    def createCancelAmendment(int monthsToCancelAfter, boolean isDryRun = false) {
        Map order = OrderSpec.createOrderWithRecurringPlan(client, Recurrence.THREE_YEAR_CYCLE)
        Long startDate = order.startDate as Long
        Long cancellationDate = DateTimeHelper.plusMonths(startDate, monthsToCancelAfter, DateTimeHelper.DEFAULT_TZ)

        Map cancelMutation = OrderQueries.createCancelMutation(order.subscriptionId, order.billingContactId, cancellationDate, isDryRun)
        HttpResponse cancelResponse = client.post(GQL_PATH, cancelMutation)
        return cancelResponse.body.data.upsertAmendment
    }

    static Map getOrderById(HttpClient client, String orderId) {
        Map query = OrderQueries.getOrderDetailQuery(orderId)
        HttpResponse getOrderResponse = client.post(GQL_PATH, query)
        return getOrderResponse.body.data.orderDetail
    }
}
