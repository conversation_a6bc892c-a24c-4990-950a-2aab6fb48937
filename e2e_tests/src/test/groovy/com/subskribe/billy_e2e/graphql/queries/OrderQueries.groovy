package com.subskribe.billy_e2e.graphql.queries

import com.subskribe.billy_e2e.EnumType
import com.subskribe.billy_e2e.GqlUtils
import com.subskribe.billy_e2e.StringUtils

class OrderQueries {

    static String orderStatusUpdateResponseFragment() {
        /
            id
            status
            orderType
        /
    }

    static String orderDetailFragment(boolean fullApprovalFlow = false) {
        /
            id
            name
            account {
              id
              name
            }
            billingTerm
            paymentTerm
            subscriptionId
            subscriptionTargetVersion
            orderType
            compositeOrderId
            executedOn
            currentSubscription {
                id
                version
            }
            updatedSubscription {
                id
                version
            }
            shippingContact {
              id
              firstName
              lastName
            }
            billingContact {
              id
              firstName
              lastName
            }
            predefinedDiscounts {
                id
                name
                type
                description
                percent
            }
            creditableAmounts {
                subscriptionChargeId
                creditableAmount
                maxCreditableAmount
            }
            currency
            startDate
            endDate
            billingAnchorDate
            termLength {
              cycle
              step
            }
            billingCycle {
              cycle
              step
            }
            currentSubscription {
                id
                version
                state
                startDate
                endDate
                billingCycle {
                    cycle
                    step
                }
            }
            customBillingEligibleOrderLineIds
            renewalForSubscription {
                id
                version
                state
                startDate
                endDate
                billingCycle {
                    cycle
                    step
                }
            }
            lineItems {
                ${orderItemDetailFragment}
            }
            lineItemsNetEffect {
                ${orderItemDetailFragment}
            }
            status
            totalListAmount
            taxEstimate
            totalAmount
            totalDiscount
            totalDiscountPercent
            deltaArrPercent
            rampInterval
            orderFormTemplates {
                id
                name
                description
                content
            }
            sfdcOpportunityId
            sfdcOpportunityName
            sfdcOpportunityType
            sfdcOpportunityStage
            isPrimaryOrderForSfdcOpportunity
            owner {
                id
            }
            purchaseOrderNumber
            purchaseOrderRequiredForInvoicing
            ${fullApprovalFlow ? fullApprovalFlowFragment : partialApprovalFlowFragment}
            autoRenew
            approvalSegment {
                id
                name
                description
                createdOn
                updatedOn
            }
            validApprovalSegments {
                id
                name
                description
                createdOn
                updatedOn
            }
            submittedBy {
                id
                displayName
            }
            orderMetrics {
                entryArr
                exitArr
                averageArr
            }
            customFields {
                id
                name
                value
            }
            customPredefinedTemplatesOnOrder {
                ${customPredefinedTemplateOnOrderFragment}
            }  
            ${zeppaOutputFragment}
        /
    }

    static String orderItemDetailFragment =
    /
        action
        id
        isDryRunItem
        plan {
            id
            name
            productId
        }
        charge {
            id
            name
            isListPriceEditable
        }
        subscriptionChargeId
        quantity
        effectiveDate
        endDate
        discountAmount
        discounts {
            name
            percent
            discountAmount
            amount
        }
        predefinedDiscounts {
            id
            name
            type
            description
            percent
            amount
        }
        attributeReferences {
            attributeDefinitionId
            attributeValue
        }
        listUnitPrice
        sellUnitPrice
        listAmount
        taxEstimate
        amount
        annualizedAmount
        currencyConversionRateId
        isCreditable
        metrics {
            arr
            arrWithoutOverride
        }
        arrOverride
        availableReplacementPlan {
            id
            name
        }
        replacedPlan {
            id
            name
        }
        recognitionRule {
            id
            recognitionType
        } 
    /

    static String partialApprovalFlowFragment =
    /
        approvalFlows {
            id
            orderId
            approvalStatus
            workflowStatus
            note
            version
        }
    /

    static String fullApprovalFlowFragment =
    /
        approvalFlows {
            id
            orderId
            submitterNote {
                id
                orderId
                note
            }
            version
            approvalStatus
            workflowStatus
            note
            createdOn
            updatedOn
            approvalFlowInstances {
                id
                activeStateId
                status
                data {
                    id
                    name
                    description
                    status
                    states {
                        id
                        name
                        status
                        action {
                            emailGroupId
                        }
                        approvalGroup {
                            id
                            name
                            isUser
                            isRole
                            users {
                                id
                                displayName
                            }
                        }
                        approvalGroupDetail {
                            id
                            name
                            isUser
                            isRole
                            isUserGroup
                            approvalRoleResolvedFrom {
                                id
                                name
                                description
                                createdOn
                                updatedOn
                            }
                            users {
                                id
                                displayName
                            }
                        }
                        approvedBy {
                            user {
                                id
                                displayName
                            }
                            time
                            note
                        }
                        rejectedBy {
                            user {
                                id
                                displayName
                            }
                            time
                            note
                        }
                    }
                    transitionRules {
                        id
                        name
                        fromState {
                            id
                            name
                        }
                        toState {
                            id
                            name
                        }
                        condition
                    }
                }
            }
        }
    /

    static String customPredefinedTemplateOnOrderFragment =
    /
        id
        orderId
        name
        description
        content
    /

    static String zeppaOutputFragment =
    /
             zeppaOutput {
                customizationRunSkipped
                customizationDefinitionMissing
                ruleTraces {
                     ruleName
                     fired
                     orderActionsPerformed {
                          action
                          actionMessage
                     }
                     orderLineActionsPerformed {
                          lineIdentifier
                          actionsPerformed {
                               action
                               actionMessage
                          }
                     }
                     orderRuleWarnings
                     orderLineRuleWarnings {
                          lineIdentifier
                          warnings
                     }   
                } 
            }
    /

    static def getOrderDetailQuery(String orderId) {
        String fields = /{
            ${orderDetailFragment(true)}
        }/
        return [query: GqlUtils.query("orderDetail", [id: orderId], fields)]
    }

    static def createOrderMutation(String accountId,
            String contactId,
            Map plan,
            Long startDate,
            Long lineItemEffectiveDate,
            boolean isDryRun = false,
            String templateId = null,
            boolean sanitizeLineItemDates = false,
            String attachmentId = null,
            String ownerId = "USER-ADMIN",
            String purchaseOrderNumber = null,
            boolean purchaseOrderRequiredForInvoicing = false,
            long quantity = 10
    ) {
        String contactIdString = StringUtils.isBlank(contactId) ? null : String.format("\"%s\"", contactId)

        return [
            query: /mutation {
                    upsertOrder(
                        order: {
                            accountId: "${accountId}",
                            name: "create order test",
                            shippingContactId: ${contactIdString},
                            billingContactId: ${contactIdString},
                            orderType: NEW,
                            paymentTerm: "NET30",
                            lineItems: [
                                {
                                    effectiveDate: ${lineItemEffectiveDate}
                                    planId: "${plan.id}",
                                    chargeId: "${plan.charges.get(0).id}",
                                    quantity: ${quantity},
                                    isDryRunItem: ${isDryRun},
                                    discounts: [
                                        {
                                            name: "default",
                                            percent: 0.2
                                        }
                                    ]
                                }
                            ],
                            startDate: ${startDate},
                            termLength: {
                                cycle: YEAR,
                                step: 3
                            },
                            billingCycle: {
                                cycle: YEAR,
                                step: 1
                            },
                            billingTerm: UP_FRONT,
                            orderFormTemplateIds: [${templateId ? "\"" + templateId + "\"" : ""}],
                            sfdcOpportunityId: "${"TEST_OP_ID" + StringUtils.getRandomString(10)}",
                            isPrimaryOrderForSfdcOpportunity: true,
                            sfdcOpportunityName: "TEST_OP_NAME",
                            sfdcOpportunityType: "TEST_OP_TYPE",
                            sfdcOpportunityStage: "TEST_OP_STAGE",
                            ownerId: "${ownerId}"
                            purchaseOrderNumber: "${purchaseOrderNumber}",
                            purchaseOrderRequiredForInvoicing: ${purchaseOrderRequiredForInvoicing},
                            autoRenew: true,
                            attachmentId: ${attachmentId ? "\"" + attachmentId + "\"" : "null"}
                        },
                        isDryRun: ${isDryRun},
                        sanitizeLineItemDates: ${sanitizeLineItemDates}
                    ){
                        ${orderDetailFragment()}
                    }
                }/
        ]
    }

    static def createOrderMutationWithCustomEndDate(String accountId,
            String contactId,
            Map plan,
            Long startDate,
            Long customEndDate,
            Long lineItemEffectiveDate,
            boolean isDryRun = false,
            String templateId = null,
            boolean sanitizeLineItemDates = false,
            String ownerId = "USER-ADMIN",
            String purchaseOrderNumber = null,
            boolean purchaseOrderRequiredForInvoicing = false,
            long quantity = 10) {
        return [
            query: /mutation {
                    upsertOrder(
                        order: {
                            accountId: "${accountId}",
                            name: "create order test",
                            shippingContactId: "${contactId}",
                            billingContactId: "${contactId}",
                            orderType: NEW,
                            paymentTerm: "NET30",
                            startDate: ${startDate},
                            endDate: ${customEndDate},
                            billingCycle: {
                                cycle: YEAR,
                                step: 1
                            },
                            billingTerm: UP_FRONT,
                            orderFormTemplateIds: [${templateId ? "\"" + templateId + "\"" : ""}],
                            sfdcOpportunityId: "TEST_OP_ID",
                            isPrimaryOrderForSfdcOpportunity: true,
                            sfdcOpportunityName: "TEST_OP_NAME",
                            sfdcOpportunityType: "TEST_OP_TYPE",
                            sfdcOpportunityStage: "TEST_OP_STAGE",
                            ownerId: "${ownerId}"
                            purchaseOrderNumber: "${purchaseOrderNumber}",
                            purchaseOrderRequiredForInvoicing: ${purchaseOrderRequiredForInvoicing},
                            autoRenew: true,
                            lineItems: [
                                {
                                    effectiveDate: ${lineItemEffectiveDate}
                                    planId: "${plan.id}",
                                    chargeId: "${plan.charges.get(0).id}",
                                    quantity: ${quantity}
                                    discounts: [
                                        {
                                            name: "default",
                                            percent: 0.2
                                        }
                                    ]
                                }
                            ],
                        },
                        isDryRun: ${isDryRun},
                        sanitizeLineItemDates: ${sanitizeLineItemDates}
                    ){
                        ${orderDetailFragment()}
                    }
                }/
        ]
    }

    static def createOrderMutationWithPredefinedDiscounts(String orderId, String accountId, String contactId, Map plan, Long startDate, Map predefinedDiscount, isDryRun = false) {
        String orderIdString = null
        if (orderId != null) {
            orderIdString = "${orderId}"
        }

        return [
            query: /mutation {
                    upsertOrder(
                        order: {
                            id: ${orderIdString},
                            accountId: "${accountId}",
                            name: "create order test",
                            shippingContactId: "${contactId}",
                            billingContactId: "${contactId}",
                            orderType: NEW,
                            paymentTerm: "NET30",
                            predefinedDiscounts: [
                                {
                                    id: "${predefinedDiscount.id}",
                                    percent: ${predefinedDiscount.percent}
                                }
                            ]
                            lineItems: [
                                {
                                    planId: "${plan.id}",
                                    chargeId: "${plan.charges[0].id}",
                                    quantity: 10
                                    discounts: [
                                        {
                                            name: "default",
                                            percent: 0.2
                                        }
                                    ],
                                    predefinedDiscounts: ["${predefinedDiscount.id}"]
                                }
                            ],
                            startDate: ${startDate},
                            termLength: {
                                cycle: YEAR,
                                step: 3
                            },
                            billingCycle: {
                                cycle: YEAR,
                                step: 1
                            },
                            billingTerm: UP_FRONT
                        },
                        isDryRun: ${isDryRun}
                    ){
                        ${orderDetailFragment()}
                    }
                }/
        ]
    }

    static def updateOrderStatusMutation(String orderId, String status) {
        Map arguments = [
            orderId        : orderId,
            status         : status,
            statusUpdatedOn: 1689893629
        ]
        String fields = /{
            ${orderStatusUpdateResponseFragment()}
        }/
        return [query: GqlUtils.mutation("updateOrderStatus", arguments, fields)]
    }

    static def generateRenewalQuery(String subscriptionId) {
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.query("generateRenewalOrder", [subscriptionId: subscriptionId], fields)]
    }

    static def renewOrderMutation(String subscriptionId, long startDate, boolean isDryRun = false, String ownerId = "USER-ADMIN") {
        Map arguments = [
            order   : [
                orderType               : new EnumType("RENEWAL"),
                renewalForSubscriptionId: subscriptionId,
                termLength              : [
                    cycle: new EnumType("YEAR"),
                    step : 3
                ],
                startDate               : startDate,
                ownerId                 : ownerId
            ],
            isDryRun: isDryRun
        ]
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.mutation("upsertRenewalOrder", arguments, fields)]
    }

    static def duplicateOrderQuery(String orderId) {
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.query("duplicateOrder", [orderId: orderId], fields)]
    }

    static def generateAmendmentQuery(String subscriptionId) {
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.query("generateAmendment", [subscriptionId: subscriptionId], fields)]
    }

    static def generateCancelOrderQuery(String subscriptionId, Long effectiveDate) {
        Map arguments = [
            subscriptionId: subscriptionId,
            effectiveDate : effectiveDate
        ]
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.query("generateCancelOrder", arguments, fields)]
    }

    static def createAmendmentMutation(String subscriptionId, String contactId, String planId, String chargeId, String subscriptionChargeId, Long effectiveDate, String lineAction, int quantity, boolean isDryRun = false, String ownerId = "USER-ADMIN", String purchaseOrderNumber = null, boolean purchaseOrderRequiredForInvoicing = false) {
        Map arguments = [
            order   : [
                orderType                        : new EnumType("AMENDMENT"),
                subscriptionId                   : subscriptionId,
                shippingContactId                : contactId,
                billingContactId                 : contactId,
                lineItems                        : [
                    [
                        action              : new EnumType(lineAction),
                        subscriptionChargeId: subscriptionChargeId,
                        planId              : planId,
                        chargeId            : chargeId,
                        quantity            : quantity,
                    ]
                ],
                startDate                        : effectiveDate,
                ownerId                          : ownerId,
                sfdcOpportunityId                : "123",
                sfdcOpportunityName              : "sfdc name",
                purchaseOrderNumber              : purchaseOrderNumber,
                purchaseOrderRequiredForInvoicing: purchaseOrderRequiredForInvoicing
            ],
            isDryRun: isDryRun
        ]
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.mutation("upsertAmendment", arguments, fields)]
    }

    static def createAmendmentMutationWithAttributeReferences(String subscriptionId, String contactId, String planId, String chargeId, String subscriptionChargeId, List attributeReferences, Long effectiveDate, String lineAction, int quantity, boolean isDryRun = false, String ownerId = "USER-ADMIN", String purchaseOrderNumber = null, boolean purchaseOrderRequiredForInvoicing = false) {
        Map arguments = [
            order   : [
                orderType                        : new EnumType("AMENDMENT"),
                subscriptionId                   : subscriptionId,
                shippingContactId                : contactId,
                billingContactId                 : contactId,
                lineItems                        : [
                    [
                        action              : new EnumType(lineAction),
                        subscriptionChargeId: subscriptionChargeId,
                        planId              : planId,
                        chargeId            : chargeId,
                        quantity            : quantity,
                        attributeReferences : attributeReferences
                    ]
                ],
                startDate                        : effectiveDate,
                ownerId                          : ownerId,
                sfdcOpportunityId                : "123",
                sfdcOpportunityName              : "sfdc name",
                purchaseOrderNumber              : purchaseOrderNumber,
                purchaseOrderRequiredForInvoicing: purchaseOrderRequiredForInvoicing
            ],
            isDryRun: isDryRun
        ]
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.mutation("upsertAmendment", arguments, fields)]
    }

    static def createCancelMutation(String subscriptionId, String contactId, Long effectiveDate, boolean isDryRun = false, String ownerId = "USER-ADMIN") {
        Map arguments = [
            order   : [
                orderType        : new EnumType("CANCEL"),
                subscriptionId   : subscriptionId,
                shippingContactId: contactId,
                billingContactId : contactId,
                startDate        : effectiveDate,
                ownerId          : ownerId
            ],
            isDryRun: isDryRun
        ]
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.mutation("upsertAmendment", arguments, fields)]
    }

    static Map getPaymentTermSettings() {
        String fields = /{
            defaultPaymentTerm
            defaultPaymentTerms
            customPaymentTermsAllowed
        }/
        return [query: GqlUtils.query("paymentTermSettings", null, fields)]
    }

    static def deleteOrderMutation(String orderId) {
        String fields = /{
            ${orderDetailFragment()}
        }/
        return [query: GqlUtils.mutation("deleteOrder", [id: orderId], fields)]
    }

    static def createOrderWithAttributeReferenceMutation(String accountId,
            String contactId,
            Map plan,
            Long startDate,
            Long lineItemEffectiveDate,
            boolean isDryRun = false,
            String templateId = null,
            boolean sanitizeLineItemDates = false,
            List attributeReferences,
            String attachmentId = null,
            String ownerId = "USER-ADMIN",
            String purchaseOrderNumber = null,
            boolean purchaseOrderRequiredForInvoicing = false,
            long quantity = 10) {
        String contactIdString = StringUtils.isBlank(contactId) ? null : String.format("\"%s\"", contactId)
        return [
            query: /mutation {
                    upsertOrder(
                        order: {
                            accountId: "${accountId}",
                            name: "create order test",
                            shippingContactId: ${contactIdString},
                            billingContactId: ${contactIdString},
                            orderType: NEW,
                            paymentTerm: "NET30",
                            lineItems: [
                                {
                                    effectiveDate: ${lineItemEffectiveDate},
                                    planId: "${plan.id}",
                                    chargeId: "${plan.charges.get(0).id}",
                                    quantity: ${quantity},
                                    attributeReferences: [
                                        {
                                            attributeDefinitionId: "${attributeReferences[0].attributeDefinitionId}",
                                            attributeValue       : "${attributeReferences[0].attributeValue}"
                                        },
                                        {
                                            attributeDefinitionId: "${attributeReferences[1].attributeDefinitionId}",
                                            attributeValue       : "${attributeReferences[1].attributeValue}"
                                        }
                                    ]
                                }
                            ],
                            startDate: ${startDate},
                            termLength: {
                                cycle: YEAR,
                                step: 3
                            },
                            billingCycle: {
                                cycle: YEAR,
                                step: 1
                            },
                            billingTerm: UP_FRONT,
                            orderFormTemplateIds: [${templateId ? "\"" + templateId + "\"" : ""}],
                            sfdcOpportunityId: "TEST_OP_ID",
                            isPrimaryOrderForSfdcOpportunity: true,
                            sfdcOpportunityName: "TEST_OP_NAME",
                            sfdcOpportunityType: "TEST_OP_TYPE",
                            sfdcOpportunityStage: "TEST_OP_STAGE",
                            ownerId: "${ownerId}"
                            purchaseOrderNumber: "${purchaseOrderNumber}",
                            purchaseOrderRequiredForInvoicing: ${purchaseOrderRequiredForInvoicing},
                            autoRenew: true,
                            attachmentId: ${attachmentId ? "\"" + attachmentId + "\"" : "null"}
                        },
                        isDryRun: ${isDryRun},
                        sanitizeLineItemDates: ${sanitizeLineItemDates}
                    ){
                        ${orderDetailFragment()}
                    }
                }/
        ]
    }

    static Map getIsAmendmentCurrentQuery(String orderId) {
        Map arguments = [
            orderId: orderId
        ]

        return [query: GqlUtils.query("isAmendmentCurrent", arguments, null)]
    }

    static def createOrderMutationWithOrderCurrencyAndCustomEndDate(String accountId,
            String contactId,
            Map plan,
            Long startDate,
            Long customEndDate,
            Long lineItemEffectiveDate,
            boolean isDryRun = false,
            String templateId = null,
            boolean sanitizeLineItemDates = false,
            String attachmentId = null,
            String ownerId = "USER-ADMIN",
            String purchaseOrderNumber = null,
            boolean purchaseOrderRequiredForInvoicing = false,
            long quantity = 10,
            String currency = "USD"
    ) {
        String contactIdString = StringUtils.isBlank(contactId) ? null : String.format("\"%s\"", contactId)

        return [
            query: /mutation {
                upsertOrder(
                    order: {
                        accountId: "${accountId}",
                        name: "create order test",
                        shippingContactId: ${contactIdString},
                        billingContactId: ${contactIdString},
                        orderType: NEW,
                        paymentTerm: "NET30",
                        lineItems: [
                            {
                                effectiveDate: ${lineItemEffectiveDate}
                                planId: "${plan.id}",
                                chargeId: "${plan.charges.get(0).id}",
                                quantity: ${quantity},
                                isDryRunItem: ${isDryRun},
                                discounts: [
                                    {
                                        name: "default",
                                        percent: 0.2
                                    }
                                ]
                            }
                        ],
                        startDate: ${startDate},
                        endDate: ${customEndDate},
                        billingCycle: {
                            cycle: YEAR,
                            step: 1
                        },
                        billingTerm: UP_FRONT,
                        orderFormTemplateIds: [${templateId ? "\"" + templateId + "\"" : ""}],
                        sfdcOpportunityId: "${"TEST_OP_ID" + StringUtils.getRandomString(10)}",
                        isPrimaryOrderForSfdcOpportunity: true,
                        sfdcOpportunityName: "TEST_OP_NAME",
                        sfdcOpportunityType: "TEST_OP_TYPE",
                        sfdcOpportunityStage: "TEST_OP_STAGE",
                        ownerId: "${ownerId}"
                        purchaseOrderNumber: "${purchaseOrderNumber}",
                        purchaseOrderRequiredForInvoicing: ${purchaseOrderRequiredForInvoicing},
                        autoRenew: true,
                        attachmentId: ${attachmentId ? "\"" + attachmentId + "\"" : "null"},
                        currency: "${currency}"
                    },
                    isDryRun: ${isDryRun},
                    sanitizeLineItemDates: ${sanitizeLineItemDates}
                ){
                    ${orderDetailFragment()}
                }
            }/
        ]
    }

    static def upsertCustomPredefinedTemplateOnOrderMutation(String orderId, String templateId, String name, String description, String content) {
        Map arguments = [
            documentTemplate: [
                id         : templateId,
                orderId    : orderId,
                name       : name,
                description: description,
                content    : content
            ]
        ]
        String fields = /{
            ${customPredefinedTemplateOnOrderFragment}
        }/
        return [query: GqlUtils.mutation("upsertCustomPredefinedTemplateOnOrder", arguments, fields)]
    }
}
