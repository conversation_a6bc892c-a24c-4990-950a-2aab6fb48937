package com.subskribe.billy_e2e.utils

import static com.subskribe.billy_e2e.accounting.BaseAccountingSpec.pumpEvents
import static com.subskribe.billy_e2e.utils.Authenticated.getCurrentTenant
import static com.subskribe.billy_e2e.utils.TenantJobWait.jobsExistAndCompleted

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import groovy.json.JsonSlurper
import java.time.Duration
import java.time.Instant
import java.util.logging.Logger
import spock.util.concurrent.PollingConditions

class EventControl {

    private static final Logger LOGGER = Logger.getLogger("EventControl")
    private static boolean eventingReady = false

    static void pumpEventsTimed(HttpClient client) {
        Instant start = Instant.now()
        pumpEvents(client)
        Duration duration = Duration.between(start, Instant.now())
        LOGGER.fine("pumpEvents took ${duration.toMillis() / 1000} seconds")
    }

    static void eventuallyWithEvents(HttpClient client, int timeout, Closure condition, initialDelay = 0.5, factor = 1) {
        waitEventingToBeReady(client)
        new PollingConditions(timeout: timeout, initialDelay: initialDelay, factor: factor).eventually {
            pumpEventsTimed(client)
            condition.run()
        }
    }

    static void waitEventingToBeReady(HttpClient client) {
        if (eventingReady) {
            return
        }

        String tenantId = getCurrentTenant(client).tenantId
        new PollingConditions(timeout: 120, initialDelay: 1, factor: 1).eventually {
            pumpEventsTimed(client)
            assert jobsExistAndCompleted(client, "TENANT_CREATED", tenantId, 1)
            eventingReady = true
        }
    }

    static List<Map<String, Object>> getEvents(HttpClient client, String eventType) {
        String url = "/admin/local/events?eventType=${eventType}"
        HttpResponse response = client.get(url)
        return response.body.collect { event ->
            event.payload = new JsonSlurper().parseText(new String(Base64.decoder.decode(event.payload)))
            return event
        }.sort { event -> event.sequenceNumber }
    }
}
