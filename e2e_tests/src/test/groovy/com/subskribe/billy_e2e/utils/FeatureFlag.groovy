package com.subskribe.billy_e2e.utils

import com.subskribe.billy_e2e.HttpClient

class FeatureFlag {

    static final String INVOICE_ON_ACTIVATION_FEATURE = "INVOICE_ON_ACTIVATION"

    static void enableFeature(HttpClient client, String featureName) {
        updateFlag(client, featureName, true)
    }

    static void disableFeature(HttpClient client, String featureName) {
        updateFlag(client, featureName, false)
    }

    static boolean updateFlag(HttpClient client, String featureName, boolean value) {
        String url = "/admin/local/setFeature/${featureName}?value=${value.toString()}"
        def response = client.putNoBody(url)
        if (response.status != 200) {
            throw new IllegalStateException("could not flip feature flag ${featureName}, service returned ${response.status} body ${response.body}")
        }
        return true
    }
}
