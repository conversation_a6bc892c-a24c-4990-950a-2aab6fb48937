package com.subskribe.billy_e2e.utils


import com.subskribe.billy_e2e.HttpClient
import spock.util.concurrent.PollingConditions
class TenantJobWait {

    private static final String TENANT_ACTIVE_JOB_GET_ENDPOINT = "/tenantJobs/test/findActiveJob"
    private static final String TENANT_JOBS_GET_ENDPOINT = "/tenantJobs/test/findJobs"

    static def waitUntilNoActiveJobs(HttpClient client, String jobType, String objectId) {
        new PollingConditions(timeout: 60, initialDelay: 1, factor: 1).eventually {
            EventControl.pumpEventsTimed(client)
            assert isNoActiveJobs(client, jobType, objectId)
        }
    }
    static def waitUntilJobExistsAndComplete(HttpClient client, String jobType, String objectId, int minimumJobs = 1) {
        new PollingConditions(timeout: 60, initialDelay: 1, factor: 1).eventually {
            EventControl.pumpEventsTimed(client)
            assert jobsExistAndCompleted(client, jobType, objectId, minimumJobs)
        }
    }

    private static boolean jobsExistAndCompleted(HttpClient client, String jobType, String objectId, int minimumJobs) {
        def jobResponse = client.getWithParams(TENANT_JOBS_GET_ENDPOINT, [
            jobType: jobType,
            objectId: objectId
        ])

        List<Map> jobs = jobResponse.body

        if (jobs.size() < minimumJobs) {
            return false
        }

        if (jobs.findAll {it.status != "SUCCESSFUL"}.size() > 0) {
            return false
        }

        return true
    }

    private static boolean isNoActiveJobs(HttpClient client, String jobType, String objectId) {
        def jobResponse = client.getWithParams(TENANT_ACTIVE_JOB_GET_ENDPOINT, [
            jobType: jobType,
            objectId: objectId
        ])
        if (jobResponse.status == 404) {
            return true
        }
        if (jobResponse.body.status != "COMPLETED") {
            return false
        }
        return true
    }
}
