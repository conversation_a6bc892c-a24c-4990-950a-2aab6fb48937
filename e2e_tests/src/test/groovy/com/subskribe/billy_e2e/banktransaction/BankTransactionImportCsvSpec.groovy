package com.subskribe.billy_e2e.banktransaction

import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.MultipartHttpClient
import com.subskribe.billy_e2e.PaymentSpec
import com.subskribe.billy_e2e.accounting.AccountingPeriodSpec
import com.subskribe.billy_e2e.invoice.BaseInvoiceSpec
import com.subskribe.billy_e2e.order.OrderSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.usage.UsageSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.FeatureFlag
import com.subskribe.billy_e2e.utils.Recurrence
import java.time.Instant
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.stream.Collectors
import okhttp3.Response
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class BankTransactionImportCsvSpec extends Authenticated {

    @Shared
    def paymentBankAccount

    @Shared
    String subscriptionId

    @Shared
    long subscriptionStartDate

    @Shared
    String usageChargeId

    @Shared
    private TimeZone tenantTimeZone

    @Shared
    private String draftInvoiceNumber

    @Shared
    private long targetDate

    @Shared
    private String invoiceNumber

    @Shared
    Map invoice

    @Shared
    String bankTransactionId

    private static final String BANK_TRANSACTION_IMPORT_PATH = "/bankTransactions/csv"
    private static final String BANK_TRANSACTION_LIST_PATH = "/bankTransactions/potentialInvoices"
    protected static final String QUERY_ENDPOINT = "/admin/local/runQuery"
    static final String BANK_TRANSACTION_MATCH_PATH = "/bankTransactions/match"
    private static final String BANK_TRANSACTION_CSV_RESOURCE_PATH = "/banktransactions/BankTransactions.csv"
    private static final String BANK_TRANSACTION_CANADA_CSV_RESOURCE_PATH = "/banktransactions/BankTransactionsCanada.csv"
    private static final String BANK_TRANSACTION_MISSING_REQUIRED_FIELDS_CSV_PATH = "/banktransactions/BankTransactionsMissingRequiredFields.csv"
    private static final int NUM_OF_MONTHS_RETROACTIVE = 9
    private static final String CSV_HEADERS = "BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber,TransactionType,PaymentType\n"

    def setupSpec() {
        String timeZoneString = getTenantTimeZoneSetting(client)
        tenantTimeZone = TimeZone.getTimeZone(timeZoneString)

        subscriptionStartDate = DateTimeHelper.plusMonths(DateTimeHelper.getClosestNonMonthEndInstant(), -NUM_OF_MONTHS_RETROACTIVE, tenantTimeZone)
    }

    def "upload bank transactions csv, non-existent bank account"() {
        when:
        Response uploadBankTransactionCsvResponse = uploadBankTransactionCsv(BANK_TRANSACTION_CSV_RESOURCE_PATH, "")
        Map importData
        if (uploadBankTransactionCsvResponse.successful) {
            importData = MultipartHttpClient.parseMultiPartResponseBody(uploadBankTransactionCsvResponse)
        }
        then:
        uploadBankTransactionCsvResponse.code() == 200
        importData != null
        importData.bankTransactionsCount == 3
        importData.failedBankTransactionsCount == 3
        importData.successfulBankTransactionsCount == 0
        importData.bankTransactionsUploadData[0].failed == true
        importData.bankTransactionsUploadData[0].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[1].failed == true
        importData.bankTransactionsUploadData[1].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[2].failed == true
        importData.bankTransactionsUploadData[2].failureReason.isEmpty() == false
    }

    def "create bank account"() {
        when:
        paymentBankAccount = PaymentSpec.createPaymentBankAccount(client, [entityId])

        then:
        paymentBankAccount.id.isEmpty()==false
    }

    def "upload bank transactions csv, create bank account"() {
        when:
        Response uploadBankTransactionCsvResponse = uploadBankTransactionCsv(BANK_TRANSACTION_CSV_RESOURCE_PATH, paymentBankAccount.id)
        Map importData
        if (uploadBankTransactionCsvResponse.successful) {
            importData = MultipartHttpClient.parseMultiPartResponseBody(uploadBankTransactionCsvResponse)
        }
        then:
        uploadBankTransactionCsvResponse.code() == 200
        importData != null
        importData.bankTransactionsCount == 3
        importData.failedBankTransactionsCount == 0
        importData.successfulBankTransactionsCount == 3
        importData.bankTransactionsUploadData[0].failed == false
        importData.bankTransactionsUploadData[0].failureReason.isEmpty() == true
        importData.bankTransactionsUploadData[1].failed == false
        importData.bankTransactionsUploadData[1].failureReason.isEmpty() == true
        importData.bankTransactionsUploadData[2].failed == false
        importData.bankTransactionsUploadData[2].failureReason.isEmpty() == true
    }

    def "upload bank transactions csv, currency mismatch"() {
        when:
        Response uploadBankTransactionCsvResponse = uploadBankTransactionCsv(BANK_TRANSACTION_CANADA_CSV_RESOURCE_PATH, paymentBankAccount.id)
        Map importData
        if (uploadBankTransactionCsvResponse.successful) {
            importData = MultipartHttpClient.parseMultiPartResponseBody(uploadBankTransactionCsvResponse)
        }
        then:
        uploadBankTransactionCsvResponse.code() == 200
        importData != null
        importData.bankTransactionsCount == 3
        importData.failedBankTransactionsCount == 3
        importData.successfulBankTransactionsCount == 0
        importData.bankTransactionsUploadData[0].failed == true
        importData.bankTransactionsUploadData[0].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[1].failed == true
        importData.bankTransactionsUploadData[1].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[2].failed == true
        importData.bankTransactionsUploadData[2].failureReason.isEmpty() == false
    }

    def "upload bank transactions csv, missing required fields"() {
        when:
        Response uploadBankTransactionCsvResponse = uploadBankTransactionCsv(BANK_TRANSACTION_MISSING_REQUIRED_FIELDS_CSV_PATH, "")
        Map importData
        if (uploadBankTransactionCsvResponse.successful) {
            importData = MultipartHttpClient.parseMultiPartResponseBody(uploadBankTransactionCsvResponse)
        }
        then:
        uploadBankTransactionCsvResponse.code() == 200
        importData != null
        importData.bankTransactionsCount == 3
        importData.failedBankTransactionsCount == 3
        importData.successfulBankTransactionsCount == 0
        importData.bankTransactionsUploadData[0].failed == true
        importData.bankTransactionsUploadData[0].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[1].failed == true
        importData.bankTransactionsUploadData[1].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[2].failed == true
        importData.bankTransactionsUploadData[2].failureReason.isEmpty() == false
    }

    def "potential bank transaction invoices listing - nothing to list"() {
        when:
        HttpResponse response = client.get(BANK_TRANSACTION_LIST_PATH)

        then:
        response.status==200
        response.body.pageToken==null
        response.body.data.size()==0
        response.body.count==0
    }

    def "create order with usage charge"() {
        when:
        def unitPrice = new BigDecimal("100.0")
        Map plan = PlanSpec.createPlanWithUsageCharge(client, unitPrice, [shouldTrackArr: true])
        Map order = OrderSpec.createOrderWithUsagePlanWithBillingCycle(client, plan, subscriptionStartDate, Recurrence.ONE_QUARTER_CYCLE, Recurrence.ONE_MONTH_CYCLE)
        subscriptionId = order.subscriptionId
        usageChargeId = order.lineItems[0].chargeId

        then:
        order != null
        order.status == "EXECUTED"
        order.startDate == subscriptionStartDate
        order.lineItems.get(0).listUnitPrice == unitPrice
        order.lineItems.get(0).sellUnitPrice == unitPrice
        subscriptionId != null
        subscriptionStartDate != null
        usageChargeId != null
    }

    def "add usage records for the first month and create invoice"() {
        when:
        def periodStart = DateTimeHelper.plusDays(subscriptionStartDate, 1, tenantTimeZone)
        def dataList = []
        dataList.add(UsageSpec.rawUsage(null, subscriptionId, usageChargeId, periodStart, 100))
        def usageResponse = UsageSpec.addRawUsage(client, dataList)
        def usageAggregationResponse = UsageSpec.performUsageAggregationOnDemand(client)
        targetDate = DateTimeHelper.plusMonths(subscriptionStartDate, 1, tenantTimeZone)

        def response = client.post(BaseInvoiceSpec.getGenerateInvoicePathWithInvoiceDate(subscriptionId, targetDate, targetDate, BaseInvoiceSpec.InvoiceChargeInclusionOption.INCLUDE_USAGE), [:])
        invoice = response.body
        draftInvoiceNumber = invoice.invoiceNumber

        then:
        usageResponse.status == 200
        usageResponse.body.rawUsagesTotal == 1
        usageAggregationResponse.status == 200
        invoice != null
        draftInvoiceNumber != null
        invoice.total == 10000 // $100 per unit per month * 100 units
    }

    def "post first period draft invoice"() {
        when:
        def postResponse = BaseInvoiceSpec.postInvoice(client, draftInvoiceNumber)
        invoiceNumber = postResponse.body.invoiceNumber

        then:
        postResponse.status == 200
    }

    def "bank transaction potential invoices"() {
        when:
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss")
        def csvRow =
        List.of(
        paymentBankAccount.id,
        "EXT-0001",
        String.valueOf(invoice.total),
        "USD",
        DateTimeHelper.epochSecondsToLocalDateTime(targetDate).format(formatter),
        "Payer name",
        invoiceNumber,
        "Wire",
        "Deposit"
        ).stream().collect(Collectors.joining(","))

        def csvContent = CSV_HEADERS + csvRow

        Response uploadBankTransactionCsvResponse =
        multiPartClient.multiPartFilePost(BANK_TRANSACTION_IMPORT_PATH, csvContent.getBytes(), "BankTransaction", "text/csv")
        Map importData
        if (uploadBankTransactionCsvResponse.successful) {
            importData = MultipartHttpClient.parseMultiPartResponseBody(uploadBankTransactionCsvResponse)
        }
        then:
        uploadBankTransactionCsvResponse.code() == 200
        importData != null
        importData.bankTransactionsCount == 1
        importData.failedBankTransactionsCount == 0
        importData.successfulBankTransactionsCount == 1
        importData.bankTransactionsUploadData[0].failed == false
        importData.bankTransactionsUploadData[0].failureReason.isEmpty() == true
    }

    def "potential invoices - transaction date before invoice date" () {
        when:
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss")
        def csvRow =
        List.of(
        paymentBankAccount.id,
        "EXT-0001",
        String.valueOf(invoice.total),
        "USD",
        DateTimeHelper.epochSecondsToLocalDateTime(subscriptionStartDate).format(formatter),
        "Payer name",
        invoiceNumber,
        "Wire",
        "Deposit"
        ).stream().collect(Collectors.joining(","))

        def csvContent = CSV_HEADERS + csvRow

        Response uploadBankTransactionCsvResponse =
        multiPartClient.multiPartFilePost(BANK_TRANSACTION_IMPORT_PATH, csvContent.getBytes(), "BankTransaction", "text/csv")
        Map importData
        if (uploadBankTransactionCsvResponse.successful) {
            importData = MultipartHttpClient.parseMultiPartResponseBody(uploadBankTransactionCsvResponse)
        }
        def query = "SELECT COUNT(*) as row_count from bank_transaction " +
        "WHERE tenant_id = '" + tenantId + "' AND " +
        "bank_account_id = '" + paymentBankAccount.id + "' AND " +
        "potential_invoices IS NOT NULL " +
        "ORDER BY created_on DESC" +
        "LIMIT 1"
        def potentialInvoices = runSqlQuery(client, query).collect(it -> it.potential_invoices)
        then:
        uploadBankTransactionCsvResponse.code() == 200
        importData != null
        importData.bankTransactionsCount == 1
        importData.failedBankTransactionsCount == 0
        importData.successfulBankTransactionsCount == 1
        importData.bankTransactionsUploadData[0].failed == false
        importData.bankTransactionsUploadData[0].failureReason.isEmpty() == true
        potentialInvoices == []
    }

    def "potential invoices listing - no bank account id specified"() {
        when:
        HttpResponse response = client.get(BANK_TRANSACTION_LIST_PATH)
        def query = "SELECT COUNT(*) as row_count from bank_transaction " +
        "WHERE tenant_id = '" + tenantId + "' AND " +
        "potential_invoices IS NOT NULL " +
        "AND potential_invoices != '[]'"

        def rows = runSqlQuery(client, query)

        then:
        response.status==200
        response.body.pageToken==null
        response.body.count>0
        response.body.data.size()>0
        response.body.data.size()==rows[0].row_count
        response.body.count==rows[0].row_count
        response.body.data[0].potentialInvoices.size()>0
    }

    def "potential invoices listing - bank account specified"() {
        when:
        HttpResponse response = client.get("${BANK_TRANSACTION_LIST_PATH}?bankAccountId=${paymentBankAccount.id}")
        def query = "SELECT COUNT(*) as row_count from bank_transaction " +
        "WHERE tenant_id = '" + tenantId + "' AND " +
        "bank_account_id = '" + paymentBankAccount.id + "' AND " +
        "potential_invoices IS NOT NULL " +
        "AND potential_invoices != '[]'"
        def rows = runSqlQuery(client, query)

        then:
        response.status==200
        response.body.pageToken==null
        response.body.count>0
        response.body.data.size()>0
        response.body.data.size()==rows[0].row_count
        response.body.count==rows[0].row_count
        response.body.data[0].potentialInvoices.size()>0
    }

    def "bank transactions - delete a record"() {
        when:
        def queryBefore = "SELECT bank_transaction_id FROM bank_transaction " +
        " WHERE tenant_id = '" + tenantId + "' " +
        " ORDER BY created_on " +
        " LIMIT 1 "
        def rowsBefore = runSqlQuery(client, queryBefore)
        HttpResponse response = client.delete("/bankTransactions/${rowsBefore[0].bank_transaction_id}")
        def queryAfter = "SELECT bank_transaction_id FROM bank_transaction " +
        "  WHERE tenant_id = '" + tenantId + "' " +
        " AND bank_transaction_id = '" + rowsBefore[0].bank_transaction_id + "' "
        def rowsAfter = runSqlQuery(client, queryAfter)

        then:
        response.status==200
        rowsAfter.size()==0
    }

    def "bank transactions - delete a non-existing record  should fail" () {
        when:
        HttpResponse response = client.delete("/bankTransactions/i_do_not_exist")
        then:
        response.status!=200
    }

    def "bank transactions - upload with inactive bank account"() {
        when:
        Response uploadBankTransactionCsvResponse = uploadBankTransactionCsv(BANK_TRANSACTION_CSV_RESOURCE_PATH, createPaymentBankAccount("DEPRECATED"))
        Map importData
        if (uploadBankTransactionCsvResponse.successful) {
            importData = MultipartHttpClient.parseMultiPartResponseBody(uploadBankTransactionCsvResponse)
        }
        then:
        uploadBankTransactionCsvResponse.code() == 200
        importData != null
        importData.bankTransactionsCount == 3
        importData.failedBankTransactionsCount == 3
        importData.successfulBankTransactionsCount == 0
        importData.bankTransactionsUploadData[0].failed == true
        importData.bankTransactionsUploadData[0].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[1].failed == true
        importData.bankTransactionsUploadData[1].failureReason.isEmpty() == false
        importData.bankTransactionsUploadData[2].failed == true
        importData.bankTransactionsUploadData[2].failureReason.isEmpty() == false
    }

    String createPaymentBankAccount(String status = "ACTIVE") {
        return PaymentSpec.createPaymentBankAccount(client, [entityId], status).id
    }

    Response uploadBankTransactionCsv(String csvResourcePath, String paymentBankAccountId) {
        def csvUri = new File(getClass().getResource(csvResourcePath).toURI())
        String csvContent = csvUri.getText()
        if(!paymentBankAccountId.isBlank()) {
            csvContent = csvContent.replace("<addPaymentBankAccountId>", paymentBankAccountId)
        }
        return multiPartClient.multiPartFilePost(BANK_TRANSACTION_IMPORT_PATH, csvContent.getBytes(), csvUri.getName(), "text/csv")
    }

    static def runSqlQuery(HttpClient client, String query) {
        def result = client.post(QUERY_ENDPOINT, query, true)
        def rows = result.body
        return rows
    }

    def "fetch bank transactions by entity ID"() {
        given:
        String entityId = entityId  // Replace with actual entity ID


        when:
        def query = """
           SELECT bank_transaction_id
           FROM bank_transaction
           WHERE entity_id = '${entityId}'
           """
        def transactions = runSqlQuery(client, query)
        def transactionIds = transactions.collect { it.bank_transaction_id }

        and: // Ensure we assign the ID inside `when:`
        bankTransactionId = transactionIds[0]  // Store first retrieved Bank Transaction ID


        then:
        transactionIds.size() > 0  // Ensure at least one record exists
        bankTransactionId != null  // Ensure the variable is correctly assigned
    }

    def "match bank transactions by invoice ID and transaction ID"() {
        when:
        FeatureFlag.updateFlag(client, "PAYMENT_WITH_BANK_ACCOUNT", true)

        def currentPeriodInstant = DateTimeHelper.getZonedDateTime(Instant.now().epochSecond, DateTimeHelper.DEFAULT_TZ)
        .with(TemporalAdjusters.firstDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS)
        AccountingPeriodSpec.specifyAccountingPeriod(client, currentPeriodInstant.toInstant().epochSecond)


        // Create the JSON payload
        Map payload = [
            invoiceID         : invoiceNumber,
            bankTransactionIDs: [bankTransactionId]
        ]

        HttpResponse matchBankTransactionResponse = matchBankTransaction(client, payload)

        then:
        matchBankTransactionResponse.status == 200

        // Ensure response body is a map
        assert matchBankTransactionResponse.body instanceof Map

        // Validate response content
        assert matchBankTransactionResponse.body.invoiceID == invoiceNumber
        assert matchBankTransactionResponse.body.bankTransactionIDs instanceof List
        assert matchBankTransactionResponse.body.bankTransactionIDs.contains(bankTransactionId)
    }

    static HttpResponse matchBankTransaction(HttpClient client, Map payload) {
        return client.post(BANK_TRANSACTION_MATCH_PATH, payload)
    }
}