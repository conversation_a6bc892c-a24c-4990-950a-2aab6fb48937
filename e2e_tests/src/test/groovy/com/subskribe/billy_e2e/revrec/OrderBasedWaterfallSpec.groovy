package com.subskribe.billy_e2e.revrec

import com.subskribe.billy_e2e.CsvUtils
import com.subskribe.billy_e2e.DateTimeHelper
import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.accounting.AccountingPeriodSpec
import com.subskribe.billy_e2e.accounting.AccountingTestUtility
import com.subskribe.billy_e2e.graphql.GqlSpec
import com.subskribe.billy_e2e.graphql.RecognitionRuleGqlSpec
import com.subskribe.billy_e2e.platformfeature.PlatformFeatureSpec
import com.subskribe.billy_e2e.utils.FeatureFlag
import com.subskribe.billy_e2e.utils.Recurrence
import com.subskribe.billy_e2e.utils.Role
import groovy.json.JsonSlurper
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class OrderBasedWaterfallSpec extends GqlSpec {

    private static final String TEST_CASES_PATH = "/revrec/Order_RevRec_Test_Cases.json"
    public static final String WATERFALL_REPORT_API = "/revrec/waterfall"
    private static final String EXPECTED_RESOURCE_PATH = "/revrec/Order_RevRec_Waterfall_Expected.csv"

    @Shared
    protected TimeZone tenantTimeZone

    def setupSpec() {
        FeatureFlag.updateFlag(client, "REV_REC_INVOICE", false)
    }

    def "run test cases"() {
        when:
        Map testCasesInput = readJsonResourceFile(TEST_CASES_PATH) as Map
        Map testCaseDefaults = testCasesInput.defaults as Map
        String currentAccountingPeriodDateInput = testCaseDefaults.currentAccountingPeriodDate
        Long currentAccountingPeriodDate = DateTimeHelper.parseToInstant(currentAccountingPeriodDateInput)
        AccountingTestUtility.performAccountingSetup(client, currentAccountingPeriodDate)
        PlatformFeatureSpec.enablePlatformFeature(getUserClient(Role.ADMIN), PlatformFeatureSpec.ACCOUNTING_FEATURE_NAME)
        PlatformFeatureSpec.enablePlatformFeature(getUserClient(Role.ADMIN), PlatformFeatureSpec.REVENUE_RECOGNITION_FEATURE_NAME)

        for (def testCase: testCasesInput.testCases) {
            testCase = testCaseDefaults + testCase
            Map order = generateOrderForTestCase(client, testCase)
            String orderId = order.id as String
            RecognitionScheduleSpec.generateRecognitionSchedule(client, RecognitionScheduleSpec.TRANSACTION_TYPE_ORDER, orderId)
            verifyRevScheduleForOrder(client, order, testCase)
        }

        then:
        verifyWaterfallReport(client)
    }

    static readJsonResourceFile(String resourcePath) {
        def fileUri = RevenueRampTestRunnerSpec.getResource(resourcePath).toURI()
        String jsonText = new File(fileUri).getText()
        return (new JsonSlurper()).parseText(jsonText)
    }

    static Map generateOrderForTestCase(HttpClient client, Map testCase) {
        Integer numberOfYears = testCase.numberOfYears
        String executionDateInput = testCase.executionDate
        Long executionDate = DateTimeHelper.parseToInstant(executionDateInput)
        String startDateInput = testCase.startDate
        Long startDate = DateTimeHelper.parseToInstant(startDateInput)
        def chargeOverrides = [:]
        def ruleOverrides = [
            source: RecognitionRuleGqlSpec.ORDER_SOURCE,
            recognitionType   : RecognitionRuleGqlSpec.OVER_TIME_RECOGNITION_TYPE,
            distributionMethod: RecognitionRuleGqlSpec.PARTIAL_PRORATED_DISTRIBUTION,
        ]
        def orderOverrides = [
            startDate: startDate,
            termLength: Recurrence.getRecurrenceCycle("YEAR", numberOfYears),
            executedOn: executionDate,
        ]
        def accountOverrides = [
            name: testCase.name,
        ]
        def order = RevenueWaterfallSpec.executeOrderForRevRule(client, chargeOverrides, ruleOverrides, orderOverrides, null, accountOverrides)
        assert order.id
        return order
    }

    static boolean verifyRevScheduleForOrder(HttpClient client, Map order, Map testCase) {
        def revTransactions = getRevTransactionsByOrderId(client, order.id)
        return verifyRevTransactions(client, order.lineItems, revTransactions, testCase)
    }

    static List<Map> getRevTransactionsByOrderId(HttpClient client, String orderId) {
        String path = "/revrec/test/transactionsByOrder/${orderId}"
        return client.get(path).body
    }

    // use the format of Order_RevRec_Test_Cases.json
    // verify expectedLineAmounts as per the line items in the order
    // verify expectedRevAmounts as per the revenue transactions in order of accounting period
    // verify scheduleStartDate as per the accounting period of the first revenue transaction
    static boolean verifyRevTransactions(HttpClient client, List lineItems, List revTransactions, Map testCase) {
        def expectedLineAmounts = testCase.expectedLineAmounts
        def expectedRevAmounts = testCase.expectedRevAmounts
        def sumOfRevAmounts = expectedRevAmounts.sum()
        def sumOfLineAmounts = expectedLineAmounts.sum()
        assert lineItems?.size() == expectedLineAmounts.size()
        lineItems.eachWithIndex{
            lineItem, i -> {
                assert lineItem.amount == expectedLineAmounts[i]
            }
        }
        assert revTransactions?.size() == expectedRevAmounts.size()
        revTransactions.eachWithIndex {
            revTransaction, i -> {
                assert revTransaction.amount == expectedRevAmounts[i]
                assert revTransaction.scheduleId == revTransactions[0].scheduleId
            }
        }
        assert sumOfRevAmounts == sumOfLineAmounts
        // verify first accounting period
        def firstRevTransaction = revTransactions[0]
        def accountingPeriodId = firstRevTransaction.scheduledAccountingPeriodId
        def firstAccountingPeriod = AccountingPeriodSpec.getAccountingPeriodById(client, accountingPeriodId)
        assert testCase.scheduleStartDate == firstAccountingPeriod.startDate
        return true
    }

    static boolean verifyWaterfallReport(HttpClient client) {
        Long startDate = DateTimeHelper.parseToInstant("2023-01-15T00:00:00")
        Long endDate = DateTimeHelper.parseToInstant("2024-02-15T00:00:00")
        List<List<String>> expectedReport = CsvUtils.readCsvResourceRaw(EXPECTED_RESOURCE_PATH)
        HttpResponse downloadResponse = client.get(WATERFALL_REPORT_API + "?startDate=${startDate}&endDate=${endDate}")
        assert downloadResponse.status == 200
        def actualReport = CsvUtils.extractCsvRecordsFromHttpResponse(downloadResponse, [skipHeader: false])
        assert actualReport.size() == expectedReport.size()
        for (int row = 0; row < actualReport.size(); row++) {
            for (int column = 0; column < actualReport[row].size(); column++) {
                if (expectedReport[row][column] == "Any") {
                    continue
                }
                assert actualReport[row][column] == expectedReport[row][column]
            }
        }
        return true
    }

}
