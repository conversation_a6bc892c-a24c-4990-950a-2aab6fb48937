package com.subskribe.billy_e2e.template

import com.subskribe.billy_e2e.EnumType
import com.subskribe.billy_e2e.entity.EntityBaseGqlSpec
import com.subskribe.billy_e2e.utils.Authenticated
import spock.lang.Shared
import spock.lang.Stepwise

@Stepwise
class DocumentMasterTemplateEntitySpec extends Authenticated {

    private static final String MASTER_TEMPLATE_PATH = "/documentTemplates"
    public static final String MASTER_TEMPLATE_TEST_PATH = MASTER_TEMPLATE_PATH + "/test"
    private static final String TEMPLATE_CONTENT = "<h1>template header</h1>"

    @Shared
    String defaultEntityId

    @Shared
    String newEntityId

    // selected: [*]; input: null -> [*]
    def "create master template with required fields only"() {
        when:
        def defaultEntity = EntityBaseGqlSpec.getInitialEntity(client)
        defaultEntityId = defaultEntity.id
        def masterTemplate = [
            name: "document template",
            content: TEMPLATE_CONTENT,
            type: "ORDER",
        ]
        def createdMasterTemplate = OrderFormTemplateSpec.createMasterDocumentTemplate(client, masterTemplate)

        then:
        createdMasterTemplate.id
        createdMasterTemplate.entityIds
        createdMasterTemplate.entityIds.size() == 1
        createdMasterTemplate.entityIds[0] == "*"
        createdMasterTemplate.name == masterTemplate.name
        createdMasterTemplate.content == masterTemplate.content
        createdMasterTemplate.type == masterTemplate.type
        createdMasterTemplate.status == "DRAFT"
        createdMasterTemplate.isFullHtml == true
        createdMasterTemplate.isDefault == false
    }

    // selected: [*]; input: [*] -> [*]
    def "try to create master template with full access"() {
        when:
        def masterTemplate = [
            name: "document template",
            content: TEMPLATE_CONTENT,
            type: "ORDER",
            entityIds: ["*"]
        ]
        def createdMasterTemplate = OrderFormTemplateSpec.createMasterDocumentTemplate(client, masterTemplate)

        then:
        createdMasterTemplate.id
        createdMasterTemplate.entityIds
        createdMasterTemplate.entityIds.size() == 1
        createdMasterTemplate.entityIds[0] == "*"
        createdMasterTemplate.name == masterTemplate.name
        createdMasterTemplate.content == masterTemplate.content
        createdMasterTemplate.type == masterTemplate.type
        createdMasterTemplate.status == "DRAFT"
        createdMasterTemplate.isFullHtml == true
        createdMasterTemplate.isDefault == false
    }

    def "create new entity and switch api key"() {
        when:
        def newEntity = EntityBaseGqlSpec.createAndSwitchEntity(client, [
            name: "entity 2",
            displayId: "ENT-2",
            invoiceConfig: [
                prefix: "INV-E2-",
                scheme: new EnumType("SEQUENCE"),
                length: 6,
                    nextSequenceNumber: 1,
            ],
        ])
        newEntityId = newEntity.id

        then:
        newEntityId
        newEntityId != defaultEntityId
    }

    // selected: [*]; input: [invalid,ent 1] -> error
    def "try to create master template with invalid entity ids"() {
        when:
        def masterTemplate = [
            name: "document template",
            content: TEMPLATE_CONTENT,
            type: "ORDER",
            entityIds: ["invalid", "ent 1"]
        ]
        def createResponse = client.post(MASTER_TEMPLATE_TEST_PATH, masterTemplate)

        then:
        createResponse.status == 403
    }


    // selected: [ent 2]; input: null -> [ent 2]
    def "create master template without entity"() {
        when:
        def masterTemplate = [
            name: "document template",
            content: TEMPLATE_CONTENT,
            type: "ORDER",
        ]
        def createdMasterTemplate = OrderFormTemplateSpec.createMasterDocumentTemplate(client, masterTemplate)

        then:
        createdMasterTemplate.id
        createdMasterTemplate.entityIds
        createdMasterTemplate.entityIds.size() == 1
        createdMasterTemplate.entityIds[0] == newEntityId
    }

    // selected: [ent 2]; input: [ent 1] -> error
    def "try to create master template with default entity id"() {
        when:
        def masterTemplate = [
            name: "document template",
            content: TEMPLATE_CONTENT,
            type: "ORDER",
            entityIds: [defaultEntityId]
        ]
        def createResponse = client.post(MASTER_TEMPLATE_TEST_PATH, masterTemplate)

        then:
        createResponse.status == 403
    }

    // selected: [ent 2]; input: [*] -> error
    def "try to create master template with all entities"() {
        when:
        def masterTemplate = [
            name: "document template",
            content: TEMPLATE_CONTENT,
            type: "ORDER",
            entityIds: ["*"]
        ]
        def createResponse = client.post(MASTER_TEMPLATE_TEST_PATH, masterTemplate)

        then:
        createResponse.status == 403
    }
}
