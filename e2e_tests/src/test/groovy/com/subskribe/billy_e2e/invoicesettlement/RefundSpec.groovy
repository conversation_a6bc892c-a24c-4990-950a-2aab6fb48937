package com.subskribe.billy_e2e.invoicesettlement

import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.account.AccountSpec
import com.subskribe.billy_e2e.accounting.AccountingPeriodSpec
import com.subskribe.billy_e2e.productcatalog.PlanSpec
import com.subskribe.billy_e2e.utils.Authenticated
import com.subskribe.billy_e2e.utils.EventControl
import java.time.Instant
import spock.lang.Ignore
import spock.lang.Shared
import spock.lang.Stepwise
import spock.lang.Unroll

@Stepwise
class RefundSpec extends Authenticated {

    static final String REFUND_PATH = "/refunds"
    static final BigDecimal DEFAULT_AMOUNT = new BigDecimal("1000")

    private static final int NUM_REFUNDS = 10

    @Shared
    private String creditMemoNumber

    @Shared
    private String accountId

    @Shared
    private List<Map> refunds = new ArrayList<>()

    @Shared
    private String paymentId

    def setupSpec() {
    }

    def "add new payment"() {
        when:
        def amount = new BigDecimal("100.********")
        def accountData = AccountSpec.createAccountWithPaymentMethod(client)
        def paymentApplicationsResponse = PaymentApplicationSpec.createNewSettlementApplication(client)
        paymentId = paymentApplicationsResponse.body.paymentId

        then:
        paymentApplicationsResponse.status == 200
    }

    def "add new credit memo"() {
        when:
        Map account = AccountSpec.createAndGetCustomerAccount(client).body
        accountId = account.id
        def plan = PlanSpec.createPlanWithOneTimeCharge(client)
        HttpResponse response = CreditMemoSpec.addStandaloneCreditMemo(client, accountId, plan.charges[0].id, DEFAULT_AMOUNT)
        creditMemoNumber = response.locationId

        then:
        response.status == 201
    }

    def "Post credit memo"() {
        when:
        def response = client.post(CreditMemoSpec.postCreditMemoPath(creditMemoNumber), [:])
        creditMemoNumber = response.body.creditMemoNumber

        then:
        response.status == 200
    }

    def "Create refund with too large amount fails"() {
        when:
        def refundRequest = getCreateRefundJson(creditMemoNumber, paymentId, DEFAULT_AMOUNT.plus(BigDecimal.ONE), "")
        def response = client.post(REFUND_PATH, refundRequest)

        then:
        response.status == 400
        response.error.contains("Refund amount cannot be more than credit memo balance amount")
    }

    def "Create refund with negative amount fails"() {
        when:
        def refundRequest = getCreateRefundJson(creditMemoNumber, paymentId, BigDecimal.valueOf(-1), "")
        def response = client.post(REFUND_PATH, refundRequest)

        then:
        response.status == 400
        response.error.contains("Refund must be greater than 0, received -1")
    }

    def "Create refund with too long notes fails"() {
        when:
        def refundRequest = getCreateRefundJson(creditMemoNumber, paymentId, DEFAULT_AMOUNT, "a" * 1025)
        def response = client.post(REFUND_PATH, refundRequest)

        then:
        response.status == 400
        response.error.contains("field: notes")
    }

    def "Create refund with fake credit memo number fails"() {
        when:
        def refundRequest = getCreateRefundJson("CM-FAKE", paymentId, DEFAULT_AMOUNT, "")
        def response = client.post(REFUND_PATH, refundRequest)

        then:
        response.status == 404
        response.error.contains("does not exist")
    }

    def "Create refund with fake payment id fails"() {
        when:
        def refundRequest = getCreateRefundJson(creditMemoNumber, UUID.randomUUID().toString(), DEFAULT_AMOUNT, "")
        def response = client.post(REFUND_PATH, refundRequest)

        then:
        response.status == 404
        response.error.contains("does not exist")
    }

    def "Create refund with date in closed accounting period fails"() {
        when:
        AccountingPeriodSpec.specifyAccountingPeriod(client)
        def firstAccountingPeriod = AccountingPeriodSpec.getCurrentAccountingPeriod(client)
        def firstAccountingPeriodStart = Instant.parse(firstAccountingPeriod.startDate as String).getEpochSecond()

        def refundRequest = getCreateRefundJson(creditMemoNumber, paymentId, BigDecimal.valueOf(10), "", firstAccountingPeriodStart - 1)
        def response = client.post(REFUND_PATH, refundRequest)

        then:
        response.status == 409
    }

    @Unroll
    def 'Create refunds'() {
        when:
        def refundRequest = getCreateRefundJson(creditMemoNumber, paymentId, BigDecimal.valueOf(10), "")
        def response = client.post(REFUND_PATH, refundRequest)
        def refundId = response.locationId
        refunds.add(client.get("${REFUND_PATH}/${refundId}?accountId=${accountId}").body)

        then:
        response.status == 201

        where:
        i << (0..<NUM_REFUNDS)
    }

    def "List refunds from account"() {
        when:
        def response = client.get("${REFUND_PATH}?accountId=${accountId}")

        then:
        response.status == 200
        response.body.size() == NUM_REFUNDS
        for (i in 0..<NUM_REFUNDS) {
            compareRefunds(refunds[i], response.body[i] as Map)
        }
    }

    def "List refunds from account and credit memo number"() {
        when:
        def response = client.get("${REFUND_PATH}?accountId=${accountId}&creditMemoNumber=${creditMemoNumber}")

        then:
        response.status == 200
        response.body.size() == NUM_REFUNDS
        for (i in 0..<NUM_REFUNDS) {
            compareRefunds(refunds[i], response.body[i] as Map) == true
        }
    }

    def "Get refund by id"() {
        when:
        def response = client.get("${REFUND_PATH}/${refunds[0].refundId}?accountId=${accountId}")

        then:
        response.status == 200
        compareRefunds(refunds[0], response.body as Map) == true
    }

    @Ignore
    def "Check refund event"() {
        when:
        def events = EventControl.getEvents(client, "REFUND_GENERATED")

        then:
        events.size() == NUM_REFUNDS
        events.last().payload.id == refunds[0].refundId
    }

    def "Check credit memo balance"() {
        when:
        HttpResponse getCreditMemoBalanceResponse = CreditMemoSpec.getCreditMemoBalance(client, creditMemoNumber)

        then:
        getCreditMemoBalanceResponse.status == 200
        getCreditMemoBalanceResponse.body.balance == DEFAULT_AMOUNT.subtract(BigDecimal.valueOf(10) * NUM_REFUNDS)
    }

    def compareRefunds(Map refund1, Map refund2) {
        assert refund1.id == refund2.id
        assert refund1.refundId == refund2.refundId
        assert refund1.creditMemoNumber == refund2.creditMemoNumber
        assert refund1.amount == refund2.amount
        assert refund1.paymentId == refund2.paymentId
        assert refund1.paymentMethodType == refund2.paymentMethodType
        assert refund1.createdBy == refund2.createdBy
        assert refund1.notes == refund2.notes
        assert refund1.createdOn == refund2.createdOn
        assert refund1.updatedOn == refund2.updatedOn
        return true
    }

    static Map getCreateRefundJson(String creditMemoNumber, String paymentId, BigDecimal amount, String notes, long refundDate = Instant.now().getEpochSecond()) {
        return [
            creditMemoNumber : creditMemoNumber,
            paymentId        : paymentId,
            amount           : amount,
            paymentMethodType: "MANUAL",
            createdBy        : "E2E testing",
            notes            : notes,
            refundDate       : refundDate
        ]
    }
}
