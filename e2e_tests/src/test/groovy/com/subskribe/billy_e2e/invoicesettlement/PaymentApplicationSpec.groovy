package com.subskribe.billy_e2e.invoicesettlement

import com.subskribe.billy_e2e.HttpClient
import com.subskribe.billy_e2e.HttpResponse
import com.subskribe.billy_e2e.PaymentSpec
import com.subskribe.billy_e2e.account.AccountSpec
import com.subskribe.billy_e2e.entity.EntitySpec
import com.subskribe.billy_e2e.invoice.InvoiceBalanceSpec
import com.subskribe.billy_e2e.invoice.MixedChargesInvoiceSpec
import com.subskribe.billy_e2e.utils.FeatureFlag
import groovyx.gpars.GParsPool
import java.util.concurrent.atomic.AtomicInteger
import spock.lang.Stepwise

@Stepwise
class PaymentApplicationSpec extends BaseSettlementApplicationSpec {

    def "add and apply payment and get application"() {
        when:
        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, invoiceTotal, client, [entityId])
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)
        String settleApplicationId = addAndApplyPaymentResponse.locationId
        HttpResponse getPaymentApplicationsResponse = client.get("${SETTLEMENT_PATH}/${settleApplicationId}")
        HttpResponse getApplicationsByInvoiceNumberResponse = client.get("${SETTLEMENT_PATH}?invoiceNumber=${invoiceNumber}")
        HttpResponse getApplicationsByPaymentIdResponse = client.get("${SETTLEMENT_PATH}?paymentId=${getPaymentApplicationsResponse.body.paymentId}")
        HttpResponse getApplicationsWithoutParamsResponse = client.get("${SETTLEMENT_PATH}?invoiceNumber=")

        then:
        addAndApplyPaymentResponse.status == 201
        getPaymentApplicationsResponse.status == 200
        getApplicationsByInvoiceNumberResponse.status == 200
        getApplicationsByPaymentIdResponse.status == 200
        getApplicationsWithoutParamsResponse.status == 400
        getPaymentApplicationsResponse.body.amount == invoice.total
        getApplicationsByInvoiceNumberResponse.body.size() == 1
        getApplicationsByInvoiceNumberResponse.body.get(0).id == settleApplicationId
        getApplicationsByPaymentIdResponse.body.size() == 1
        getApplicationsByPaymentIdResponse.body.get(0).id == settleApplicationId
    }

    def "add and apply payment and get balances"() {
        when:
        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, invoiceTotal, client, [entityId])
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)
        String settleApplicationId = addAndApplyPaymentResponse.locationId
        HttpResponse getPaymentApplicationsResponse = client.get("${SETTLEMENT_PATH}/${settleApplicationId}")
        HttpResponse getInvoiceBalanceResponse = InvoiceBalanceSpec.getInvoiceBalance(client, invoiceNumber)
        String paymentId = getPaymentApplicationsResponse.body.paymentId
        HttpResponse getPaymentBalanceResponse = PaymentSpec.getPaymentBalance(client, paymentId)

        then:
        addAndApplyPaymentResponse.status == 201
        getPaymentApplicationsResponse.status == 200
        getInvoiceBalanceResponse.body.balance == BigDecimal.ZERO
        getPaymentBalanceResponse.body.balance == BigDecimal.ZERO
    }

    def "add and apply partial payment and get balances"() {
        when:
        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        BigDecimal amountApplied = new BigDecimal(invoiceTotal).subtract(BigDecimal.ONE)
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, amountApplied, client, [entityId])
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)
        String settleApplicationId = addAndApplyPaymentResponse.locationId
        HttpResponse getPaymentApplicationsResponse = client.get("${SETTLEMENT_PATH}/${settleApplicationId}")
        String paymentId = getPaymentApplicationsResponse.body.paymentId
        HttpResponse getInvoiceBalanceResponse = InvoiceBalanceSpec.getInvoiceBalance(client, invoiceNumber)
        HttpResponse getPaymentBalanceResponse = PaymentSpec.getPaymentBalance(client, paymentId)

        then:
        addAndApplyPaymentResponse.status == 201
        getPaymentApplicationsResponse.status == 200
        getInvoiceBalanceResponse.body.balance == BigDecimal.ONE
        getPaymentBalanceResponse.body.balance == BigDecimal.ZERO
    }

    def "add and apply payment more than invoice amount returns 400"() {
        when:
        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        BigDecimal amountApplied = new BigDecimal(invoiceTotal).add(BigDecimal.ONE)
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, amountApplied, client, [entityId])
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)

        then:
        addAndApplyPaymentResponse.status == 400
    }

    def "add and apply concurrent payments"() {
        when:
        def totalRequests = 20
        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        String invoiceTotal = invoice.total

        AtomicInteger successfulRequests = new AtomicInteger(0)

        GParsPool.withPool(5) {
            (1..totalRequests).eachParallel {
                def response = addAndApplyPayment(invoiceNumber, paymentMethodId)
                if (response.status == 201) {
                    successfulRequests.incrementAndGet()
                }
            }
        }

        def getInvoiceBalanceResponse = InvoiceBalanceSpec.getInvoiceBalance(client, invoiceNumber)
        def getPaymentApplicationsResponse = client.get("${SETTLEMENT_PATH}?invoiceNumber=${invoiceNumber}")
        def expectedBalance = new BigDecimal(invoiceTotal).subtract(BigDecimal.valueOf(successfulRequests.get()))

        then:
        getInvoiceBalanceResponse.body.balance == expectedBalance
        getPaymentApplicationsResponse.body.size() == successfulRequests
    }

    def "add and apply payment with bank Fee more than payment amount fails"() {
        when:

        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, invoiceTotal, client, [entityId])
        addAndApplyPaymentJson.bankFee = invoiceTotal.plus(BigDecimal.TEN)
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)

        then:
        addAndApplyPaymentResponse.status == 400
        addAndApplyPaymentResponse.error.contains("Bank fee cannot be greater than payment amount.")
    }

    def "add and apply payment with bank Fee less than 0 fails"() {
        when:

        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, invoiceTotal, client, [entityId])
        addAndApplyPaymentJson.bankFee = BigDecimal.valueOf(-1)
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)

        then:
        addAndApplyPaymentResponse.status == 400
        addAndApplyPaymentResponse.error.contains("Bank fee cannot be negative.")
    }

    def "add and apply payment with bank account id not found fails"() {
        when:

        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, invoiceTotal, client, [entityId])
        addAndApplyPaymentJson.paymentBankAccountId = "123"
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)

        then:
        addAndApplyPaymentResponse.status == 409
        addAndApplyPaymentResponse.error.contains("Payment bank account with id 123 was not found or unique")
    }

    def "add and apply payment with bank account not belonging to entity fails"() {
        when:

        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        HttpResponse entity = EntitySpec.createEntity(client, "Japan", "INV-JP-", "JP", "CALENDAR_DAYS", "NORMALIZED", "US/Pacific", "JPY", "Please enter your bank details here.")
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, invoiceTotal, client, [entityId])
        def paymentBankAccount = PaymentSpec.createPaymentBankAccount(client, [entity.body.entityId])
        addAndApplyPaymentJson.paymentBankAccountId = paymentBankAccount.id
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)

        then:
        addAndApplyPaymentResponse.status == 409
        addAndApplyPaymentResponse.error.contains("Payment bank account with id")
        addAndApplyPaymentResponse.error.contains("was not found or unique")
    }

    private addAndApplyPayment(String invoiceNumber, String paymentMethodId) {
        HttpResponse getBalanceResponse = client.get("${MixedChargesInvoiceSpec.INVOICE_PATH}/${invoiceNumber}/balance")
        BigDecimal invoiceBalance = getBalanceResponse.body.balance
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceBalance, paymentMethodId, BigDecimal.ONE, client, [entityId])
        return client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)
    }

    static def getAddAndApplyPaymentJson(String invoiceNumber, BigDecimal invoiceBalance, String paymentMethodId, BigDecimal paymentAmount, HttpClient client, List entityIds) {
        FeatureFlag.updateFlag(client, "PAYMENT_WITH_BANK_ACCOUNT", true)
        def paymentBankAccount = PaymentSpec.createPaymentBankAccount(client, entityIds)
        return [
            invoiceNumber: invoiceNumber,
            invoiceAmount: invoiceBalance,
            paymentMethodId    : paymentMethodId,
            amount             : paymentAmount,
            note         : "test add and apply payment application",
            bankFee : paymentAmount - BigDecimal.ONE,
            paymentBankAccountId: paymentBankAccount.id
        ]
    }

    static HttpResponse addPaymentToInvoice(HttpClient client, String invoiceNumber, String paymentMethodId, BigDecimal invoiceBalance, BigDecimal paymentAmount, String entityId) {
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceBalance, paymentMethodId, paymentAmount, client, [entityId])
        return client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)
    }

    static HttpResponse getSettlementApplication(HttpClient client, String settlementApplicationId) {
        return client.get("${SETTLEMENT_PATH}/${settlementApplicationId}")
    }

    static def createNewSettlementApplication(HttpClient client) {
        def invoice = getPostedInvoice(client)
        String accountId = invoice.accountId
        String paymentMethodId = AccountSpec.addPaymentMethodToAccount(client, accountId).locationId
        String invoiceNumber = invoice.invoiceNumber
        BigDecimal invoiceTotal = invoice.total
        def getEntityResponse = client.get(EntitySpec.ENTITY_PATH)
        Map entity = getEntityResponse.body[0]
        Map addAndApplyPaymentJson = getAddAndApplyPaymentJson(invoiceNumber, invoiceTotal, paymentMethodId, invoiceTotal, client, [entity.entityId])
        HttpResponse addAndApplyPaymentResponse = client.post("${SETTLEMENT_PATH}/addAndApplyPayment", addAndApplyPaymentJson)
        String settleApplicationId = addAndApplyPaymentResponse.locationId
        return client.get("${SETTLEMENT_PATH}/${settleApplicationId}")
    }
}
