package com.subskribe.billy_e2e

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import java.nio.charset.StandardCharsets

@CompileStatic
class HttpClient {

    private static final String CONTENT_TYPE = "Content-Type"

    private String baseUrl

    protected Map<String, String> headers = new HashMap<String, String>()

    HttpClient(String baseUrl) {
        this.baseUrl = baseUrl
        headers.put(CONTENT_TYPE, "application/json")
    }

    def addHeader(String name, String value) {
        headers.put(name, value)
    }

    private HttpURLConnection createConnection(String urlPath, String method) {
        HttpURLConnection conn = (HttpURLConnection) new URL(baseUrl + urlPath).openConnection()
        conn.setRequestMethod(method)
        conn.setDoOutput(true)
        for (Map.Entry<String, String> header : headers.entrySet()) {
            conn.setRequestProperty(header.getKey(), header.getValue())
        }
        return conn
    }

    HttpResponse put(String urlPath, def data) {
        def conn = createConnection(urlPath, "PUT")
        String json = JsonOutput.toJson(data)
        conn.getOutputStream().write(json.getBytes(StandardCharsets.UTF_8.name()))
        return new HttpResponse(conn)
    }

    HttpResponse putRawText(String urlPath, String data) {
        def conn = createConnection(urlPath, "PUT")
        conn.setRequestProperty(CONTENT_TYPE, "text/plain")
        conn.getOutputStream().write(data.getBytes(StandardCharsets.UTF_8.name()))
        return new HttpResponse(conn)
    }

    HttpResponse putNoBody(String urlPath) {
        HttpURLConnection conn = (HttpURLConnection) new URL(baseUrl + urlPath).openConnection()
        conn.setRequestMethod("PUT")
        conn.setDoOutput(true)
        for (Map.Entry<String, String> header : headers.entrySet()) {
            if (header.key == CONTENT_TYPE) {
                continue
            }
            conn.setRequestProperty(header.getKey(), header.getValue())
        }
        return new HttpResponse(conn)
    }

    HttpResponse post(String urlPath, def data, boolean skipSerialization = false) {
        def conn = createConnection(urlPath, "POST")
        String json = (skipSerialization) ? data : JsonOutput.toJson(data)
        conn.getOutputStream().write(json.getBytes(StandardCharsets.UTF_8.name()))
        return new HttpResponse(conn)
    }

    HttpResponse postWithHeaders(String urlPath, def data, Map<String, String> customHeaders) {
        def conn = createConnection(urlPath, "POST")
        for (Map.Entry<String, String> header : customHeaders.entrySet()) {
            conn.addRequestProperty(header.getKey(), header.getValue())
        }
        String json = JsonOutput.toJson(data)
        conn.getOutputStream().write(json.getBytes(StandardCharsets.UTF_8.name()))
        return new HttpResponse(conn)
    }

    HttpResponse postWithParams(String urlPath, Map params, def data = null, boolean skipSerialization = false) {
        urlPath += QueryParamBuilder.buildQueryParam(params)
        return post(urlPath, data, skipSerialization)
    }

    Map postJsonAndParse(String urlPath, def data, boolean skipSerialization = false) {
        def response = post(urlPath, data, skipSerialization)
        return parseResponse(response)
    }

    Map postFormAndParse(String urlPath, Map data) {
        def conn = createConnection(urlPath, "POST")
        def queryParams = QueryParamBuilder.buildQueryParamWithoutPrefix(data)
        byte[] postData       = queryParams.getBytes(StandardCharsets.UTF_8)
        int    postDataLength = postData.length
        conn.setUseCaches(false)
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
        conn.setRequestProperty("charset", "utf-8")
        conn.setRequestProperty("Content-Length", Integer.toString(postDataLength))
        try(DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
            wr.write(postData)
        }
        def response = new HttpResponse(conn)
        return parseResponse(response)
    }

    static Map parseResponse(HttpResponse httpResponse) {
        assert httpResponse.conn.inputStream
        def output = (new JsonSlurper()).parse(httpResponse.conn.inputStream)
        return output as Map
    }

    HttpResponse get(String urlPath) {
        def conn = createConnection(urlPath, "GET")
        return new HttpResponse(conn)
    }

    HttpResponse getWithHeaders(String urlPath, Map<String, String> customHeaders) {
        def conn = createConnection(urlPath, "GET")
        for (Map.Entry<String, String> header : customHeaders.entrySet()) {
            conn.addRequestProperty(header.getKey(), header.getValue())
        }
        return new HttpResponse(conn)
    }

    HttpResponse getWithParams(String urlPath, Map params) {
        urlPath += QueryParamBuilder.buildQueryParam(params)
        def conn = createConnection(urlPath, "GET")
        return new HttpResponse(conn)
    }

    HttpResponse delete(String urlPath) {
        def conn = createConnection(urlPath, "DELETE")
        return new HttpResponse(conn)
    }

    HttpResponse delete(String urlPath, def data) {
        def conn = createConnection(urlPath, "DELETE")
        String json = JsonOutput.toJson(data)
        conn.getOutputStream().write(json.getBytes(StandardCharsets.UTF_8.name()))
        return new HttpResponse(conn)
    }

    HttpResponse postMultipart(String urlPath, Map<String, Object> formData) {
        String boundary = "----WebKitFormBoundary" + System.currentTimeMillis()

        HttpURLConnection conn = (HttpURLConnection) new URL(baseUrl + urlPath).openConnection()
        conn.setRequestMethod("POST")
        conn.setDoOutput(true)
        conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)

        for (Map.Entry<String, String> header : headers.entrySet()) {
            if (header.key != CONTENT_TYPE) {
                conn.setRequestProperty(header.getKey(), header.getValue())
            }
        }

        try (DataOutputStream out = new DataOutputStream(conn.getOutputStream())) {
            for (Map.Entry<String, Object> entry : formData.entrySet()) {
                String fieldName = entry.getKey()
                Object value = entry.getValue()

                out.writeBytes("--" + boundary + "\r\n")

                if (value instanceof byte[]) {
                    out.writeBytes("Content-Disposition: form-data; name=\"" + fieldName + "\"; filename=\"upload\"\r\n")
                    out.writeBytes("Content-Type: application/octet-stream\r\n\r\n")
                    out.write((byte[]) value)
                    out.writeBytes("\r\n")
                } else {
                    out.writeBytes("Content-Disposition: form-data; name=\"" + fieldName + "\"\r\n\r\n")
                    out.writeBytes(value.toString())
                    out.writeBytes("\r\n")
                }
            }

            out.writeBytes("--" + boundary + "--\r\n")
            out.flush()
        }

        return new HttpResponse(conn)
    }
}
