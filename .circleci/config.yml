# CircleCI Java language guide: https://circleci.com/docs/2.0/language-java/?section=examples-and-guides
# A quick overview
# jobs creates individual jobs with user defined series of steps to run
# workflows define the order of execution of the jobs

version: 2.1


orbs:
  aws-ecr: circleci/aws-ecr@9.1.0
  slack: circleci/slack@4.13.1


parameters:
  devops_tag:
    type: string
    default: 8a913635-cci

  dmz_ecr_domain:
    type: string
    default: 054887729006.dkr.ecr.us-east-2.amazonaws.com

  github_rw_key_fingerprint:
    type: string
    default: "4f:75:1d:33:77:eb:ae:cc:39:02:a6:d2:d6:0a:5f:c2"

executors:
  openjdk:
    docker:
      - image: cimg/openjdk:17.0.6

  openjdk-node:
    docker:
      - image: cimg/openjdk:17.0-node

  devops-container:
    docker:
      - image: <<pipeline.parameters.dmz_ecr_domain>>/dev/billy-devops:<<pipeline.parameters.devops_tag>>
        aws_auth:
          aws_access_key_id: $DMZ_PULL_AWS_ACCESS_KEY_ID
          aws_secret_access_key: $DMZ_PULL_AWS_SECRET_ACCESS_KEY

  devops-container-root:
    docker:
      - image: <<pipeline.parameters.dmz_ecr_domain>>/dev/billy-devops:<<pipeline.parameters.devops_tag>>
        user: root
        aws_auth:
          aws_access_key_id: $DMZ_PULL_AWS_ACCESS_KEY_ID
          aws_secret_access_key: $DMZ_PULL_AWS_SECRET_ACCESS_KEY


commands:
  prevent_pipeline_fail_on_step_fail:
    description: prevent pipeline from aborting if any previous step has failed
    steps:
      - run:
          name: Prevent pipeline from aborting if any previous step has failed
          shell: /bin/bash # so the pipeline will be marked as failed but other jobs will still run
          when: on_fail
          command: |
            # mark the job as failed (has to be last line):
            false

  configure_git_push:
    description: configure the job to be able to push tags to github
    parameters:
      key_fingerprint:
        type: string
    steps:
      - add_ssh_keys:
          fingerprints:
            - << parameters.key_fingerprint >>
      - run:
          name: Configure git
          command: |            
            git config user.email "<EMAIL>"
            git config user.name "Subskribe Infra"

  notify_master_build_failure:
    description: send notification to Slack channel when a build-related job has failed on master branch
    steps:
      - slack/notify:
          event: fail
          template: basic_fail_1
          branch_pattern: "master,release/.*"
          channel: build-notifications

  notify_master_security_scan:
    description: send notification to Slack channel when a security-scan completes on master branch
    steps:
      - slack/notify:
          event: fail
          template: basic_fail_1
          branch_pattern: master
          channel: security-notifications

  notify_release_cut:
    description: send notification to Slack channel when a new release has been cut
    steps:
      - slack/notify:
          event: fail
          template: basic_fail_1
          branch_pattern: master
          channel: 'qa'
      - slack/notify:
          event: pass
          template: basic_success_1
          branch_pattern: master
          channel: 'qa'

  notify_master_deployment_status:
    description: send notification to Slack channel for deployment job on master branch
    steps:
      - slack/notify:
          event: fail
          template: basic_fail_1
          branch_pattern: "master,release/.*"
          channel: 'deployment-notifications,deployment-failures'
      - slack/notify:
          event: pass
          template: basic_success_1
          branch_pattern: "master,release/.*"
          channel: deployment-notifications

  notify_deployment_validation_failure:
    description: send notification to Slack channel for deployment validation failures
    steps:
      - slack/notify:
          event: fail
          template: basic_fail_1
          branch_pattern: master
          channel: 'deployment-notifications,deployment-failures'

  notify_release_build_success:
    description: send notification to Slack channel on successful build of release branch
    steps:
      - run:
          name: fetch and export slack username
          command: |
            echo ci username: ${CIRCLE_USERNAME}
            echo ci branch: ${CIRCLE_BRANCH}
            YQ_SELECTOR=".users[] | select(.github == \"${CIRCLE_USERNAME}\") | .slack"
            SLACK_USERNAME=$(yq "$YQ_SELECTOR" $SUBSKRIBE_CONFIG_FILE)
            echo slack username: $SLACK_USERNAME
            if [[ -z "$SLACK_USERNAME" ]]; then
              echo slack username not found. skipping sending notification
              echo infra config file: $SUBSKRIBE_CONFIG_FILE
              circleci-agent step halt
            fi
            BRANCH_URL=https://app.circleci.com/pipelines/github/Subskribe/billy?branch=${CIRCLE_BRANCH}
            echo release branch url: $BRANCH_URL
            echo "export SLACK_USERNAME=$SLACK_USERNAME" >> "$BASH_ENV"
            echo "export BRANCH_URL=$BRANCH_URL" >> "$BASH_ENV"
      - slack/notify:
          channel: 'build-notifications'
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "<@${SLACK_USERNAME}>, your branch *${CIRCLE_BRANCH}* is built. <${BRANCH_URL}|click to deploy>"
                  }
                }
              ]
            }

jobs:
  native-build:
    executor: openjdk
    steps:
      - checkout
      - setup_remote_docker:
          version: default
      - restore_cache:
          keys:
            - native-{{ checksum "pom.xml" }}
      - run:
          name: Install build-time packages
          command: |
            echo "install build time packages................."
            sudo ./infra/docker/install-buildtime-packages.sh
      - run:
          name: Generate and set versions
          command: |
            echo ${CIRCLE_SHA1::8} > billy-app/src/main/resources/version.txt
            ./infra/set-maven-version.sh
      - run:
          name: Authenticate
          command: |
            export AWS_ACCESS_KEY_ID=${IMG_PULLER_AWS_ACCESS_KEY_ID}
            export AWS_SECRET_ACCESS_KEY=${IMG_PULLER_AWS_SECRET_ACCESS_KEY}
            export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain subskribe-domain --domain-owner 054887729006 --region us-east-2 --query authorizationToken --output text`
            echo "export CODEARTIFACT_AUTH_TOKEN='${CODEARTIFACT_AUTH_TOKEN}'" >> "$BASH_ENV"
      - run:
          name: Build billy jar
          command: |
            mvn -s ./infra/maven_settings.xml -Pnoverify package deploy
      - run:
          name: Build tenant lambda jar
          command: |
            pushd infra/tenant-lambda-executor
            mvn package
            popd
      - run:
          name: Copy jars to workspace
          command: |
            mkdir -p /tmp/workspace
            find billy-app/target -name "billy-app-1.0.*-SNAPSHOT.jar" -exec cp '{}' /tmp/workspace/billy-app-1.0-SNAPSHOT.jar \;
            find infra/tenant-lambda-executor/target -name "tenant-lambda-executor-1.0.*-SNAPSHOT.jar" -exec cp '{}' /tmp/workspace/tenant-lambda-executor-1.0-SNAPSHOT.jar \;
            echo "Files in the workspace:"
            ls /tmp/workspace
      - run:
          # So we can get clean cache keys
          name: reset version
          command: |
            ./infra/reset-maven-version.sh
      - save_cache:
          paths:
            - ~/.m2 # mvn dependencies
          key: native-{{ checksum "pom.xml" }}
      - persist_to_workspace:
          root: /tmp/workspace
          paths:
            - billy-app-1.0-SNAPSHOT.jar
            - tenant-lambda-executor-1.0-SNAPSHOT.jar
      - notify_master_build_failure

  native-test:
    executor: openjdk
    steps:
      - checkout
      - setup_remote_docker:
          version: default
      - restore_cache:
          keys:
            - native-{{ checksum "pom.xml" }}
      - run:
          name: Install build-time packages
          command: |
            echo "install build time packages................."
            sudo ./infra/docker/install-buildtime-packages.sh
      - run:
          name: Set versions
          command: |
            ./infra/set-maven-version.sh
      - run:
          name: Authenticate
          command: |
            export AWS_ACCESS_KEY_ID=${IMG_PULLER_AWS_ACCESS_KEY_ID}
            export AWS_SECRET_ACCESS_KEY=${IMG_PULLER_AWS_SECRET_ACCESS_KEY}
            export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain subskribe-domain --domain-owner 054887729006 --region us-east-2 --query authorizationToken --output text`
            echo "export CODEARTIFACT_AUTH_TOKEN='${CODEARTIFACT_AUTH_TOKEN}'" >> "$BASH_ENV"
      - run:
          name: Run test
          command: |
            mvn -s ./infra/maven_settings.xml test
      - save_cache:
          paths:
            - ~/.m2 # mvn dependencies
          key: native-{{ checksum "pom.xml" }}
      - attach_workspace:
          at: /tmp/workspace
      - store_test_results:
          path: billy-app/target/surefire-reports
      - store_artifacts:
          path: billy-app/target/coverage-reports/index.html
      - run:
          name: Package coverage reports
          command: zip -r billy-app/target/coverage-reports.zip billy-app/target/coverage-reports
      - store_artifacts:
          path: billy-app/target/coverage-reports.zip
      - notify_master_build_failure

  native-static-analysis:
    executor: openjdk-node
    steps:
      - checkout
      - setup_remote_docker:
          version: default
      - restore_cache:
          keys:
            - static-checks-{{ checksum "pom.xml" }}-{{ checksum "e2e_tests/pom.xml" }}
            - native-{{ checksum "pom.xml" }}
      - run:
          name: Install build-time packages
          command: |
            echo "install build time packages................."
            sudo ./infra/docker/install-buildtime-packages.sh
      - run:
          name: Set versions
          command: |
            ./infra/set-maven-version.sh
      - run:
          name: Authenticate
          command: |
            export AWS_ACCESS_KEY_ID=${IMG_PULLER_AWS_ACCESS_KEY_ID}
            export AWS_SECRET_ACCESS_KEY=${IMG_PULLER_AWS_SECRET_ACCESS_KEY}
            export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain subskribe-domain --domain-owner 054887729006 --region us-east-2 --query authorizationToken --output text`
            echo "export CODEARTIFACT_AUTH_TOKEN='${CODEARTIFACT_AUTH_TOKEN}'" >> "$BASH_ENV"
      - run:
          name: Compile
          command: |
            mvn compile
      - run:
          name: Run spotless check
          command: |
            mvn spotless:check
      - run:
          name: Run PMD check
          when: always
          command: |
            mvn pmd:check
      - store_artifacts:
          path: /home/<USER>/project/billy-app/target/site/pmd.html
      - run:
          name: Run Spotbugs check
          when: always
          command: |
            mvn spotbugs:check
      - store_artifacts:
          path: /home/<USER>/project/billy-app/target/site/spotbugs.html
      - run:
          name: Run e2e spotless check
          when: always
          command: |
            cd e2e_tests
            mvn spotless:check
      - save_cache:
          paths:
            - ~/.m2
          key: static-checks-{{ checksum "pom.xml" }}-{{ checksum "e2e_tests/pom.xml" }}
      - notify_master_build_failure

  build-diff-infra:
    executor: devops-container
    steps:
      - setup_remote_docker:
          docker_layer_caching: true
          version: default
      - attach_workspace:
          at: /tmp/workspace
      - checkout
      - run:
          name: Copy lambda jar
          command: |
            mkdir -p ./infra/tenant-lambda-executor/target
            cp /tmp/workspace/tenant-lambda-executor-1.0-SNAPSHOT.jar ./infra/tenant-lambda-executor/target/tenant-lambda-executor-1.0-SNAPSHOT.jar
      - run:
          name: check if infra code changed
          command: ./infra/billy/has_module_changed.sh infra
      - restore_cache:
          keys:
            - billy-infra-{{ checksum "infra/cdk/pom.xml" }}
      - run:
          name: Build, Test and Package
          command: |            
            cd infra/cdk && mvn -s ../../infra/maven_settings.xml package
      - run:
          name: Run devops1 diff
          command: |
            cd infra/cdk && cdk diff "billy-infra/devops1/*"
      - run:
          name: Run dev2 diff
          command: |
            cd infra/cdk && cdk diff "billy-infra/dev2/*"
      - run:
          name: Run qa01 diff
          command: |
            cd infra/cdk && cdk diff "billy-infra/qa01/*"
      - run:
          name: Run sandbox diff
          command: |
            cd infra/cdk && cdk diff "billy-infra/sandbox/*"
      - run:
          name: Run prod diff
          command: |
            cd infra/cdk && cdk diff "billy-infra/prod/*"
      - save_cache:
          paths:
            - ~/.m2 # mvn dependencies
          key: billy-infra-{{ checksum "infra/cdk/pom.xml" }}
      - notify_master_build_failure

  docker-build:
    executor: devops-container-root
    steps:
      - setup_remote_docker:
          docker_layer_caching: true
          version: default
      - checkout
      - attach_workspace:
          at: /tmp/workspace
      - run:
          name: Setup job env (set PATH, build tag, etc)
          command: |
            cci setup-env prod
      - run:
          name: Copy previously built jar
          command: |
            ls /tmp/workspace/
            mkdir -p ./billy-app/target
            cp /tmp/workspace/billy-app-1.0-SNAPSHOT.jar ./billy-app/target/billy-app-1.0-SNAPSHOT.jar
            ls ./billy-app/target
      - run:
          name: Create docker image for billy
          command: |
            export DOCKER_BUILDKIT=1
            cci build-with-logs --app billy --target build-deps --tag "${LOCAL_TAG}" --git-sha-short "${GIT_SHA_SHORT}"
            docker build . --target prod --tag billy:${LOCAL_TAG} --build-arg git_sha_short=${GIT_SHA_SHORT} --progress=plain
            cci save-built-image-to-workspace billy
      - persist_to_workspace:
          root: /tmp/workspace
          paths:
            - billy-prod.tar
      - notify_master_build_failure

  security-scan-source-code:
    executor: devops-container
    steps:
      - checkout
      - run:
          name: Scan billy source code
          command: |
            export AWS_ACCESS_KEY_ID=${IMG_PULLER_AWS_ACCESS_KEY_ID}
            export AWS_SECRET_ACCESS_KEY=${IMG_PULLER_AWS_SECRET_ACCESS_KEY}
            export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain subskribe-domain --domain-owner 054887729006 --region us-east-2 --query authorizationToken --output text`
            snyk test --severity-threshold=critical
      - notify_master_security_scan

  security-scan-docker-image-ecr:
    executor: devops-container
    environment:
      REPO_DOMAIN: << pipeline.parameters.dmz_ecr_domain >>/dev
    steps:
      - setup_remote_docker:
          docker_layer_caching: true
          version: default
      - checkout
      - run:
          name: Scan billy docker image with Snyk
          command: |
            GIT_SHA_SHORT=${CIRCLE_SHA1::8}
            export IMG_PUSHER_AWS_ACCESS_KEY_ID=${DMZ_PULL_AWS_ACCESS_KEY_ID}
            export IMG_PUSHER_AWS_SECRET_ACCESS_KEY=${DMZ_PULL_AWS_SECRET_ACCESS_KEY}
            snyk-test-ecr-image --app billy --git-sha-short "${GIT_SHA_SHORT}" --repo-domain "${REPO_DOMAIN}" --snyk-policy-file "$(pwd)/.snyk"
      - notify_master_security_scan

  security-scan-gotenberg:
    executor: devops-container
    steps:
      - setup_remote_docker:
          docker_layer_caching: true
          version: default
      - checkout
      - run:
          name: Scan gotenberg docker image with Snyk
          command: |
            GOTENBERG_VERSION="8.9.0"
            docker pull gotenberg/gotenberg:${GOTENBERG_VERSION}
            snyk test --docker gotenberg/gotenberg:${GOTENBERG_VERSION} --severity-threshold=critical --policy-path="$(pwd)/.snyk.gotenberg"
      - notify_master_security_scan

  docker-run-e2e-tests:
    parameters:
      run_salesforce:
        type: boolean
        default: false
      run_hubspot:
        type: boolean
        default: false
    executor: openjdk
    resource_class: large
    environment:
      BILLY_ENV: ci
      RUN_SALESFORCE: << parameters.run_salesforce >>
      RUN_HUBSPOT: << parameters.run_hubspot >>
    steps:
      - run:
          name: Check if e2e tests should be skipped
          command: |
            SKIP_BRANCH_REGEX=".*/skip-e2e-tests/.*"
            if [[ $CIRCLE_BRANCH =~ $SKIP_BRANCH_REGEX ]]; then
              echo "Current branch $CIRCLE_BRANCH matches the skip pattern, will not run e2e tests"
              circleci-agent step halt
            fi
      - attach_workspace:
          at: /tmp/workspace
      - setup_remote_docker:
          docker_layer_caching: true
          version: default
      - checkout
      - when:
          condition:
            and:
              - << parameters.run_salesforce >>
              - not:
                  equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
          steps:
            - run:
                name: check if salesforce code changed
                command: ./infra/billy/has_module_changed.sh salesforce
      - when:
          condition:
            and:
              - << parameters.run_hubspot >>
              - not:
                  equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
          steps:
            - run:
                name: check if hubspot code changed
                command: ./infra/billy/has_module_changed.sh hubspot
      - restore_cache:
          keys:
            - e2e-maven-{{ checksum "pom.xml" }}
      - run:
          name: Import image generated by previous job
          command: |
            docker load -i "/tmp/workspace/billy-prod.tar"
      - run:
          name: Docker compose up on billy and dependencies
          command: |
            echo "AWS Access key: " ${AWS_ACCESS_KEY_ID}
            echo "AWS Access secret: " ${AWS_SECRET_ACCESS_KEY}
            echo "Billy stage name: ${BILLY_ENV}"
            export BILLY_TAG="prod-${CIRCLE_SHA1::8}" # used by docker compose
            docker compose -f docker-compose.yml -f e2e_tests/docker-compose.yml up -d
            docker compose -f docker-compose.yml -f e2e_tests/docker-compose.yml ps
            echo "All the containers should be up and running"
            docker ps
      - run:
          name: Run e2e test
          command: |
            cd e2e_tests
            ssh -N -L 127.0.0.1:8080:127.0.0.1:8080 -L 127.0.0.1:4566:127.0.0.1:4566 remote-docker &
            mvn compile test-compile
            timeout 90 bash -c 'while [[ "$(curl -s -o /dev/null -w ''%{http_code}'' localhost:8080/health)" != "200" ]]; do sleep 2; done' || false
            mvn test -Dspock.runner.runSalesforce=${RUN_SALESFORCE} -Dspock.runner.runHubSpot=${RUN_HUBSPOT}
          no_output_timeout: 20m
      - save_cache:
          paths:
            - ~/.m2 # mvn dependencies
          key: e2e-maven-{{ checksum "pom.xml" }}
      - run:
          name: Show billy container logs
          command: |
            cd ${CIRCLE_WORKING_DIRECTORY}
            docker compose -f docker-compose.yml -f e2e_tests/docker-compose.yml logs billy
          when: always
      - store_test_results:
          path: e2e_tests/target/surefire-reports
      - run:
          name: Package test results
          command: zip -r e2e_tests/target/test-results.zip e2e_tests/target/surefire-reports
      - store_artifacts:
          path: e2e_tests/target/test-results.zip
      - when:
          condition:
            and:
              - matches:
                  pattern: ".*/db-dump/.*"
                  value: << pipeline.git.branch >>
          steps:
            - run:
                name: Dump database contents
                when: always
                command: |
                  ssh -N -L 127.0.0.1:5432:127.0.0.1:5432 remote-docker &
                  export PGPASSWORD=password
                  pg_dump -h localhost -U billy billy-db >> e2e.sql
                  zip e2e-sql.zip e2e.sql
            - store_artifacts:
                name: Store database contents
                path: e2e-sql.zip
      - when:
          condition:
            equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
          steps:
            - notify_master_build_failure

  push-image:
    parameters:
      image_tag_prefix:
        type: string
        default: prod
      repo_type:
        type: string
    environment:
      REPO_DOMAIN: << pipeline.parameters.dmz_ecr_domain >>/<< parameters.repo_type >>
      IMAGE_TAG_PREFIX: << parameters.image_tag_prefix >>
    executor: devops-container
    steps:
      - setup_remote_docker:
          docker_layer_caching: false
          version: default
      - attach_workspace:
          at: /tmp/workspace
      - run:
          name: Setup job env (set PATH, build tag, etc)
          command: |
            cci setup-env ${IMAGE_TAG_PREFIX}
      - run:
          name: Import image generated by previous job
          command: |
            cci import-built-image-from-workspace billy
      - run:
          name: Push << parameters.image_tag_prefix >> image to DMZ << parameters.repo_type >> ECR
          command: |
            docker-login "${REPO_DOMAIN}"
            push-image --name billy --local-tag "${LOCAL_TAG}" --remote-tag-prefix "${GIT_SHA_SHORT}" --repo-domain "${REPO_DOMAIN}"
      - notify_master_build_failure
      - when:
          condition:
            and:
              - equal: [ "release", << parameters.repo_type >> ]
              - matches:
                  pattern: release\/release-.*
                  value: << pipeline.git.branch >>
          steps:
            - notify_release_build_success

  plan-deploy:
    parameters:
      stage_name:
        type: string
    environment:
      STAGE_NAME: << parameters.stage_name >>
      USE_RBAC: true
    executor: devops-container
    steps:
      - checkout
      - run:
          name: Check billy deployment plan for stage << parameters.stage_name >>
          command: |
            deploy-via-helm plan --app billy --stage-name "${STAGE_NAME}"
      - notify_master_build_failure

  deploy:
    parameters:
      stage_name:
        type: string
      tag_release:
        type: boolean
        default: false
    environment:
      STAGE_NAME: << parameters.stage_name >>
      USE_RBAC: true
    executor: devops-container
    steps:
      - checkout # to get the helm chart
      - attach_workspace:
          at: /tmp/workspace
      - restore_cache:
          keys:
            - billy-infra-{{ checksum "infra/cdk/pom.xml" }}
      - run:
          name: Check billy deployment plan for << parameters.stage_name >>
          command: |
            deploy-via-helm plan --app billy --stage-name ${STAGE_NAME}
      - run:
          name: Check if this branch can be deployed
          command: |
            if [ $(git tag -l "allow-rollback" --contains HEAD) ]; then
                echo "This SHA has been tagged with 'allow-rollback', deployment checks will not be run"
            else
                infra-cli billy get-running-version --stage=${STAGE_NAME} --component=backend --check-can-deploy="${CIRCLE_BRANCH}"
            fi
      # Temporary, will replace with downloading the artifact from internal maven repo
      - run:
          name: Create lambda jar
          command: |
            mvn -s ./infra/maven_settings.xml -Pnoverify package -pl infra/tenant-lambda-executor -am
      - run:
          name: Deploy infra changes
          command: |
            cd infra/cdk
            cdk diff "billy-infra/${STAGE_NAME}/*" --fail || export diff_status=$?
            [[ $diff_status -eq 1 ]] && cdk deploy "billy-infra/${STAGE_NAME}/*" --require-approval=never --exclusively || exit 0

      - run:
          name: Deploy billy to << parameters.stage_name >>
          command: |
            deploy-via-helm apply --app billy --stage-name ${STAGE_NAME} --debug
      - notify_master_deployment_status
      - run:
          name: Publish datadog event
          command: |
            dog \
              --api-key "${DATADOG_API_KEY}" \
              --application-key "${DATADOG_APPLICATION_KEY}" \
              event post \
              --tags "env:${STAGE_NAME},app:billy,type:deployment" \
              "billy deployment to ${STAGE_NAME}" \
              "billy has been deployed to ${STAGE_NAME} from CCI job ${CIRCLE_BUILD_URL}"

  cut-release:
    executor: devops-container
    steps:
      - checkout
      - configure_git_push:
          key_fingerprint: << pipeline.parameters.github_rw_key_fingerprint >>
      - run:
          name: Tag release
          # getting the path to the correct key is a bit tricky. CCI puts it in ~/.ssh/id_rsa_{fingerprint without all the: characters} hence the sed 's/://g'
          # then since the golang io doesn't support using simply ~ to get the user path, we're doing $(eval echo ~$USER) here to get the absolute path to it.
          command: |
            ssh_file_suffix=$(echo "<< pipeline.parameters.github_rw_key_fingerprint >>" | sed 's/://g')
            export RW_SSH_KEY_FILE=$(eval echo ~$USER)/.ssh/id_rsa_$ssh_file_suffix
            infra-cli release start-release --commit-sha=${CIRCLE_SHA1} --key-file=${RW_SSH_KEY_FILE}
      - notify_release_cut

  validate:
    parameters:
      stage_name:
        type: string
    environment:
      STAGE_NAME: << parameters.stage_name >>
    executor: devops-container
    steps:
      - run:
          name: Run datadog synthetics for << parameters.stage_name >>
          command: |
            datadog-ci synthetics run-tests \
              -s "tag:env-${STAGE_NAME}" \
              --failOnTimeout \
              --failOnCriticalErrors \
              --apiKey "${DATADOG_API_KEY}" \
              --appKey "${DATADOG_APPLICATION_KEY}"
      - notify_deployment_validation_failure

  readme-api-upload:
    docker:
      - image: cimg/openjdk:17.0.6
    resource_class: small
    steps:
      - attach_workspace:
          at: /tmp/workspace
      - checkout
      - run:
          name: update readme.com with swagger yaml
          command: |
            curl -o /tmp/workspace/swagger.yaml https://api.app.subskribe.com/swagger.yaml
            {
              head -2 /tmp/workspace/swagger.yaml;
              cat billy-app/src/main/resources/swagger-include.yaml;
              tail +3 /tmp/workspace/swagger.yaml;
            } > /tmp/workspace/swagger-combined.yaml
            curl -X PUT -u "$README_API_KEY:" -F spec=@/tmp/workspace/swagger-combined.yaml https://dash.readme.com/api/v1/api-specification/66a067a14c9e3d003a9477dd

workflows:
  version: 2

  auto-deployments:
    when:
      not:
        equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
    jobs:
      - native-build:
          context:
            - slack-secrets
            - cci-dmz-pull
            - build-secrets

      - build-diff-infra:
          context:
            - pull-devops-image
            - cci-dmz-infra
            - slack-secrets
          requires:
            - native-build

      - native-test:
          context:
            - slack-secrets
            - build-secrets
            - cci-dmz-pull
          requires:
            - native-build

      - native-static-analysis:
          context:
            - slack-secrets
            - cci-dmz-pull
            - build-secrets
          requires:
            - native-build

      - docker-build:
          context:
            - pull-devops-image
            - slack-secrets
          requires:
            - native-build

      - docker-run-e2e-tests:
          name: docker-run-e2e-tests
          context:
            - pull-devops-image
            - localstack
            - slack-secrets
            - build-secrets
          requires:
            - docker-build

      - docker-run-e2e-tests:
          name: run-salesforce-tests
          run_salesforce: true
          context:
            - pull-devops-image
            - localstack
            - slack-secrets
            - build-secrets
          requires:
            - docker-build

      - docker-run-e2e-tests:
          name: run-hubspot-tests
          run_hubspot: true
          context:
            - pull-devops-image
            - localstack
            - slack-secrets
            - build-secrets
          requires:
            - docker-build

      - push-image:
          repo_type: dev

          name: push-image-to-non-prod-ecr
          context:
            - pull-devops-image
            - slack-secrets
            - cci-dmz-push-pull
          requires:
            - docker-run-e2e-tests
            - native-test
            - native-static-analysis
            - run-hubspot-tests
            - build-diff-infra
          filters:
            branches:
              # master and any branch that matches a pattern that identifies it for testing deployments via ci/cd
              # per https://circleci.com/docs/2.0/configuration-reference/#branches
              only:
                - master
                - /release/.*/
                - /.*/test-auto-deploy-devops1/.*/
                - /.*/security-scan/.*/

      - push-image:
          repo_type: release

          name: push-image-to-prod-ecr
          context:
            - pull-devops-image
            - slack-secrets
            - cci-dmz-push-pull
          requires:
            - push-image-to-non-prod-ecr
          filters:
            branches:
              only:
                - /release/.*/
                - master

      - plan-deploy:
          stage_name: devops1

          name: plan-deploy-devops1
          context:
            - pull-devops-image
            - cci-dmz-direct
            - slack-secrets
          requires:
            - docker-run-e2e-tests
            - run-hubspot-tests
          filters:
            branches:
              only:
                - /.*/test-auto-deploy-devops1/.*/

      - plan-deploy:
          stage_name: dev2

          name: plan-deploy-dev2
          context:
            - pull-devops-image
            - cci-dmz-direct
            - slack-secrets
          requires:
            - docker-run-e2e-tests
            - run-hubspot-tests
          filters:
            branches:
              only:
                - /.*/test-auto-deploy-devops1/.*/

      - plan-deploy:
          stage_name: qa01

          name: plan-deploy-qa01
          context:
            - pull-devops-image
            - cci-dmz-direct
            - slack-secrets
          requires:
            - docker-run-e2e-tests
            - run-hubspot-tests
          filters:
            branches:
              only:
                - /.*/test-auto-deploy-devops1/.*/

      - plan-deploy:
          stage_name: sandbox

          name: plan-deploy-sandbox
          context:
            - pull-devops-image
            - cci-dmz-direct
            - slack-secrets
          requires:
            - docker-run-e2e-tests
            - run-hubspot-tests
          filters:
            branches:
              only:
                - /.*/test-auto-deploy-devops1/.*/

      - plan-deploy:
          stage_name: prod

          name: plan-deploy-prod
          context:
            - pull-devops-image
            - cci-dmz-direct
            - slack-secrets
          requires:
            - docker-run-e2e-tests
            - run-hubspot-tests
          filters:
            branches:
              only:
                - /.*/test-auto-deploy-devops1/.*/

      - deploy:
          stage_name: dev2

          name: deploy-to-dev2
          context:
            - pull-devops-image
            - cci-dmz-infra
            - slack-secrets
            - datadog-dev
          requires:
            - push-image-to-non-prod-ecr
          filters:
            branches:
              only:
                - master

      - deploy:
          stage_name: devops1

          name: deploy-to-devops1
          context:
            - pull-devops-image
            - cci-dmz-infra
            - slack-secrets
            - datadog-dev
          requires:
            - push-image-to-non-prod-ecr
          filters:
            branches:
              # master and any branch that matches a pattern that identifies it for testing deployments via ci/cd
              # per https://circleci.com/docs/2.0/configuration-reference/#branches
              only:
                - master
                - /.*/test-auto-deploy-devops1/.*/

      - validate:
          stage_name: dev2

          name: validate-dev2
          context:
            - pull-devops-image
            - slack-secrets
            - datadog-dev
          requires:
            - deploy-to-dev2
          filters:
            branches:
              only:
                - master

      - validate:
          stage_name: devops1

          name: validate-devops1
          context:
            - pull-devops-image
            - slack-secrets
            - datadog-dev
          requires:
            - deploy-to-devops1
          filters:
            branches:
              only:
                - master
                - /.*/test-auto-deploy-devops1/.*/

  manual-deployments:
    when:
      not:
        equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
    jobs:

      - approve-release-candidate:
          type: approval
          filters:
            branches:
              only:
                - master

      - cut-release:
          name: cut-release
          context:
            - pull-devops-image
            - slack-secrets
          requires:
            - approve-release-candidate

      - approval-deploy-to-qa01:
          type: approval
          filters:
            branches:
              only:
                - /release/.*/

      - deploy:
          stage_name: qa01
          tag_release: true

          name: deploy-to-qa01
          context:
            - pull-devops-image
            - cci-dmz-infra
            - slack-secrets
            - datadog-dev
          requires:
            - approval-deploy-to-qa01

      # sandbox

      - approval-deploy-to-sandbox:
          type: approval
          filters:
            branches:
              only:
                - /release/.*/

      - deploy:
          stage_name: sandbox

          name: deploy-to-sandbox
          context:
            - pull-devops-image
            - slack-secrets
            - cci-dmz-infra
            - datadog-dev
          requires:
            - approval-deploy-to-sandbox

      # prod

      - approval-deploy-to-prod:
          type: approval
          filters:
            branches:
              only:
                - /release/.*/

      - deploy:
          stage_name: prod

          name: deploy-to-prod
          context:
            - pull-devops-image
            - slack-secrets
            - cci-dmz-infra
            - datadog-prod
          requires:
            - approval-deploy-to-prod

  security-scan:
    when:
      or:
        - and:
          - equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
          - equal: [ "Internal Svc Security Scan", << pipeline.schedule.name >> ]
        - matches:
            pattern: /.*/security-scan/.*/
            value: << pipeline.git.branch >>
    jobs:
      - security-scan-source-code:
          context:
            - pull-devops-image
            - cci-dmz-pull
            - snyk
            - slack-secrets
            - build-secrets

      - security-scan-docker-image-ecr:
          context:
            - pull-devops-image
            - snyk
            - slack-secrets

  security-scan-third-party:
    when:
      or:
        - and:
          - equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
          - equal: [ "External Svc Security Scan", << pipeline.schedule.name >> ]
        - matches:
            pattern: /.*/security-scan/.*/
            value: << pipeline.git.branch >>
    jobs:
      - security-scan-gotenberg:
          context:
            - pull-devops-image
            - snyk
            - slack-secrets

  scheduled-e2e-tests:
    when:
      and:
        - equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
        - equal: [ "scheduled e2e tests", << pipeline.schedule.name >> ]
    jobs:
      - native-build:
          context:
            - slack-secrets
            - build-secrets
            - cci-dmz-pull
      - docker-build:
          context:
            - pull-devops-image
            - slack-secrets
            - build-secrets
          requires:
            - native-build
      - docker-run-e2e-tests:
          name: run-hubspot-tests
          run_hubspot: true
          context:
            - pull-devops-image
            - localstack
            - slack-secrets
            - build-secrets
          requires:
            - docker-build
      - docker-run-e2e-tests:
          name: run-salesforce-tests
          run_salesforce: true
          context:
            - pull-devops-image
            - localstack
            - slack-secrets
            - build-secrets
          requires:
            - docker-build

  scheduled-readme-upload:
    when:
      and:
        - equal: [ scheduled_pipeline, << pipeline.trigger_source >> ]
        - equal: [ "scheduled readme upload", << pipeline.schedule.name >> ]
    jobs:
      - readme-api-upload:
          context:
            - readme-secrets
