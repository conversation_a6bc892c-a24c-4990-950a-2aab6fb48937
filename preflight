#!/bin/bash
echo "✈️      ✈️     ✈️    ✈️    ✈️   ✈️   ✈️  ✈️ "
# try killing billy no matter what at the end
trap 'kill $BILLY_PID' EXIT
set -e

# first clean make
make
# then db migrate
make db_migrate

# start billy
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=0.0.0.0:5005 -jar target/billy-app-1.0-SNAPSHOT.jar server config-local.yml 2>&1 > /tmp/billy_flight.log &
BILLY_PID=$!

echo "========== BILLY STARTED ============="
echo "stdout/stderr go to /tmp/billy_flight.log"
echo "billy process id is:$BILLY_PID"
echo "let billy breathe for 10 seconds ....."
sleep 10

echo "========== E2E TESTS STARTED ============="
cd ./e2e_tests; make

echo "========== REPORT STARTED =============="
cd ..
make site
open ./billy-app/target/site/spotbugs.html
open ./billy-app/target/site/pmd.html
