<?xml version="1.0"?>
<ruleset xmlns="http://pmd.sourceforge.net/ruleset/2.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="Default Maven PMD Plugin Ruleset" xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
    <description>Custom PMD rules for subskribe</description>
    <rule name="ProhibitedImports"
          language="java"
          message="Avoid using classes from these packages"
          class="net.sourceforge.pmd.lang.rule.xpath.XPathRule">
        <description>
            Avoid using these classes, there are better alternatives.
        </description>
        <priority>3</priority>
        <properties>
            <property name="xpath">
                <value>
                    <![CDATA[
//Name[starts-with(@Image, 'org.jetbrains.')] |
//Name[starts-with(@Image, 'io.dropwizard.util.')] |
//Name[starts-with(@Image, 'org.elasticsearch.common.collect.')] |
//Name[starts-with(@Image, 'org.testcontainers.shaded.')] |
//Name[pmd-java:typeIs('org.apache.http.HttpStatus')]
]]>
                </value>
            </property>
        </properties>
    </rule>
</ruleset>
