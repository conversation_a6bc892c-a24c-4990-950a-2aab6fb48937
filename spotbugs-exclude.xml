<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter>
    <Match>
        <Package name="~com.subskribe.billy.jooq.*" />
    </Match>
    <Match>
        <!-- TODO: figure out enabling spotbugs for groovy after investigation -->
        <Package name="~com.subskribe.zeppa.*" />
    </Match>
    <Match>
        <Bug pattern="EI_EXPOSE_REP,EI_EXPOSE_REP2,CT_CONSTRUCTOR_THROW,SE_BAD_FIELD" />
    </Match>
</FindBugsFilter>
